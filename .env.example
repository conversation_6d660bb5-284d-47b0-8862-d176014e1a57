# =============================================================================
# CryptoSentinel Environment Configuration
# =============================================================================

# Application
NODE_ENV=development
PORT=3000
API_PORT=3001
AI_SERVICE_PORT=8000
BLOCKCHAIN_SERVICE_PORT=3002

# Database Configuration
DATABASE_URL=postgresql://cryptosentinel:password@localhost:5432/cryptosentinel
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=cryptosentinel
DATABASE_USER=cryptosentinel
DATABASE_PASSWORD=password

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_SECRET=your-refresh-token-secret
REFRESH_TOKEN_EXPIRES_IN=30d

# Blockchain RPC URLs
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/YOUR_INFURA_KEY
ETHEREUM_TESTNET_RPC_URL=https://goerli.infura.io/v3/YOUR_INFURA_KEY
BSC_RPC_URL=https://bsc-dataseed.binance.org/
BSC_TESTNET_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545/
POLYGON_RPC_URL=https://polygon-rpc.com/
POLYGON_TESTNET_RPC_URL=https://rpc-mumbai.maticvigil.com/
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
SOLANA_TESTNET_RPC_URL=https://api.testnet.solana.com
ARBITRUM_RPC_URL=https://arb1.arbitrum.io/rpc
OPTIMISM_RPC_URL=https://mainnet.optimism.io

# External API Keys
COINGECKO_API_KEY=your_coingecko_api_key
COINMARKETCAP_API_KEY=your_coinmarketcap_api_key
DEXSCREENER_API_KEY=your_dexscreener_api_key
MORALIS_API_KEY=your_moralis_api_key
ALCHEMY_API_KEY=your_alchemy_api_key
INFURA_PROJECT_ID=your_infura_project_id
INFURA_PROJECT_SECRET=your_infura_project_secret

# Social Media APIs
TWITTER_BEARER_TOKEN=your_twitter_bearer_token
TWITTER_API_KEY=your_twitter_api_key
TWITTER_API_SECRET=your_twitter_api_secret
REDDIT_CLIENT_ID=your_reddit_client_id
REDDIT_CLIENT_SECRET=your_reddit_client_secret
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
DISCORD_BOT_TOKEN=your_discord_bot_token

# Developer Platform APIs
GITHUB_TOKEN=your_github_personal_access_token
GITLAB_TOKEN=your_gitlab_access_token

# AI/ML Services (Optional)
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key
HUGGINGFACE_API_KEY=your_huggingface_api_key

# Email Service
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
FROM_EMAIL=<EMAIL>

# File Storage
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=cryptosentinel-storage

# Google Cloud (Optional)
GOOGLE_CLOUD_PROJECT_ID=your_gcp_project_id
GOOGLE_CLOUD_KEY_FILE=path/to/service-account-key.json

# Monitoring & Analytics
SENTRY_DSN=your_sentry_dsn
GOOGLE_ANALYTICS_ID=your_ga_tracking_id
MIXPANEL_TOKEN=your_mixpanel_token

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
CORS_ORIGIN=http://localhost:3000
ALLOWED_ORIGINS=http://localhost:3000,https://cryptosentinel.ai
BCRYPT_ROUNDS=12

# Feature Flags
ENABLE_AI_ANALYSIS=true
ENABLE_SOCIAL_SENTIMENT=true
ENABLE_DEVELOPER_TRACKING=true
ENABLE_REAL_TIME_ALERTS=true
ENABLE_PREMIUM_FEATURES=false

# Development
DEBUG=cryptosentinel:*
LOG_LEVEL=info
ENABLE_SWAGGER=true
ENABLE_PLAYGROUND=true

# Testing
TEST_DATABASE_URL=postgresql://cryptosentinel:password@localhost:5432/cryptosentinel_test
TEST_REDIS_URL=redis://localhost:6379/1

# Deployment
DEPLOYMENT_ENV=development
HEALTH_CHECK_INTERVAL=30000
GRACEFUL_SHUTDOWN_TIMEOUT=10000
