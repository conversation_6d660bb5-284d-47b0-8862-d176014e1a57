"""
CryptoSentinel AI Service
AI-powered token risk assessment and analysis service
"""

import asyncio
import logging
import os
from contextlib import asynccontextmanager
from typing import Dict, Any

import uvicorn
from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from prometheus_client import Counter, Histogram, generate_latest, CONTENT_TYPE_LATEST
import structlog

# Import custom modules
from src.models.risk_assessment import RiskAssessmentModel
from src.analyzers.contract_analyzer import ContractAnalyzer
from src.analyzers.social_analyzer import <PERSON>Analyzer
from src.analyzers.developer_analyzer import DeveloperAnalyzer
from src.utils.blockchain_client import BlockchainClient
from src.utils.cache import CacheManager
from src.config.settings import get_settings
from src.schemas.requests import TokenAnalysisRequest, SentimentAnalysisRequest
from src.schemas.responses import RiskAssessmentResponse, SentimentAnalysisResponse

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Prometheus metrics
REQUEST_COUNT = Counter('ai_service_requests_total', 'Total requests', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('ai_service_request_duration_seconds', 'Request duration')
ANALYSIS_COUNT = Counter('token_analysis_total', 'Total token analyses', ['result'])

# Global instances
settings = get_settings()
cache_manager = CacheManager()
blockchain_client = BlockchainClient()
risk_model = RiskAssessmentModel()
contract_analyzer = ContractAnalyzer()
social_analyzer = SocialAnalyzer()
developer_analyzer = DeveloperAnalyzer()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting CryptoSentinel AI Service")
    
    # Initialize components
    await cache_manager.connect()
    await blockchain_client.initialize()
    await risk_model.load_models()
    
    logger.info("AI Service initialized successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down AI Service")
    await cache_manager.disconnect()
    await blockchain_client.close()

# Create FastAPI app
app = FastAPI(
    title="CryptoSentinel AI Service",
    description="AI-powered token risk assessment and analysis service",
    version="1.0.0",
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
    lifespan=lifespan
)

# Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "ai-service",
        "version": "1.0.0",
        "timestamp": asyncio.get_event_loop().time()
    }

# Metrics endpoint
@app.get("/metrics")
async def metrics():
    """Prometheus metrics endpoint"""
    return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)

# Token Risk Analysis Endpoint
@app.post("/analyze/token", response_model=RiskAssessmentResponse)
async def analyze_token(
    request: TokenAnalysisRequest,
    background_tasks: BackgroundTasks
):
    """
    Analyze a token for risk assessment
    
    This endpoint performs comprehensive token analysis including:
    - Smart contract analysis for scam patterns
    - Social sentiment analysis
    - Developer reputation assessment
    - Liquidity and holder distribution analysis
    """
    REQUEST_COUNT.labels(method="POST", endpoint="/analyze/token").inc()
    
    with REQUEST_DURATION.time():
        try:
            logger.info("Starting token analysis", token_address=request.token_address)
            
            # Check cache first
            cache_key = f"token_analysis:{request.token_address}:{request.chain}"
            cached_result = await cache_manager.get(cache_key)
            
            if cached_result and not request.force_refresh:
                logger.info("Returning cached result", token_address=request.token_address)
                return RiskAssessmentResponse(**cached_result)
            
            # Perform analysis
            analysis_tasks = []
            
            # Contract analysis
            contract_task = contract_analyzer.analyze_contract(
                request.token_address, 
                request.chain
            )
            analysis_tasks.append(contract_task)
            
            # Social sentiment analysis
            if request.include_social:
                social_task = social_analyzer.analyze_sentiment(
                    request.token_address,
                    request.token_symbol
                )
                analysis_tasks.append(social_task)
            
            # Developer analysis
            if request.include_developer:
                dev_task = developer_analyzer.analyze_developers(
                    request.token_address,
                    request.chain
                )
                analysis_tasks.append(dev_task)
            
            # Execute all analyses concurrently
            results = await asyncio.gather(*analysis_tasks, return_exceptions=True)
            
            # Process results
            contract_analysis = results[0] if not isinstance(results[0], Exception) else None
            social_analysis = results[1] if len(results) > 1 and not isinstance(results[1], Exception) else None
            developer_analysis = results[2] if len(results) > 2 and not isinstance(results[2], Exception) else None
            
            # Calculate risk score
            risk_score = await risk_model.calculate_risk_score(
                contract_analysis=contract_analysis,
                social_analysis=social_analysis,
                developer_analysis=developer_analysis
            )
            
            # Create response
            response = RiskAssessmentResponse(
                token_address=request.token_address,
                chain=request.chain,
                risk_score=risk_score.score,
                risk_level=risk_score.level,
                confidence=risk_score.confidence,
                factors=risk_score.factors,
                contract_analysis=contract_analysis,
                social_analysis=social_analysis,
                developer_analysis=developer_analysis,
                timestamp=asyncio.get_event_loop().time()
            )
            
            # Cache result
            background_tasks.add_task(
                cache_manager.set,
                cache_key,
                response.dict(),
                expire=3600  # 1 hour
            )
            
            # Update metrics
            ANALYSIS_COUNT.labels(result=risk_score.level).inc()
            
            logger.info(
                "Token analysis completed",
                token_address=request.token_address,
                risk_score=risk_score.score,
                risk_level=risk_score.level
            )
            
            return response
            
        except Exception as e:
            logger.error("Token analysis failed", error=str(e), token_address=request.token_address)
            raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

# Sentiment Analysis Endpoint
@app.post("/analyze/sentiment", response_model=SentimentAnalysisResponse)
async def analyze_sentiment(request: SentimentAnalysisRequest):
    """
    Analyze social sentiment for a token or project
    """
    REQUEST_COUNT.labels(method="POST", endpoint="/analyze/sentiment").inc()
    
    try:
        logger.info("Starting sentiment analysis", query=request.query)
        
        # Check cache
        cache_key = f"sentiment:{hash(request.query)}:{request.timeframe}"
        cached_result = await cache_manager.get(cache_key)
        
        if cached_result:
            return SentimentAnalysisResponse(**cached_result)
        
        # Perform sentiment analysis
        sentiment_data = await social_analyzer.analyze_sentiment_detailed(
            query=request.query,
            timeframe=request.timeframe,
            sources=request.sources
        )
        
        response = SentimentAnalysisResponse(
            query=request.query,
            sentiment_score=sentiment_data.score,
            sentiment_label=sentiment_data.label,
            confidence=sentiment_data.confidence,
            sources=sentiment_data.sources,
            mentions_count=sentiment_data.mentions_count,
            trending_score=sentiment_data.trending_score,
            timestamp=asyncio.get_event_loop().time()
        )
        
        # Cache result
        await cache_manager.set(cache_key, response.dict(), expire=1800)  # 30 minutes
        
        return response
        
    except Exception as e:
        logger.error("Sentiment analysis failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Sentiment analysis failed: {str(e)}")

# Batch Analysis Endpoint
@app.post("/analyze/batch")
async def analyze_batch(requests: list[TokenAnalysisRequest]):
    """
    Analyze multiple tokens in batch
    """
    if len(requests) > 10:
        raise HTTPException(status_code=400, detail="Maximum 10 tokens per batch")
    
    tasks = [analyze_token(req, BackgroundTasks()) for req in requests]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    return {
        "results": [
            result if not isinstance(result, Exception) else {"error": str(result)}
            for result in results
        ]
    }

# Error handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    logger.error("HTTP exception", status_code=exc.status_code, detail=exc.detail)
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.detail, "status_code": exc.status_code}
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    logger.error("Unhandled exception", error=str(exc))
    return JSONResponse(
        status_code=500,
        content={"error": "Internal server error", "status_code": 500}
    )

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=int(os.getenv("PORT", 8000)),
        reload=settings.debug,
        log_level="info"
    )
