# Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP Client
httpx==0.25.2
aiohttp==3.9.1
requests==2.31.0

# Database
asyncpg==0.29.0
databases[postgresql]==0.8.0
sqlalchemy==2.0.23
alembic==1.13.0

# Redis
redis==5.0.1
aioredis==2.0.1

# Machine Learning
scikit-learn==1.3.2
numpy==1.25.2
pandas==2.1.4
scipy==1.11.4
joblib==1.3.2

# Natural Language Processing
nltk==3.8.1
textblob==0.17.1
vaderSentiment==3.3.2
transformers==4.36.0
torch==2.1.1
sentence-transformers==2.2.2

# Web Scraping
beautifulsoup4==4.12.2
scrapy==2.11.0
selenium==4.15.2
playwright==1.40.0

# Blockchain
web3==6.13.0
eth-account==0.10.0
eth-utils==2.3.0
solana==0.30.2
solders==0.18.1

# Data Processing
python-dateutil==2.8.2
pytz==2023.3
python-dotenv==1.0.0
pyyaml==6.0.1
toml==0.10.2

# Cryptography
cryptography==41.0.8
hashlib-compat==1.0.1

# Monitoring & Logging
structlog==23.2.0
sentry-sdk[fastapi]==1.38.0
prometheus-client==0.19.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2
faker==20.1.0

# Development
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0

# API Documentation
fastapi-users==12.1.2
python-multipart==0.0.6

# Background Tasks
celery==5.3.4
kombu==5.3.4

# Image Processing
pillow==10.1.0
opencv-python==********

# Mathematical Operations
sympy==1.12
statsmodels==0.14.0

# Time Series Analysis
prophet==1.1.5
plotly==5.17.0

# Feature Engineering
feature-engine==1.6.2
category-encoders==2.6.3

# Model Deployment
onnx==1.15.0
onnxruntime==1.16.3

# Configuration Management
hydra-core==1.3.2
omegaconf==2.3.0

# Utilities
tqdm==4.66.1
click==8.1.7
rich==13.7.0
typer==0.9.0
