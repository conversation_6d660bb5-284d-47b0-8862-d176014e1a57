"""
CryptoSentinel Risk Assessment Algorithm
Custom risk calculation algorithm for token analysis
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class RiskLevel(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class RiskFactor:
    name: str
    score: float  # 0-100
    weight: float  # 0-1
    category: str
    description: str
    severity: RiskLevel

@dataclass
class RiskAssessment:
    overall_score: float
    risk_level: RiskLevel
    confidence: float
    factors: List[RiskFactor]
    recommendations: List[str]

class TokenRiskCalculator:
    """
    Advanced token risk assessment algorithm using multiple factors
    """
    
    def __init__(self):
        self.weights = {
            'contract_security': 0.30,
            'liquidity': 0.20,
            'social_sentiment': 0.15,
            'developer_reputation': 0.15,
            'market_metrics': 0.10,
            'holder_distribution': 0.10
        }
        
        # Risk thresholds
        self.risk_thresholds = {
            RiskLevel.LOW: (0, 25),
            RiskLevel.MEDIUM: (25, 50),
            RiskLevel.HIGH: (50, 75),
            RiskLevel.CRITICAL: (75, 100)
        }
    
    def calculate_risk(self, 
                      contract_analysis: Dict,
                      social_analysis: Dict,
                      developer_analysis: Dict,
                      market_data: Dict,
                      liquidity_data: Dict) -> RiskAssessment:
        """
        Calculate comprehensive risk assessment
        """
        try:
            factors = []
            
            # Contract Security Analysis
            contract_factors = self._analyze_contract_security(contract_analysis)
            factors.extend(contract_factors)
            
            # Liquidity Analysis
            liquidity_factors = self._analyze_liquidity(liquidity_data)
            factors.extend(liquidity_factors)
            
            # Social Sentiment Analysis
            social_factors = self._analyze_social_sentiment(social_analysis)
            factors.extend(social_factors)
            
            # Developer Reputation Analysis
            dev_factors = self._analyze_developer_reputation(developer_analysis)
            factors.extend(dev_factors)
            
            # Market Metrics Analysis
            market_factors = self._analyze_market_metrics(market_data)
            factors.extend(market_factors)
            
            # Calculate weighted score
            overall_score = self._calculate_weighted_score(factors)
            
            # Determine risk level
            risk_level = self._determine_risk_level(overall_score)
            
            # Calculate confidence
            confidence = self._calculate_confidence(factors)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(factors, risk_level)
            
            return RiskAssessment(
                overall_score=overall_score,
                risk_level=risk_level,
                confidence=confidence,
                factors=factors,
                recommendations=recommendations
            )
            
        except Exception as e:
            logger.error(f"Risk calculation failed: {str(e)}")
            raise
    
    def _analyze_contract_security(self, contract_data: Dict) -> List[RiskFactor]:
        """Analyze smart contract security factors"""
        factors = []
        
        if not contract_data:
            return factors
        
        # Honeypot detection
        if contract_data.get('is_honeypot', False):
            factors.append(RiskFactor(
                name="Honeypot Detected",
                score=95,
                weight=0.4,
                category="contract_security",
                description="Contract appears to be a honeypot - tokens cannot be sold",
                severity=RiskLevel.CRITICAL
            ))
        
        # Mint function analysis
        if contract_data.get('has_mint_function', False):
            mint_score = 70 if contract_data.get('mint_restricted', False) else 90
            factors.append(RiskFactor(
                name="Mint Function",
                score=mint_score,
                weight=0.3,
                category="contract_security",
                description="Contract has mint function - supply can be increased",
                severity=RiskLevel.HIGH if mint_score > 80 else RiskLevel.MEDIUM
            ))
        
        # Ownership analysis
        if contract_data.get('has_owner', False):
            if contract_data.get('ownership_renounced', False):
                owner_score = 20
                severity = RiskLevel.LOW
                desc = "Contract ownership has been renounced"
            else:
                owner_score = 60
                severity = RiskLevel.MEDIUM
                desc = "Contract has an active owner with privileges"
            
            factors.append(RiskFactor(
                name="Contract Ownership",
                score=owner_score,
                weight=0.25,
                category="contract_security",
                description=desc,
                severity=severity
            ))
        
        # Pause function
        if contract_data.get('has_pause_function', False):
            factors.append(RiskFactor(
                name="Pause Function",
                score=65,
                weight=0.2,
                category="contract_security",
                description="Contract can be paused by owner",
                severity=RiskLevel.MEDIUM
            ))
        
        # Blacklist functionality
        if contract_data.get('has_blacklist', False):
            factors.append(RiskFactor(
                name="Blacklist Function",
                score=70,
                weight=0.25,
                category="contract_security",
                description="Contract can blacklist addresses",
                severity=RiskLevel.HIGH
            ))
        
        # Code verification
        if not contract_data.get('is_verified', True):
            factors.append(RiskFactor(
                name="Unverified Contract",
                score=80,
                weight=0.3,
                category="contract_security",
                description="Contract source code is not verified",
                severity=RiskLevel.HIGH
            ))
        
        return factors
    
    def _analyze_liquidity(self, liquidity_data: Dict) -> List[RiskFactor]:
        """Analyze liquidity-related risk factors"""
        factors = []
        
        if not liquidity_data:
            return factors
        
        # Liquidity amount
        liquidity_usd = liquidity_data.get('total_liquidity_usd', 0)
        if liquidity_usd < 1000:
            score = 90
            severity = RiskLevel.CRITICAL
            desc = f"Very low liquidity: ${liquidity_usd:,.2f}"
        elif liquidity_usd < 10000:
            score = 70
            severity = RiskLevel.HIGH
            desc = f"Low liquidity: ${liquidity_usd:,.2f}"
        elif liquidity_usd < 50000:
            score = 40
            severity = RiskLevel.MEDIUM
            desc = f"Moderate liquidity: ${liquidity_usd:,.2f}"
        else:
            score = 15
            severity = RiskLevel.LOW
            desc = f"Good liquidity: ${liquidity_usd:,.2f}"
        
        factors.append(RiskFactor(
            name="Liquidity Amount",
            score=score,
            weight=0.4,
            category="liquidity",
            description=desc,
            severity=severity
        ))
        
        # Liquidity lock
        if liquidity_data.get('is_locked', False):
            lock_duration = liquidity_data.get('lock_duration_days', 0)
            if lock_duration > 365:
                score = 10
                severity = RiskLevel.LOW
            elif lock_duration > 90:
                score = 25
                severity = RiskLevel.LOW
            else:
                score = 50
                severity = RiskLevel.MEDIUM
            
            factors.append(RiskFactor(
                name="Liquidity Lock",
                score=score,
                weight=0.3,
                category="liquidity",
                description=f"Liquidity locked for {lock_duration} days",
                severity=severity
            ))
        else:
            factors.append(RiskFactor(
                name="Unlocked Liquidity",
                score=85,
                weight=0.3,
                category="liquidity",
                description="Liquidity is not locked - can be removed anytime",
                severity=RiskLevel.CRITICAL
            ))
        
        return factors
    
    def _analyze_social_sentiment(self, social_data: Dict) -> List[RiskFactor]:
        """Analyze social sentiment factors"""
        factors = []
        
        if not social_data:
            return factors
        
        sentiment_score = social_data.get('sentiment_score', 0)  # -1 to 1
        
        # Convert sentiment to risk score (inverse relationship)
        if sentiment_score > 0.5:
            risk_score = 20
            severity = RiskLevel.LOW
            desc = "Very positive social sentiment"
        elif sentiment_score > 0.2:
            risk_score = 35
            severity = RiskLevel.LOW
            desc = "Positive social sentiment"
        elif sentiment_score > -0.2:
            risk_score = 50
            severity = RiskLevel.MEDIUM
            desc = "Neutral social sentiment"
        elif sentiment_score > -0.5:
            risk_score = 70
            severity = RiskLevel.HIGH
            desc = "Negative social sentiment"
        else:
            risk_score = 85
            severity = RiskLevel.CRITICAL
            desc = "Very negative social sentiment"
        
        factors.append(RiskFactor(
            name="Social Sentiment",
            score=risk_score,
            weight=0.4,
            category="social_sentiment",
            description=desc,
            severity=severity
        ))
        
        # Mention volume
        mentions = social_data.get('total_mentions', 0)
        if mentions < 10:
            factors.append(RiskFactor(
                name="Low Social Activity",
                score=60,
                weight=0.3,
                category="social_sentiment",
                description=f"Only {mentions} social mentions found",
                severity=RiskLevel.MEDIUM
            ))
        
        return factors
    
    def _analyze_developer_reputation(self, dev_data: Dict) -> List[RiskFactor]:
        """Analyze developer reputation factors"""
        factors = []
        
        if not dev_data:
            factors.append(RiskFactor(
                name="Unknown Developers",
                score=75,
                weight=0.5,
                category="developer_reputation",
                description="No developer information available",
                severity=RiskLevel.HIGH
            ))
            return factors
        
        # Developer experience
        avg_experience = dev_data.get('average_experience_years', 0)
        if avg_experience > 3:
            score = 20
            severity = RiskLevel.LOW
            desc = f"Experienced team (avg {avg_experience:.1f} years)"
        elif avg_experience > 1:
            score = 40
            severity = RiskLevel.MEDIUM
            desc = f"Moderate experience (avg {avg_experience:.1f} years)"
        else:
            score = 70
            severity = RiskLevel.HIGH
            desc = f"Limited experience (avg {avg_experience:.1f} years)"
        
        factors.append(RiskFactor(
            name="Developer Experience",
            score=score,
            weight=0.4,
            category="developer_reputation",
            description=desc,
            severity=severity
        ))
        
        # GitHub activity
        github_score = dev_data.get('github_activity_score', 0)  # 0-100
        if github_score > 70:
            factors.append(RiskFactor(
                name="GitHub Activity",
                score=25,
                weight=0.3,
                category="developer_reputation",
                description="High GitHub activity",
                severity=RiskLevel.LOW
            ))
        elif github_score > 40:
            factors.append(RiskFactor(
                name="GitHub Activity",
                score=45,
                weight=0.3,
                category="developer_reputation",
                description="Moderate GitHub activity",
                severity=RiskLevel.MEDIUM
            ))
        else:
            factors.append(RiskFactor(
                name="GitHub Activity",
                score=70,
                weight=0.3,
                category="developer_reputation",
                description="Low GitHub activity",
                severity=RiskLevel.HIGH
            ))
        
        return factors
    
    def _analyze_market_metrics(self, market_data: Dict) -> List[RiskFactor]:
        """Analyze market-related risk factors"""
        factors = []
        
        if not market_data:
            return factors
        
        # Market cap
        market_cap = market_data.get('market_cap_usd', 0)
        if market_cap < 100000:
            factors.append(RiskFactor(
                name="Low Market Cap",
                score=70,
                weight=0.4,
                category="market_metrics",
                description=f"Low market cap: ${market_cap:,.0f}",
                severity=RiskLevel.HIGH
            ))
        
        # Trading volume
        volume_24h = market_data.get('volume_24h_usd', 0)
        if volume_24h < 10000:
            factors.append(RiskFactor(
                name="Low Trading Volume",
                score=65,
                weight=0.3,
                category="market_metrics",
                description=f"Low 24h volume: ${volume_24h:,.0f}",
                severity=RiskLevel.MEDIUM
            ))
        
        return factors
    
    def _calculate_weighted_score(self, factors: List[RiskFactor]) -> float:
        """Calculate weighted risk score from all factors"""
        if not factors:
            return 50.0  # Default medium risk
        
        # Group factors by category
        category_scores = {}
        category_weights = {}
        
        for factor in factors:
            if factor.category not in category_scores:
                category_scores[factor.category] = []
                category_weights[factor.category] = []
            
            category_scores[factor.category].append(factor.score * factor.weight)
            category_weights[factor.category].append(factor.weight)
        
        # Calculate category averages
        weighted_score = 0
        total_weight = 0
        
        for category, scores in category_scores.items():
            weights = category_weights[category]
            if sum(weights) > 0:
                category_avg = sum(scores) / sum(weights)
                category_weight = self.weights.get(category, 0.1)
                weighted_score += category_avg * category_weight
                total_weight += category_weight
        
        return weighted_score / total_weight if total_weight > 0 else 50.0
    
    def _determine_risk_level(self, score: float) -> RiskLevel:
        """Determine risk level based on score"""
        for level, (min_score, max_score) in self.risk_thresholds.items():
            if min_score <= score < max_score:
                return level
        return RiskLevel.CRITICAL
    
    def _calculate_confidence(self, factors: List[RiskFactor]) -> float:
        """Calculate confidence in the assessment"""
        if not factors:
            return 0.3  # Low confidence with no data
        
        # Base confidence on number of factors and data quality
        base_confidence = min(0.9, 0.3 + (len(factors) * 0.1))
        
        # Adjust based on factor weights
        avg_weight = np.mean([f.weight for f in factors])
        weight_adjustment = avg_weight * 0.2
        
        return min(0.95, base_confidence + weight_adjustment)
    
    def _generate_recommendations(self, factors: List[RiskFactor], risk_level: RiskLevel) -> List[str]:
        """Generate recommendations based on risk factors"""
        recommendations = []
        
        # Critical factors
        critical_factors = [f for f in factors if f.severity == RiskLevel.CRITICAL]
        if critical_factors:
            recommendations.append("⚠️ CRITICAL RISKS DETECTED - Consider avoiding this token")
            for factor in critical_factors:
                recommendations.append(f"• {factor.description}")
        
        # High risk factors
        high_factors = [f for f in factors if f.severity == RiskLevel.HIGH]
        if high_factors:
            recommendations.append("🔴 High risk factors identified:")
            for factor in high_factors[:3]:  # Limit to top 3
                recommendations.append(f"• {factor.description}")
        
        # General recommendations based on risk level
        if risk_level == RiskLevel.CRITICAL:
            recommendations.append("🚫 Recommendation: AVOID - Too many critical risk factors")
        elif risk_level == RiskLevel.HIGH:
            recommendations.append("⚠️ Recommendation: HIGH CAUTION - Only invest what you can afford to lose")
        elif risk_level == RiskLevel.MEDIUM:
            recommendations.append("🟡 Recommendation: MODERATE CAUTION - Do additional research")
        else:
            recommendations.append("✅ Recommendation: RELATIVELY SAFE - But always DYOR")
        
        return recommendations
