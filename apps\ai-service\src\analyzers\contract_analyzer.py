"""
Smart Contract Security Analyzer
Analyzes smart contracts for security vulnerabilities and risk factors
"""

import re
import json
import asyncio
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

import aiohttp
from web3 import Web3
from eth_utils import is_address, to_checksum_address

logger = logging.getLogger(__name__)

class SecurityRisk(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class ContractFunction:
    name: str
    signature: str
    visibility: str
    state_mutability: str
    inputs: List[Dict]
    outputs: List[Dict]
    risk_level: SecurityRisk
    description: str = ""

@dataclass
class SecurityIssue:
    type: str
    severity: SecurityRisk
    description: str
    location: Optional[str] = None
    recommendation: str = ""

@dataclass
class ContractAnalysisResult:
    address: str
    chain: str
    is_verified: bool
    source_code: Optional[str]
    compiler_version: Optional[str]
    
    # Security flags
    has_owner: bool
    has_mint_function: bool
    has_burn_function: bool
    has_pause_function: bool
    has_blacklist: bool
    has_whitelist: bool
    has_honeypot: bool
    has_rug_pull: bool
    
    # Ownership analysis
    owner: Optional[str]
    ownership_renounced: bool
    multi_sig: bool
    
    # Proxy analysis
    is_proxy: bool
    implementation: Optional[str]
    proxy_type: Optional[str]
    
    # Analysis results
    functions: List[ContractFunction]
    security_issues: List[SecurityIssue]
    risk_score: float
    confidence: float

class ContractAnalyzer:
    """
    Advanced smart contract security analyzer
    """
    
    def __init__(self):
        self.web3_providers = {
            'ethereum': 'https://mainnet.infura.io/v3/YOUR_KEY',
            'bsc': 'https://bsc-dataseed.binance.org/',
            'polygon': 'https://polygon-rpc.com/',
            'arbitrum': 'https://arb1.arbitrum.io/rpc',
            'optimism': 'https://mainnet.optimism.io',
        }
        
        # Dangerous function patterns
        self.dangerous_patterns = {
            'mint': {
                'patterns': [r'function\s+mint\s*\(', r'function\s+_mint\s*\('],
                'risk': SecurityRisk.HIGH,
                'description': 'Mint function can increase token supply'
            },
            'burn': {
                'patterns': [r'function\s+burn\s*\(', r'function\s+_burn\s*\('],
                'risk': SecurityRisk.MEDIUM,
                'description': 'Burn function can decrease token supply'
            },
            'pause': {
                'patterns': [r'function\s+pause\s*\(', r'whenNotPaused', r'Pausable'],
                'risk': SecurityRisk.MEDIUM,
                'description': 'Contract can be paused by owner'
            },
            'blacklist': {
                'patterns': [r'blacklist', r'blocked', r'banned'],
                'risk': SecurityRisk.HIGH,
                'description': 'Contract can blacklist addresses'
            },
            'whitelist': {
                'patterns': [r'whitelist', r'allowed'],
                'risk': SecurityRisk.MEDIUM,
                'description': 'Contract uses whitelist mechanism'
            },
            'owner_only': {
                'patterns': [r'onlyOwner', r'owner\s*==', r'msg\.sender\s*==\s*owner'],
                'risk': SecurityRisk.MEDIUM,
                'description': 'Owner-only functions detected'
            },
            'selfdestruct': {
                'patterns': [r'selfdestruct', r'suicide'],
                'risk': SecurityRisk.CRITICAL,
                'description': 'Contract can be destroyed'
            },
            'delegatecall': {
                'patterns': [r'delegatecall', r'callcode'],
                'risk': SecurityRisk.HIGH,
                'description': 'Dangerous delegatecall usage'
            },
        }
        
        # Honeypot patterns
        self.honeypot_patterns = [
            r'require\s*\(\s*false\s*\)',  # Always failing require
            r'revert\s*\(\s*\)',           # Unconditional revert
            r'transfer\s*\(\s*[^,]+,\s*0\s*\)',  # Zero transfers
            r'balanceOf\[.*\]\s*=\s*0',    # Balance manipulation
        ]
        
        # Rug pull indicators
        self.rug_pull_patterns = [
            r'removeLiquidity',
            r'emergencyWithdraw',
            r'drain',
            r'sweep',
            r'rescue',
        ]
    
    async def analyze_contract(self, address: str, chain: str) -> ContractAnalysisResult:
        """
        Perform comprehensive contract analysis
        """
        try:
            logger.info(f"Starting contract analysis for {address} on {chain}")
            
            # Validate address
            if not is_address(address):
                raise ValueError(f"Invalid address: {address}")
            
            address = to_checksum_address(address)
            
            # Initialize result
            result = ContractAnalysisResult(
                address=address,
                chain=chain,
                is_verified=False,
                source_code=None,
                compiler_version=None,
                has_owner=False,
                has_mint_function=False,
                has_burn_function=False,
                has_pause_function=False,
                has_blacklist=False,
                has_whitelist=False,
                has_honeypot=False,
                has_rug_pull=False,
                owner=None,
                ownership_renounced=False,
                multi_sig=False,
                is_proxy=False,
                implementation=None,
                proxy_type=None,
                functions=[],
                security_issues=[],
                risk_score=0.0,
                confidence=0.0
            )
            
            # Get contract source code
            source_code = await self._get_contract_source(address, chain)
            if source_code:
                result.is_verified = True
                result.source_code = source_code['source']
                result.compiler_version = source_code.get('compiler')
                
                # Analyze source code
                await self._analyze_source_code(result)
            else:
                # Analyze bytecode only
                await self._analyze_bytecode(result)
            
            # Analyze contract behavior
            await self._analyze_contract_behavior(result)
            
            # Calculate risk score
            result.risk_score = self._calculate_risk_score(result)
            result.confidence = self._calculate_confidence(result)
            
            logger.info(f"Contract analysis completed for {address}: risk_score={result.risk_score}")
            
            return result
            
        except Exception as e:
            logger.error(f"Contract analysis failed for {address}: {str(e)}")
            raise
    
    async def _get_contract_source(self, address: str, chain: str) -> Optional[Dict]:
        """
        Get contract source code from blockchain explorer
        """
        try:
            # Use different APIs based on chain
            if chain == 'ethereum':
                api_url = f"https://api.etherscan.io/api"
                params = {
                    'module': 'contract',
                    'action': 'getsourcecode',
                    'address': address,
                    'apikey': 'YourApiKeyToken'  # Replace with actual API key
                }
            elif chain == 'bsc':
                api_url = f"https://api.bscscan.com/api"
                params = {
                    'module': 'contract',
                    'action': 'getsourcecode',
                    'address': address,
                    'apikey': 'YourApiKeyToken'
                }
            elif chain == 'polygon':
                api_url = f"https://api.polygonscan.com/api"
                params = {
                    'module': 'contract',
                    'action': 'getsourcecode',
                    'address': address,
                    'apikey': 'YourApiKeyToken'
                }
            else:
                return None
            
            async with aiohttp.ClientSession() as session:
                async with session.get(api_url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data['status'] == '1' and data['result'][0]['SourceCode']:
                            return {
                                'source': data['result'][0]['SourceCode'],
                                'compiler': data['result'][0]['CompilerVersion'],
                                'contract_name': data['result'][0]['ContractName'],
                            }
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get contract source for {address}: {str(e)}")
            return None
    
    async def _analyze_source_code(self, result: ContractAnalysisResult):
        """
        Analyze contract source code for security issues
        """
        if not result.source_code:
            return
        
        source = result.source_code.lower()
        
        # Check for dangerous patterns
        for pattern_name, pattern_info in self.dangerous_patterns.items():
            for pattern in pattern_info['patterns']:
                if re.search(pattern, source, re.IGNORECASE):
                    # Set flags
                    if pattern_name == 'mint':
                        result.has_mint_function = True
                    elif pattern_name == 'burn':
                        result.has_burn_function = True
                    elif pattern_name == 'pause':
                        result.has_pause_function = True
                    elif pattern_name == 'blacklist':
                        result.has_blacklist = True
                    elif pattern_name == 'whitelist':
                        result.has_whitelist = True
                    elif pattern_name == 'owner_only':
                        result.has_owner = True
                    
                    # Add security issue
                    result.security_issues.append(SecurityIssue(
                        type=pattern_name,
                        severity=pattern_info['risk'],
                        description=pattern_info['description']
                    ))
                    break
        
        # Check for honeypot patterns
        for pattern in self.honeypot_patterns:
            if re.search(pattern, source, re.IGNORECASE):
                result.has_honeypot = True
                result.security_issues.append(SecurityIssue(
                    type='honeypot',
                    severity=SecurityRisk.CRITICAL,
                    description='Potential honeypot pattern detected'
                ))
                break
        
        # Check for rug pull patterns
        for pattern in self.rug_pull_patterns:
            if re.search(pattern, source, re.IGNORECASE):
                result.has_rug_pull = True
                result.security_issues.append(SecurityIssue(
                    type='rug_pull',
                    severity=SecurityRisk.CRITICAL,
                    description='Potential rug pull function detected'
                ))
        
        # Check for ownership renouncement
        if re.search(r'renounceownership|owner\s*=\s*address\(0\)', source, re.IGNORECASE):
            result.ownership_renounced = True
        
        # Check for proxy patterns
        if re.search(r'proxy|implementation|delegate', source, re.IGNORECASE):
            result.is_proxy = True
            
            # Determine proxy type
            if re.search(r'transparentupgradeableproxy', source, re.IGNORECASE):
                result.proxy_type = 'transparent'
            elif re.search(r'uupsupgradeable', source, re.IGNORECASE):
                result.proxy_type = 'uups'
            elif re.search(r'beacon', source, re.IGNORECASE):
                result.proxy_type = 'beacon'
            elif re.search(r'diamond', source, re.IGNORECASE):
                result.proxy_type = 'diamond'
    
    async def _analyze_bytecode(self, result: ContractAnalysisResult):
        """
        Analyze contract bytecode when source is not available
        """
        try:
            # Get Web3 provider for the chain
            provider_url = self.web3_providers.get(result.chain)
            if not provider_url:
                return
            
            web3 = Web3(Web3.HTTPProvider(provider_url))
            
            # Get contract bytecode
            bytecode = web3.eth.get_code(result.address)
            
            if bytecode and len(bytecode) > 2:  # More than '0x'
                # Basic bytecode analysis
                bytecode_hex = bytecode.hex()
                
                # Check for common function selectors
                if 'a9059cbb' in bytecode_hex:  # transfer(address,uint256)
                    pass  # Standard ERC20
                
                if '40c10f19' in bytecode_hex:  # mint(address,uint256)
                    result.has_mint_function = True
                    result.security_issues.append(SecurityIssue(
                        type='mint_function',
                        severity=SecurityRisk.HIGH,
                        description='Mint function detected in bytecode'
                    ))
                
                if '42966c68' in bytecode_hex:  # burn(uint256)
                    result.has_burn_function = True
                
                # Check for proxy patterns in bytecode
                if '3659cfe6' in bytecode_hex:  # delegatecall selector
                    result.is_proxy = True
                    result.security_issues.append(SecurityIssue(
                        type='proxy_contract',
                        severity=SecurityRisk.MEDIUM,
                        description='Proxy contract detected'
                    ))
            
        except Exception as e:
            logger.error(f"Bytecode analysis failed for {result.address}: {str(e)}")
    
    async def _analyze_contract_behavior(self, result: ContractAnalysisResult):
        """
        Analyze contract behavior through transaction history
        """
        try:
            # This would involve analyzing recent transactions
            # to detect suspicious patterns like:
            # - Large token mints
            # - Liquidity removals
            # - Ownership transfers
            # - Unusual trading patterns
            
            # For now, we'll implement basic checks
            pass
            
        except Exception as e:
            logger.error(f"Behavior analysis failed for {result.address}: {str(e)}")
    
    def _calculate_risk_score(self, result: ContractAnalysisResult) -> float:
        """
        Calculate overall risk score based on analysis results
        """
        risk_score = 0.0
        
        # Base score adjustments
        if not result.is_verified:
            risk_score += 30  # Unverified contracts are risky
        
        if result.has_honeypot:
            risk_score += 50  # Major red flag
        
        if result.has_rug_pull:
            risk_score += 45  # Major red flag
        
        if result.has_mint_function:
            risk_score += 25 if not result.ownership_renounced else 15
        
        if result.has_blacklist:
            risk_score += 20
        
        if result.has_pause_function:
            risk_score += 15
        
        if result.has_owner and not result.ownership_renounced:
            risk_score += 20
        
        if result.is_proxy:
            risk_score += 10  # Proxy contracts have upgrade risks
        
        # Reduce risk for positive indicators
        if result.ownership_renounced:
            risk_score -= 15
        
        if result.multi_sig:
            risk_score -= 10
        
        # Security issues impact
        for issue in result.security_issues:
            if issue.severity == SecurityRisk.CRITICAL:
                risk_score += 20
            elif issue.severity == SecurityRisk.HIGH:
                risk_score += 15
            elif issue.severity == SecurityRisk.MEDIUM:
                risk_score += 10
            elif issue.severity == SecurityRisk.LOW:
                risk_score += 5
        
        # Normalize to 0-100 range
        return min(100.0, max(0.0, risk_score))
    
    def _calculate_confidence(self, result: ContractAnalysisResult) -> float:
        """
        Calculate confidence in the analysis
        """
        confidence = 0.5  # Base confidence
        
        if result.is_verified:
            confidence += 0.3  # Source code analysis is more reliable
        
        if result.source_code and len(result.source_code) > 1000:
            confidence += 0.1  # More code to analyze
        
        if len(result.security_issues) > 0:
            confidence += 0.1  # Found specific issues
        
        return min(1.0, confidence)
