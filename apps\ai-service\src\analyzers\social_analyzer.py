"""
Social Media Sentiment Analyzer
Analyzes social media sentiment and trends for cryptocurrency tokens
"""

import re
import asyncio
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum

import aiohttp
import nltk
from nltk.sentiment import SentimentIntensityAnalyzer
from textblob import TextBlob
import tweepy
import praw

logger = logging.getLogger(__name__)

class SentimentLabel(Enum):
    VERY_POSITIVE = "very_positive"
    POSITIVE = "positive"
    NEUTRAL = "neutral"
    NEGATIVE = "negative"
    VERY_NEGATIVE = "very_negative"

@dataclass
class SocialMention:
    platform: str
    text: str
    author: str
    timestamp: datetime
    engagement: int  # likes, retweets, upvotes, etc.
    sentiment_score: float
    sentiment_label: SentimentLabel
    influence_score: float = 0.0
    is_verified: bool = False

@dataclass
class SocialAnalysisResult:
    query: str
    timeframe: str
    total_mentions: int
    sentiment_score: float  # -1 to 1
    sentiment_label: SentimentLabel
    confidence: float
    trending_score: float
    
    # Platform breakdown
    twitter_mentions: int
    reddit_mentions: int
    telegram_mentions: int
    discord_mentions: int
    
    # Sentiment breakdown
    positive_mentions: int
    neutral_mentions: int
    negative_mentions: int
    
    # Top mentions and influencers
    top_mentions: List[SocialMention]
    influencer_mentions: List[SocialMention]
    
    # Trend analysis
    mention_trend: List[Dict]  # Time series data
    sentiment_trend: List[Dict]  # Sentiment over time

class SocialAnalyzer:
    """
    Advanced social media sentiment analyzer for cryptocurrency tokens
    """
    
    def __init__(self):
        # Initialize NLTK sentiment analyzer
        try:
            nltk.data.find('vader_lexicon')
        except LookupError:
            nltk.download('vader_lexicon')
        
        self.sia = SentimentIntensityAnalyzer()
        
        # Social media API clients (configure with your API keys)
        self.twitter_client = None
        self.reddit_client = None
        
        # Initialize APIs if credentials are available
        self._init_twitter_client()
        self._init_reddit_client()
        
        # Crypto-specific sentiment keywords
        self.positive_keywords = [
            'moon', 'bullish', 'hodl', 'diamond hands', 'to the moon',
            'buy the dip', 'gem', 'rocket', 'lambo', 'ath', 'pump',
            'breakout', 'rally', 'surge', 'gains', 'profit'
        ]
        
        self.negative_keywords = [
            'dump', 'crash', 'bearish', 'paper hands', 'rug pull',
            'scam', 'honeypot', 'exit scam', 'dead cat bounce',
            'fud', 'rekt', 'bag holder', 'dip', 'loss', 'sell'
        ]
        
        # Influencer accounts (example list)
        self.crypto_influencers = {
            'twitter': [
                'elonmusk', 'VitalikButerin', 'cz_binance', 'justinsuntron',
                'aantonop', 'naval', 'APompliano', 'DocumentingBTC'
            ],
            'reddit': [
                'vbuterin', 'coblee', 'theymos'
            ]
        }
    
    def _init_twitter_client(self):
        """Initialize Twitter API client"""
        try:
            # Replace with your Twitter API credentials
            api_key = "your_twitter_api_key"
            api_secret = "your_twitter_api_secret"
            access_token = "your_access_token"
            access_token_secret = "your_access_token_secret"
            
            if all([api_key, api_secret, access_token, access_token_secret]):
                auth = tweepy.OAuthHandler(api_key, api_secret)
                auth.set_access_token(access_token, access_token_secret)
                self.twitter_client = tweepy.API(auth, wait_on_rate_limit=True)
                logger.info("Twitter client initialized")
        except Exception as e:
            logger.warning(f"Failed to initialize Twitter client: {e}")
    
    def _init_reddit_client(self):
        """Initialize Reddit API client"""
        try:
            # Replace with your Reddit API credentials
            client_id = "your_reddit_client_id"
            client_secret = "your_reddit_client_secret"
            user_agent = "CryptoSentinel/1.0"
            
            if all([client_id, client_secret]):
                self.reddit_client = praw.Reddit(
                    client_id=client_id,
                    client_secret=client_secret,
                    user_agent=user_agent
                )
                logger.info("Reddit client initialized")
        except Exception as e:
            logger.warning(f"Failed to initialize Reddit client: {e}")
    
    async def analyze_sentiment(self, query: str, timeframe: str = '24h', 
                              sources: List[str] = None) -> SocialAnalysisResult:
        """
        Perform comprehensive social sentiment analysis
        """
        try:
            logger.info(f"Starting social sentiment analysis for: {query}")
            
            if sources is None:
                sources = ['twitter', 'reddit']
            
            # Collect mentions from different platforms
            mentions = []
            
            if 'twitter' in sources and self.twitter_client:
                twitter_mentions = await self._analyze_twitter(query, timeframe)
                mentions.extend(twitter_mentions)
            
            if 'reddit' in sources and self.reddit_client:
                reddit_mentions = await self._analyze_reddit(query, timeframe)
                mentions.extend(reddit_mentions)
            
            # If no API clients available, use mock data for demonstration
            if not mentions:
                mentions = self._generate_mock_mentions(query, timeframe)
            
            # Analyze collected mentions
            result = self._process_mentions(mentions, query, timeframe)
            
            logger.info(f"Social analysis completed: {result.total_mentions} mentions, "
                       f"sentiment: {result.sentiment_score:.2f}")
            
            return result
            
        except Exception as e:
            logger.error(f"Social sentiment analysis failed: {str(e)}")
            raise
    
    async def _analyze_twitter(self, query: str, timeframe: str) -> List[SocialMention]:
        """Analyze Twitter mentions"""
        mentions = []
        
        try:
            # Calculate date range
            end_date = datetime.now()
            if timeframe == '1h':
                start_date = end_date - timedelta(hours=1)
            elif timeframe == '24h':
                start_date = end_date - timedelta(days=1)
            elif timeframe == '7d':
                start_date = end_date - timedelta(days=7)
            else:
                start_date = end_date - timedelta(days=1)
            
            # Search tweets
            tweets = tweepy.Cursor(
                self.twitter_client.search_tweets,
                q=f"{query} -filter:retweets",
                lang="en",
                result_type="recent",
                tweet_mode="extended"
            ).items(100)
            
            for tweet in tweets:
                if tweet.created_at < start_date:
                    continue
                
                # Calculate sentiment
                sentiment_score = self._calculate_sentiment(tweet.full_text)
                sentiment_label = self._get_sentiment_label(sentiment_score)
                
                # Calculate influence score
                influence_score = self._calculate_influence_score(
                    tweet.user.followers_count,
                    tweet.retweet_count,
                    tweet.favorite_count
                )
                
                mention = SocialMention(
                    platform='twitter',
                    text=tweet.full_text,
                    author=tweet.user.screen_name,
                    timestamp=tweet.created_at,
                    engagement=tweet.retweet_count + tweet.favorite_count,
                    sentiment_score=sentiment_score,
                    sentiment_label=sentiment_label,
                    influence_score=influence_score,
                    is_verified=tweet.user.verified
                )
                
                mentions.append(mention)
            
        except Exception as e:
            logger.error(f"Twitter analysis failed: {e}")
        
        return mentions
    
    async def _analyze_reddit(self, query: str, timeframe: str) -> List[SocialMention]:
        """Analyze Reddit mentions"""
        mentions = []
        
        try:
            # Search relevant subreddits
            subreddits = ['cryptocurrency', 'cryptomarkets', 'defi', 'ethtrader', 'bitcoin']
            
            for subreddit_name in subreddits:
                subreddit = self.reddit_client.subreddit(subreddit_name)
                
                # Search posts
                for submission in subreddit.search(query, limit=20):
                    # Check if within timeframe
                    post_time = datetime.fromtimestamp(submission.created_utc)
                    if not self._is_within_timeframe(post_time, timeframe):
                        continue
                    
                    # Analyze post
                    text = f"{submission.title} {submission.selftext}"
                    sentiment_score = self._calculate_sentiment(text)
                    sentiment_label = self._get_sentiment_label(sentiment_score)
                    
                    mention = SocialMention(
                        platform='reddit',
                        text=text,
                        author=submission.author.name if submission.author else 'deleted',
                        timestamp=post_time,
                        engagement=submission.score + submission.num_comments,
                        sentiment_score=sentiment_score,
                        sentiment_label=sentiment_label,
                        influence_score=submission.score / 10.0  # Simple influence calculation
                    )
                    
                    mentions.append(mention)
                    
                    # Analyze top comments
                    submission.comments.replace_more(limit=0)
                    for comment in submission.comments[:5]:  # Top 5 comments
                        if hasattr(comment, 'body') and len(comment.body) > 10:
                            comment_sentiment = self._calculate_sentiment(comment.body)
                            comment_label = self._get_sentiment_label(comment_sentiment)
                            
                            comment_mention = SocialMention(
                                platform='reddit',
                                text=comment.body,
                                author=comment.author.name if comment.author else 'deleted',
                                timestamp=datetime.fromtimestamp(comment.created_utc),
                                engagement=comment.score,
                                sentiment_score=comment_sentiment,
                                sentiment_label=comment_label,
                                influence_score=comment.score / 5.0
                            )
                            
                            mentions.append(comment_mention)
            
        except Exception as e:
            logger.error(f"Reddit analysis failed: {e}")
        
        return mentions
    
    def _calculate_sentiment(self, text: str) -> float:
        """Calculate sentiment score for text"""
        try:
            # Clean text
            cleaned_text = self._clean_text(text)
            
            # Use NLTK VADER sentiment analyzer
            vader_scores = self.sia.polarity_scores(cleaned_text)
            vader_compound = vader_scores['compound']
            
            # Use TextBlob as secondary analyzer
            blob = TextBlob(cleaned_text)
            textblob_polarity = blob.sentiment.polarity
            
            # Combine scores with crypto-specific adjustments
            crypto_adjustment = self._get_crypto_sentiment_adjustment(cleaned_text)
            
            # Weighted average
            final_score = (vader_compound * 0.6 + textblob_polarity * 0.3 + crypto_adjustment * 0.1)
            
            # Normalize to -1 to 1 range
            return max(-1.0, min(1.0, final_score))
            
        except Exception as e:
            logger.error(f"Sentiment calculation failed: {e}")
            return 0.0
    
    def _get_crypto_sentiment_adjustment(self, text: str) -> float:
        """Get crypto-specific sentiment adjustment"""
        text_lower = text.lower()
        
        positive_count = sum(1 for keyword in self.positive_keywords if keyword in text_lower)
        negative_count = sum(1 for keyword in self.negative_keywords if keyword in text_lower)
        
        if positive_count == 0 and negative_count == 0:
            return 0.0
        
        # Calculate adjustment based on keyword presence
        total_keywords = positive_count + negative_count
        positive_ratio = positive_count / total_keywords
        
        # Return adjustment between -0.5 and 0.5
        return (positive_ratio - 0.5) * 1.0
    
    def _clean_text(self, text: str) -> str:
        """Clean and preprocess text"""
        # Remove URLs
        text = re.sub(r'http\S+|www\S+|https\S+', '', text, flags=re.MULTILINE)
        
        # Remove mentions and hashtags for sentiment analysis
        text = re.sub(r'@\w+|#\w+', '', text)
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def _get_sentiment_label(self, score: float) -> SentimentLabel:
        """Convert sentiment score to label"""
        if score >= 0.6:
            return SentimentLabel.VERY_POSITIVE
        elif score >= 0.2:
            return SentimentLabel.POSITIVE
        elif score >= -0.2:
            return SentimentLabel.NEUTRAL
        elif score >= -0.6:
            return SentimentLabel.NEGATIVE
        else:
            return SentimentLabel.VERY_NEGATIVE
    
    def _calculate_influence_score(self, followers: int, retweets: int, likes: int) -> float:
        """Calculate influence score based on engagement metrics"""
        # Normalize and combine metrics
        follower_score = min(followers / 10000, 10.0)  # Cap at 10
        engagement_score = (retweets * 2 + likes) / 100.0
        
        return min(follower_score + engagement_score, 100.0)
    
    def _is_within_timeframe(self, timestamp: datetime, timeframe: str) -> bool:
        """Check if timestamp is within specified timeframe"""
        now = datetime.now()
        
        if timeframe == '1h':
            return timestamp >= now - timedelta(hours=1)
        elif timeframe == '24h':
            return timestamp >= now - timedelta(days=1)
        elif timeframe == '7d':
            return timestamp >= now - timedelta(days=7)
        elif timeframe == '30d':
            return timestamp >= now - timedelta(days=30)
        
        return True
    
    def _generate_mock_mentions(self, query: str, timeframe: str) -> List[SocialMention]:
        """Generate mock mentions for demonstration when APIs are not available"""
        mock_mentions = [
            SocialMention(
                platform='twitter',
                text=f"Just bought some {query}! Looking bullish 🚀",
                author='crypto_trader_123',
                timestamp=datetime.now() - timedelta(hours=2),
                engagement=45,
                sentiment_score=0.7,
                sentiment_label=SentimentLabel.POSITIVE,
                influence_score=25.0
            ),
            SocialMention(
                platform='reddit',
                text=f"What do you think about {query}? Seems like a solid project with good fundamentals.",
                author='defi_enthusiast',
                timestamp=datetime.now() - timedelta(hours=5),
                engagement=12,
                sentiment_score=0.4,
                sentiment_label=SentimentLabel.POSITIVE,
                influence_score=15.0
            ),
            SocialMention(
                platform='twitter',
                text=f"Not sure about {query}, the tokenomics look suspicious to me",
                author='cautious_investor',
                timestamp=datetime.now() - timedelta(hours=8),
                engagement=23,
                sentiment_score=-0.3,
                sentiment_label=SentimentLabel.NEGATIVE,
                influence_score=18.0
            ),
        ]
        
        return mock_mentions
    
    def _process_mentions(self, mentions: List[SocialMention], query: str, timeframe: str) -> SocialAnalysisResult:
        """Process collected mentions and generate analysis result"""
        if not mentions:
            return SocialAnalysisResult(
                query=query,
                timeframe=timeframe,
                total_mentions=0,
                sentiment_score=0.0,
                sentiment_label=SentimentLabel.NEUTRAL,
                confidence=0.0,
                trending_score=0.0,
                twitter_mentions=0,
                reddit_mentions=0,
                telegram_mentions=0,
                discord_mentions=0,
                positive_mentions=0,
                neutral_mentions=0,
                negative_mentions=0,
                top_mentions=[],
                influencer_mentions=[],
                mention_trend=[],
                sentiment_trend=[]
            )
        
        # Calculate overall sentiment
        total_sentiment = sum(mention.sentiment_score for mention in mentions)
        avg_sentiment = total_sentiment / len(mentions)
        
        # Count by platform
        platform_counts = {}
        for mention in mentions:
            platform_counts[mention.platform] = platform_counts.get(mention.platform, 0) + 1
        
        # Count by sentiment
        positive_count = sum(1 for m in mentions if m.sentiment_score > 0.2)
        neutral_count = sum(1 for m in mentions if -0.2 <= m.sentiment_score <= 0.2)
        negative_count = sum(1 for m in mentions if m.sentiment_score < -0.2)
        
        # Get top mentions by engagement
        top_mentions = sorted(mentions, key=lambda x: x.engagement, reverse=True)[:10]
        
        # Get influencer mentions
        influencer_mentions = [m for m in mentions if m.influence_score > 50 or m.is_verified][:5]
        
        # Calculate trending score
        trending_score = self._calculate_trending_score(mentions)
        
        # Calculate confidence
        confidence = min(len(mentions) / 50.0, 1.0)  # More mentions = higher confidence
        
        return SocialAnalysisResult(
            query=query,
            timeframe=timeframe,
            total_mentions=len(mentions),
            sentiment_score=avg_sentiment,
            sentiment_label=self._get_sentiment_label(avg_sentiment),
            confidence=confidence,
            trending_score=trending_score,
            twitter_mentions=platform_counts.get('twitter', 0),
            reddit_mentions=platform_counts.get('reddit', 0),
            telegram_mentions=platform_counts.get('telegram', 0),
            discord_mentions=platform_counts.get('discord', 0),
            positive_mentions=positive_count,
            neutral_mentions=neutral_count,
            negative_mentions=negative_count,
            top_mentions=top_mentions,
            influencer_mentions=influencer_mentions,
            mention_trend=self._generate_mention_trend(mentions, timeframe),
            sentiment_trend=self._generate_sentiment_trend(mentions, timeframe)
        )
    
    def _calculate_trending_score(self, mentions: List[SocialMention]) -> float:
        """Calculate trending score based on mention frequency and engagement"""
        if not mentions:
            return 0.0
        
        # Calculate engagement rate
        total_engagement = sum(mention.engagement for mention in mentions)
        avg_engagement = total_engagement / len(mentions)
        
        # Calculate recency boost
        now = datetime.now()
        recent_mentions = sum(1 for m in mentions if (now - m.timestamp).total_seconds() < 3600)
        recency_boost = recent_mentions / len(mentions)
        
        # Combine factors
        trending_score = (len(mentions) * 0.4 + avg_engagement * 0.4 + recency_boost * 20) / 10
        
        return min(trending_score, 100.0)
    
    def _generate_mention_trend(self, mentions: List[SocialMention], timeframe: str) -> List[Dict]:
        """Generate mention trend data"""
        # This would create time series data for mentions
        # For now, return empty list
        return []
    
    def _generate_sentiment_trend(self, mentions: List[SocialMention], timeframe: str) -> List[Dict]:
        """Generate sentiment trend data"""
        # This would create time series data for sentiment
        # For now, return empty list
        return []
