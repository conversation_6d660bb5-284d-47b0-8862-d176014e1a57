{"name": "@cryptosentinel/api", "version": "1.0.0", "description": "CryptoSentinel API Service", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc && tsc-alias", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --config jest.integration.config.js", "lint": "eslint src --ext .ts,.js", "lint:fix": "eslint src --ext .ts,.js --fix", "type-check": "tsc --noEmit", "clean": "rm -rf dist", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:prod": "prisma migrate deploy", "db:seed": "tsx src/scripts/seed.ts", "db:studio": "prisma studio", "docker:build": "docker build -t cryptosentinel-api .", "docker:run": "docker run -p 3001:3001 cryptosentinel-api"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "express-rate-limit": "^7.1.5", "express-slow-down": "^2.0.1", "express-validator": "^7.0.1", "@prisma/client": "^5.6.0", "prisma": "^5.6.0", "redis": "^4.6.10", "ioredis": "^5.3.2", "bull": "^4.12.2", "socket.io": "^4.7.4", "axios": "^1.6.2", "node-cron": "^3.0.3", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "nodemailer": "^6.9.7", "handlebars": "^4.7.8", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "graphql": "^16.8.1", "apollo-server-express": "^3.12.1", "graphql-tools": "^9.0.0", "dataloader": "^2.2.2", "ethers": "^6.8.1", "web3": "^4.2.2", "@solana/web3.js": "^1.87.6", "bignumber.js": "^9.1.2", "moment": "^2.29.4", "lodash": "^4.17.21", "uuid": "^9.0.1", "crypto": "^1.0.1", "cheerio": "^1.0.0-rc.12", "puppeteer": "^21.5.2"}, "devDependencies": {"@types/node": "^20.10.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.14", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/lodash": "^4.14.202", "@types/uuid": "^9.0.7", "@types/cheerio": "^0.22.35", "typescript": "^5.3.2", "tsc-alias": "^1.8.8", "nodemon": "^3.0.2", "tsx": "^4.6.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "@types/jest": "^29.5.8", "supertest": "^6.3.3", "@types/supertest": "^2.0.16", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "prettier": "^3.1.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["api", "cryptocurrency", "blockchain", "risk-assessment", "express", "typescript"], "author": "HectorTa1989", "license": "MIT"}