// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model for authentication and profiles
model User {
  id          String   @id @default(cuid())
  address     String   @unique
  username    String?  @unique
  email       String?  @unique
  avatar      String?
  bio         String?
  
  // Account status
  isVerified  <PERSON>olean  @default(false)
  isPremium   Boolean  @default(false)
  role        Role     @default(USER)
  status      UserStatus @default(ACTIVE)
  
  // Preferences stored as JSON
  preferences Json     @default("{}")
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  lastLoginAt DateTime?
  
  // Relations
  portfolios     Portfolio[]
  alerts         Alert[]
  watchlists     Watchlist[]
  analysisHistory AnalysisHistory[]
  
  @@map("users")
}

// Authentication nonces for wallet signing
model AuthNonce {
  address   String   @id
  nonce     String
  expiresAt DateTime
  createdAt DateTime @default(now())
  
  @@map("auth_nonces")
}

// Token information
model Token {
  id          String   @id @default(cuid())
  address     String
  chain       String
  symbol      String
  name        String
  decimals    Int
  totalSupply String?
  logoUrl     String?
  website     String?
  description String?
  isVerified  Boolean  @default(false)
  tags        String[] @default([])
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  metrics           TokenMetrics[]
  riskAssessments   RiskAssessment[]
  contractAnalysis  ContractAnalysis[]
  socialAnalysis    SocialAnalysis[]
  portfolioTokens   PortfolioToken[]
  alerts            Alert[]
  watchlistTokens   WatchlistToken[]
  analysisHistory   AnalysisHistory[]
  
  // Unique constraint on address + chain
  @@unique([address, chain])
  @@index([symbol])
  @@index([chain])
  @@index([isVerified])
  @@map("tokens")
}

// Token market metrics
model TokenMetrics {
  id              String   @id @default(cuid())
  tokenId         String
  price           Float?
  marketCap       Float?
  volume24h       Float?
  priceChange24h  Float?
  volumeChange24h Float?
  circulatingSupply Float?
  maxSupply       Float?
  holders         Int?
  transactions24h Int?
  
  // Timestamps
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  // Relations
  token           Token    @relation(fields: [tokenId], references: [id], onDelete: Cascade)
  
  @@index([tokenId])
  @@index([updatedAt])
  @@map("token_metrics")
}

// Risk assessment results
model RiskAssessment {
  id            String     @id @default(cuid())
  tokenId       String
  overallScore  Float
  riskLevel     RiskLevel
  confidence    Float
  factors       Json       @default("[]")
  recommendations Json     @default("[]")
  version       String     @default("1.0")
  
  // Timestamps
  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @updatedAt
  
  // Relations
  token         Token      @relation(fields: [tokenId], references: [id], onDelete: Cascade)
  
  @@index([tokenId])
  @@index([riskLevel])
  @@index([createdAt])
  @@map("risk_assessments")
}

// Smart contract analysis
model ContractAnalysis {
  id                String   @id @default(cuid())
  tokenId           String
  isVerified        Boolean  @default(false)
  sourceCode        String?
  compiler          String?
  optimization      Boolean?
  
  // Security flags
  hasOwner          Boolean  @default(false)
  hasMintFunction   Boolean  @default(false)
  hasBurnFunction   Boolean  @default(false)
  hasPauseFunction  Boolean  @default(false)
  hasBlacklist      Boolean  @default(false)
  hasWhitelist      Boolean  @default(false)
  hasHoneypot       Boolean  @default(false)
  hasRugPull        Boolean  @default(false)
  
  // Ownership
  owner             String?
  ownershipRenounced Boolean @default(false)
  multiSig          Boolean  @default(false)
  
  // Proxy information
  isProxy           Boolean  @default(false)
  implementation    String?
  proxyType         String?
  
  // Analysis results
  functions         Json     @default("[]")
  events            Json     @default("[]")
  modifiers         Json     @default("[]")
  
  // Timestamps
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  // Relations
  token             Token    @relation(fields: [tokenId], references: [id], onDelete: Cascade)
  
  @@index([tokenId])
  @@index([hasHoneypot])
  @@index([hasRugPull])
  @@map("contract_analysis")
}

// Social media analysis
model SocialAnalysis {
  id              String   @id @default(cuid())
  tokenId         String
  sentimentScore  Float
  sentimentLabel  String
  confidence      Float
  totalMentions   Int      @default(0)
  trendingScore   Float    @default(0)
  
  // Platform breakdown
  twitterMentions Int      @default(0)
  redditMentions  Int      @default(0)
  telegramMentions Int     @default(0)
  discordMentions Int      @default(0)
  
  // Sentiment breakdown
  positiveMentions Int     @default(0)
  neutralMentions  Int     @default(0)
  negativeMentions Int     @default(0)
  
  // Influencer data
  influencerMentions Json  @default("[]")
  topMentions        Json  @default("[]")
  
  // Timestamps
  timeframe       String
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  // Relations
  token           Token    @relation(fields: [tokenId], references: [id], onDelete: Cascade)
  
  @@index([tokenId])
  @@index([sentimentScore])
  @@index([createdAt])
  @@map("social_analysis")
}

// User portfolios
model Portfolio {
  id          String   @id @default(cuid())
  userId      String
  name        String
  description String?
  isDefault   Boolean  @default(false)
  isPublic    Boolean  @default(false)
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  tokens      PortfolioToken[]
  
  @@index([userId])
  @@map("portfolios")
}

// Tokens in portfolios
model PortfolioToken {
  id            String    @id @default(cuid())
  portfolioId   String
  tokenId       String
  amount        String
  averagePrice  Float?
  
  // Timestamps
  addedAt       DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  // Relations
  portfolio     Portfolio @relation(fields: [portfolioId], references: [id], onDelete: Cascade)
  token         Token     @relation(fields: [tokenId], references: [id], onDelete: Cascade)
  
  @@unique([portfolioId, tokenId])
  @@index([portfolioId])
  @@index([tokenId])
  @@map("portfolio_tokens")
}

// User alerts
model Alert {
  id          String      @id @default(cuid())
  userId      String
  tokenId     String
  type        AlertType
  condition   Json
  isActive    Boolean     @default(true)
  triggered   Boolean     @default(false)
  triggeredAt DateTime?
  
  // Timestamps
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  
  // Relations
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  token       Token       @relation(fields: [tokenId], references: [id], onDelete: Cascade)
  
  @@index([userId])
  @@index([tokenId])
  @@index([isActive])
  @@map("alerts")
}

// User watchlists
model Watchlist {
  id          String   @id @default(cuid())
  userId      String
  name        String
  description String?
  isPublic    Boolean  @default(false)
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  tokens      WatchlistToken[]
  
  @@index([userId])
  @@map("watchlists")
}

// Tokens in watchlists
model WatchlistToken {
  id          String    @id @default(cuid())
  watchlistId String
  tokenId     String
  
  // Timestamps
  addedAt     DateTime  @default(now())
  
  // Relations
  watchlist   Watchlist @relation(fields: [watchlistId], references: [id], onDelete: Cascade)
  token       Token     @relation(fields: [tokenId], references: [id], onDelete: Cascade)
  
  @@unique([watchlistId, tokenId])
  @@index([watchlistId])
  @@index([tokenId])
  @@map("watchlist_tokens")
}

// Analysis history for tracking user requests
model AnalysisHistory {
  id          String   @id @default(cuid())
  userId      String?
  tokenId     String
  analysisType String
  parameters  Json     @default("{}")
  results     Json?
  
  // Timestamps
  createdAt   DateTime @default(now())
  
  // Relations
  user        User?    @relation(fields: [userId], references: [id], onDelete: SetNull)
  token       Token    @relation(fields: [tokenId], references: [id], onDelete: Cascade)
  
  @@index([userId])
  @@index([tokenId])
  @@index([createdAt])
  @@map("analysis_history")
}

// Enums
enum Role {
  USER
  ADMIN
  MODERATOR
}

enum UserStatus {
  ACTIVE
  SUSPENDED
  BANNED
}

enum RiskLevel {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum AlertType {
  PRICE_ABOVE
  PRICE_BELOW
  RISK_INCREASE
  RISK_DECREASE
  VOLUME_SPIKE
  NEW_HOLDER
  WHALE_MOVEMENT
}
