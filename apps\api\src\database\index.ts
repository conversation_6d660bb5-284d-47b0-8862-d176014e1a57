/**
 * TokenForge Database Connection
 * Centralized database connection and Prisma client management
 */

import { PrismaClient } from '@prisma/client';
import { logger } from '@tokenforge/shared';

// Extend PrismaClient with custom methods
class DatabaseClient extends PrismaClient {
  constructor() {
    super({
      log: [
        { level: 'query', emit: 'event' },
        { level: 'error', emit: 'event' },
        { level: 'info', emit: 'event' },
        { level: 'warn', emit: 'event' },
      ],
      errorFormat: 'pretty',
    });

    // Log database queries in development
    if (process.env.NODE_ENV === 'development') {
      this.$on('query', (e) => {
        logger.debug('Database Query', {
          query: e.query,
          params: e.params,
          duration: `${e.duration}ms`,
        });
      });
    }

    // Log database errors
    this.$on('error', (e) => {
      logger.error('Database Error', { target: e.target }, new Error(e.message));
    });

    // Log database info
    this.$on('info', (e) => {
      logger.info('Database Info', { message: e.message, target: e.target });
    });

    // Log database warnings
    this.$on('warn', (e) => {
      logger.warn('Database Warning', { message: e.message, target: e.target });
    });
  }

  /**
   * Health check for database connection
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    latency?: number;
    error?: string;
  }> {
    try {
      const start = Date.now();
      await this.$queryRaw`SELECT 1`;
      const latency = Date.now() - start;

      return {
        status: 'healthy',
        latency,
      };
    } catch (error) {
      logger.error('Database health check failed', {}, error as Error);
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get database statistics
   */
  async getStats(): Promise<{
    users: number;
    tokens: number;
    portfolios: number;
    alerts: number;
    analysisHistory: number;
  }> {
    try {
      const [users, tokens, portfolios, alerts, analysisHistory] = await Promise.all([
        this.user.count(),
        this.token.count(),
        this.portfolio.count(),
        this.alert.count(),
        this.analysisHistory.count(),
      ]);

      return {
        users,
        tokens,
        portfolios,
        alerts,
        analysisHistory,
      };
    } catch (error) {
      logger.error('Failed to get database stats', {}, error as Error);
      throw error;
    }
  }

  /**
   * Clean up expired sessions and tokens
   */
  async cleanupExpired(): Promise<{
    sessions: number;
    apiKeys: number;
    passwordResets: number;
    emailVerifications: number;
  }> {
    try {
      const now = new Date();

      const [sessions, apiKeys, passwordResets, emailVerifications] = await Promise.all([
        // Clean expired sessions
        this.userSession.deleteMany({
          where: {
            expiresAt: {
              lt: now,
            },
          },
        }),

        // Clean expired API keys
        this.apiKey.deleteMany({
          where: {
            expiresAt: {
              lt: now,
            },
          },
        }),

        // Clean expired password reset tokens
        this.user.updateMany({
          where: {
            passwordResetExpires: {
              lt: now,
            },
          },
          data: {
            passwordResetToken: null,
            passwordResetExpires: null,
          },
        }),

        // Clean expired email verification tokens
        this.user.updateMany({
          where: {
            emailVerificationExpires: {
              lt: now,
            },
          },
          data: {
            emailVerificationToken: null,
            emailVerificationExpires: null,
          },
        }),
      ]);

      logger.info('Database cleanup completed', {
        sessions: sessions.count,
        apiKeys: apiKeys.count,
        passwordResets: passwordResets.count,
        emailVerifications: emailVerifications.count,
      });

      return {
        sessions: sessions.count,
        apiKeys: apiKeys.count,
        passwordResets: passwordResets.count,
        emailVerifications: emailVerifications.count,
      };
    } catch (error) {
      logger.error('Database cleanup failed', {}, error as Error);
      throw error;
    }
  }

  /**
   * Reset API request counters (called monthly)
   */
  async resetApiRequestCounters(): Promise<number> {
    try {
      const result = await this.user.updateMany({
        data: {
          apiRequestsCount: 0,
          apiRequestsResetAt: new Date(),
        },
      });

      logger.info('API request counters reset', { count: result.count });
      return result.count;
    } catch (error) {
      logger.error('Failed to reset API request counters', {}, error as Error);
      throw error;
    }
  }

  /**
   * Get user by email or username
   */
  async getUserByEmailOrUsername(identifier: string) {
    return this.user.findFirst({
      where: {
        OR: [
          { email: identifier },
          { username: identifier },
        ],
      },
    });
  }

  /**
   * Get token by address and chain
   */
  async getTokenByAddressAndChain(address: string, chain: string) {
    return this.token.findUnique({
      where: {
        address_chain: {
          address: address.toLowerCase(),
          chain: chain.toLowerCase(),
        },
      },
      include: {
        metrics: {
          orderBy: { updatedAt: 'desc' },
          take: 1,
        },
        riskAssessments: {
          orderBy: { updatedAt: 'desc' },
          take: 1,
        },
      },
    });
  }

  /**
   * Create or update token metrics
   */
  async upsertTokenMetrics(tokenId: string, metrics: any) {
    // Delete old metrics (keep only latest 100 records per token)
    await this.$executeRaw`
      DELETE FROM token_metrics 
      WHERE token_id = ${tokenId} 
      AND id NOT IN (
        SELECT id FROM token_metrics 
        WHERE token_id = ${tokenId} 
        ORDER BY created_at DESC 
        LIMIT 100
      )
    `;

    // Create new metrics record
    return this.tokenMetrics.create({
      data: {
        tokenId,
        ...metrics,
      },
    });
  }

  /**
   * Get portfolio with calculated values
   */
  async getPortfolioWithValues(portfolioId: string) {
    return this.portfolio.findUnique({
      where: { id: portfolioId },
      include: {
        tokens: {
          include: {
            token: {
              include: {
                metrics: {
                  orderBy: { updatedAt: 'desc' },
                  take: 1,
                },
                riskAssessments: {
                  orderBy: { updatedAt: 'desc' },
                  take: 1,
                },
              },
            },
          },
        },
      },
    });
  }

  /**
   * Graceful disconnect
   */
  async gracefulDisconnect(): Promise<void> {
    try {
      await this.$disconnect();
      logger.info('Database connection closed gracefully');
    } catch (error) {
      logger.error('Error during database disconnect', {}, error as Error);
    }
  }
}

// Create singleton instance
const db = new DatabaseClient();

// Handle process termination
process.on('SIGINT', async () => {
  await db.gracefulDisconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await db.gracefulDisconnect();
  process.exit(0);
});

export { db };
export default db;
