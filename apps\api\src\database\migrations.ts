/**
 * TokenForge Database Migration Utilities
 * Helper functions for database migrations and schema updates
 */

import { PrismaClient } from '@prisma/client';
import { logger } from '@tokenforge/shared';

const prisma = new PrismaClient();

/**
 * Check if database is ready and accessible
 */
export async function checkDatabaseConnection(): Promise<boolean> {
  try {
    await prisma.$queryRaw`SELECT 1`;
    logger.info('Database connection successful');
    return true;
  } catch (error) {
    logger.error('Database connection failed', {}, error as Error);
    return false;
  }
}

/**
 * Run database migrations
 */
export async function runMigrations(): Promise<void> {
  try {
    logger.info('Running database migrations...');
    
    // In a real application, you would use Prisma's migration system
    // This is a placeholder for custom migration logic
    
    // Example: Create indexes if they don't exist
    await createIndexesIfNotExists();
    
    // Example: Update existing data
    await updateExistingData();
    
    logger.info('Database migrations completed successfully');
  } catch (error) {
    logger.error('Database migration failed', {}, error as Error);
    throw error;
  }
}

/**
 * Create database indexes if they don't exist
 */
async function createIndexesIfNotExists(): Promise<void> {
  try {
    // Create composite indexes for better query performance
    const indexes = [
      {
        table: 'tokens',
        name: 'idx_tokens_chain_symbol',
        columns: ['chain', 'symbol'],
      },
      {
        table: 'token_metrics',
        name: 'idx_token_metrics_token_updated',
        columns: ['token_id', 'updated_at'],
      },
      {
        table: 'risk_assessments',
        name: 'idx_risk_assessments_token_created',
        columns: ['token_id', 'created_at'],
      },
      {
        table: 'analysis_history',
        name: 'idx_analysis_history_user_created',
        columns: ['user_id', 'created_at'],
      },
      {
        table: 'audit_logs',
        name: 'idx_audit_logs_user_action_created',
        columns: ['user_id', 'action', 'created_at'],
      },
    ];

    for (const index of indexes) {
      try {
        await prisma.$executeRawUnsafe(`
          CREATE INDEX IF NOT EXISTS ${index.name} 
          ON ${index.table} (${index.columns.join(', ')})
        `);
        logger.debug(`Created index: ${index.name}`);
      } catch (error) {
        // Index might already exist, log but don't fail
        logger.debug(`Index ${index.name} already exists or failed to create`);
      }
    }
  } catch (error) {
    logger.error('Failed to create indexes', {}, error as Error);
    throw error;
  }
}

/**
 * Update existing data for new schema changes
 */
async function updateExistingData(): Promise<void> {
  try {
    // Example: Set default values for new columns
    await prisma.$executeRaw`
      UPDATE users 
      SET subscription_status = 'NONE', subscription_plan = 'FREE' 
      WHERE subscription_status IS NULL OR subscription_plan IS NULL
    `;

    // Example: Normalize existing token addresses
    await prisma.$executeRaw`
      UPDATE tokens 
      SET address = LOWER(address), chain = LOWER(chain)
      WHERE address != LOWER(address) OR chain != LOWER(chain)
    `;

    logger.debug('Updated existing data for schema changes');
  } catch (error) {
    logger.error('Failed to update existing data', {}, error as Error);
    throw error;
  }
}

/**
 * Backup database before major changes
 */
export async function createBackup(backupName?: string): Promise<string> {
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const name = backupName || `tokenforge_backup_${timestamp}`;
    
    // This would typically use pg_dump or similar tool
    // For now, we'll just log the backup creation
    logger.info(`Creating database backup: ${name}`);
    
    // In production, you would execute something like:
    // await exec(`pg_dump ${process.env.DATABASE_URL} > backups/${name}.sql`);
    
    return name;
  } catch (error) {
    logger.error('Failed to create database backup', {}, error as Error);
    throw error;
  }
}

/**
 * Restore database from backup
 */
export async function restoreBackup(backupName: string): Promise<void> {
  try {
    logger.info(`Restoring database from backup: ${backupName}`);
    
    // In production, you would execute something like:
    // await exec(`psql ${process.env.DATABASE_URL} < backups/${backupName}.sql`);
    
    logger.info('Database restore completed');
  } catch (error) {
    logger.error('Failed to restore database backup', {}, error as Error);
    throw error;
  }
}

/**
 * Clean up old data based on retention policies
 */
export async function cleanupOldData(): Promise<{
  auditLogs: number;
  analysisHistory: number;
  tokenMetrics: number;
  userSessions: number;
}> {
  try {
    const retentionDays = {
      auditLogs: 90,
      analysisHistory: 30,
      tokenMetrics: 365,
      userSessions: 30,
    };

    const cutoffDates = {
      auditLogs: new Date(Date.now() - retentionDays.auditLogs * 24 * 60 * 60 * 1000),
      analysisHistory: new Date(Date.now() - retentionDays.analysisHistory * 24 * 60 * 60 * 1000),
      tokenMetrics: new Date(Date.now() - retentionDays.tokenMetrics * 24 * 60 * 60 * 1000),
      userSessions: new Date(Date.now() - retentionDays.userSessions * 24 * 60 * 60 * 1000),
    };

    const [auditLogs, analysisHistory, tokenMetrics, userSessions] = await Promise.all([
      prisma.auditLog.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDates.auditLogs,
          },
        },
      }),
      prisma.analysisHistory.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDates.analysisHistory,
          },
        },
      }),
      prisma.tokenMetrics.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDates.tokenMetrics,
          },
        },
      }),
      prisma.userSession.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDates.userSessions,
          },
        },
      }),
    ]);

    const result = {
      auditLogs: auditLogs.count,
      analysisHistory: analysisHistory.count,
      tokenMetrics: tokenMetrics.count,
      userSessions: userSessions.count,
    };

    logger.info('Old data cleanup completed', result);
    return result;
  } catch (error) {
    logger.error('Failed to cleanup old data', {}, error as Error);
    throw error;
  }
}

/**
 * Optimize database performance
 */
export async function optimizeDatabase(): Promise<void> {
  try {
    logger.info('Starting database optimization...');

    // Analyze tables for better query planning
    const tables = [
      'users', 'tokens', 'token_metrics', 'risk_assessments',
      'portfolios', 'portfolio_tokens', 'alerts', 'analysis_history',
      'audit_logs', 'api_keys', 'user_sessions'
    ];

    for (const table of tables) {
      try {
        await prisma.$executeRawUnsafe(`ANALYZE ${table}`);
        logger.debug(`Analyzed table: ${table}`);
      } catch (error) {
        logger.warn(`Failed to analyze table: ${table}`, { error });
      }
    }

    // Vacuum tables to reclaim space
    for (const table of tables) {
      try {
        await prisma.$executeRawUnsafe(`VACUUM ${table}`);
        logger.debug(`Vacuumed table: ${table}`);
      } catch (error) {
        logger.warn(`Failed to vacuum table: ${table}`, { error });
      }
    }

    logger.info('Database optimization completed');
  } catch (error) {
    logger.error('Database optimization failed', {}, error as Error);
    throw error;
  }
}

/**
 * Get database size and statistics
 */
export async function getDatabaseStats(): Promise<{
  size: string;
  tables: Array<{
    name: string;
    rows: number;
    size: string;
  }>;
}> {
  try {
    // Get database size
    const sizeResult = await prisma.$queryRaw<Array<{ size: string }>>`
      SELECT pg_size_pretty(pg_database_size(current_database())) as size
    `;

    // Get table statistics
    const tableStats = await prisma.$queryRaw<Array<{
      table_name: string;
      row_count: bigint;
      size: string;
    }>>`
      SELECT 
        schemaname||'.'||tablename as table_name,
        n_tup_ins + n_tup_upd - n_tup_del as row_count,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
      FROM pg_stat_user_tables 
      ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
    `;

    return {
      size: sizeResult[0]?.size || '0 bytes',
      tables: tableStats.map(stat => ({
        name: stat.table_name,
        rows: Number(stat.row_count),
        size: stat.size,
      })),
    };
  } catch (error) {
    logger.error('Failed to get database stats', {}, error as Error);
    throw error;
  }
}

/**
 * Initialize database with required data
 */
export async function initializeDatabase(): Promise<void> {
  try {
    logger.info('Initializing database...');

    // Check if database is already initialized
    const userCount = await prisma.user.count();
    if (userCount > 0) {
      logger.info('Database already initialized');
      return;
    }

    // Run migrations
    await runMigrations();

    // Create initial admin user if none exists
    const adminExists = await prisma.user.findFirst({
      where: { role: 'ADMIN' },
    });

    if (!adminExists) {
      await prisma.user.create({
        data: {
          email: '<EMAIL>',
          username: 'admin',
          passwordHash: 'change_me_in_production',
          firstName: 'Admin',
          lastName: 'User',
          role: 'ADMIN',
          status: 'ACTIVE',
          emailVerified: true,
          subscriptionPlan: 'ENTERPRISE',
          subscriptionStatus: 'ACTIVE',
        },
      });
      logger.info('Created initial admin user');
    }

    logger.info('Database initialization completed');
  } catch (error) {
    logger.error('Database initialization failed', {}, error as Error);
    throw error;
  }
}

// Cleanup on process exit
process.on('beforeExit', async () => {
  await prisma.$disconnect();
});
