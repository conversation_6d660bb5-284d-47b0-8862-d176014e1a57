import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';

const prisma = new PrismaClient();

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        userId: string;
        address: string;
        isPremium?: boolean;
        isVerified?: boolean;
      };
    }
  }
}

interface JWTPayload {
  userId: string;
  address: string;
  iat: number;
  exp: number;
}

/**
 * Authentication middleware to verify JWT tokens
 */
export const authMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: 'Access token required',
      });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    if (!token) {
      return res.status(401).json({
        success: false,
        error: 'Access token required',
      });
    }

    // Verify JWT token
    let decoded: JWTPayload;
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET!) as JWTPayload;
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        return res.status(401).json({
          success: false,
          error: 'Token expired',
        });
      } else if (error.name === 'JsonWebTokenError') {
        return res.status(401).json({
          success: false,
          error: 'Invalid token',
        });
      } else {
        throw error;
      }
    }

    // Get user from database to ensure they still exist and get latest info
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        address: true,
        isPremium: true,
        isVerified: true,
        status: true,
      },
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'User not found',
      });
    }

    // Check if user is active
    if (user.status !== 'active') {
      return res.status(403).json({
        success: false,
        error: 'Account suspended or banned',
      });
    }

    // Verify address matches (additional security check)
    if (user.address.toLowerCase() !== decoded.address.toLowerCase()) {
      return res.status(401).json({
        success: false,
        error: 'Token address mismatch',
      });
    }

    // Add user info to request object
    req.user = {
      userId: user.id,
      address: user.address,
      isPremium: user.isPremium,
      isVerified: user.isVerified,
    };

    next();
  } catch (error) {
    logger.error('Authentication middleware error', { 
      error: error.message,
      stack: error.stack,
    });
    
    res.status(500).json({
      success: false,
      error: 'Authentication failed',
    });
  }
};

/**
 * Optional authentication middleware - doesn't fail if no token provided
 */
export const optionalAuthMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next(); // Continue without authentication
    }

    const token = authHeader.substring(7);

    if (!token) {
      return next(); // Continue without authentication
    }

    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as JWTPayload;
      
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: {
          id: true,
          address: true,
          isPremium: true,
          isVerified: true,
          status: true,
        },
      });

      if (user && user.status === 'active') {
        req.user = {
          userId: user.id,
          address: user.address,
          isPremium: user.isPremium,
          isVerified: user.isVerified,
        };
      }
    } catch (error) {
      // Ignore token errors in optional auth
      logger.debug('Optional auth token error', { error: error.message });
    }

    next();
  } catch (error) {
    logger.error('Optional authentication middleware error', { 
      error: error.message,
    });
    next(); // Continue even if there's an error
  }
};

/**
 * Premium user middleware - requires premium subscription
 */
export const premiumMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required',
    });
  }

  if (!req.user.isPremium) {
    return res.status(403).json({
      success: false,
      error: 'Premium subscription required',
      code: 'PREMIUM_REQUIRED',
    });
  }

  next();
};

/**
 * Admin middleware - requires admin role
 */
export const adminMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required',
      });
    }

    const user = await prisma.user.findUnique({
      where: { id: req.user.userId },
      select: { role: true },
    });

    if (!user || user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Admin access required',
      });
    }

    next();
  } catch (error) {
    logger.error('Admin middleware error', { 
      error: error.message,
      userId: req.user?.userId,
    });
    
    res.status(500).json({
      success: false,
      error: 'Authorization check failed',
    });
  }
};

/**
 * Rate limiting middleware for authenticated users
 */
export const createAuthRateLimit = (windowMs: number, max: number) => {
  const requests = new Map<string, { count: number; resetTime: number }>();

  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(); // Skip rate limiting for unauthenticated requests
    }

    const userId = req.user.userId;
    const now = Date.now();
    const windowStart = now - windowMs;

    // Clean up old entries
    for (const [key, value] of requests.entries()) {
      if (value.resetTime < now) {
        requests.delete(key);
      }
    }

    const userRequests = requests.get(userId);

    if (!userRequests) {
      requests.set(userId, { count: 1, resetTime: now + windowMs });
      return next();
    }

    if (userRequests.resetTime < now) {
      // Reset window
      requests.set(userId, { count: 1, resetTime: now + windowMs });
      return next();
    }

    if (userRequests.count >= max) {
      return res.status(429).json({
        success: false,
        error: 'Too many requests',
        retryAfter: Math.ceil((userRequests.resetTime - now) / 1000),
      });
    }

    userRequests.count++;
    next();
  };
};

export default authMiddleware;
