/**
 * TokenForge Error Handler Middleware
 * Centralized error handling for the API
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '@tokenforge/shared';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';

export interface ApiError extends Error {
  statusCode?: number;
  code?: string;
  details?: any;
  isOperational?: boolean;
}

/**
 * Custom error class for API errors
 */
export class AppError extends Error implements ApiError {
  public statusCode: number;
  public code: string;
  public isOperational: boolean;
  public details?: any;

  constructor(
    message: string,
    statusCode: number = 500,
    code: string = 'INTERNAL_ERROR',
    details?: any
  ) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = true;
    this.details = details;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Create standardized error response
 */
function createErrorResponse(error: ApiError, req: Request) {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  return {
    success: false,
    error: {
      code: error.code || 'INTERNAL_ERROR',
      message: error.message,
      ...(error.details && { details: error.details }),
      ...(isDevelopment && error.stack && { stack: error.stack }),
    },
    timestamp: new Date().toISOString(),
    path: req.path,
    method: req.method,
  };
}

/**
 * Handle Prisma database errors
 */
function handlePrismaError(error: PrismaClientKnownRequestError): ApiError {
  switch (error.code) {
    case 'P2002':
      return new AppError(
        'A record with this value already exists',
        409,
        'DUPLICATE_RECORD',
        { field: error.meta?.target }
      );
    
    case 'P2025':
      return new AppError(
        'Record not found',
        404,
        'RECORD_NOT_FOUND'
      );
    
    case 'P2003':
      return new AppError(
        'Foreign key constraint failed',
        400,
        'FOREIGN_KEY_CONSTRAINT'
      );
    
    case 'P2014':
      return new AppError(
        'Invalid ID provided',
        400,
        'INVALID_ID'
      );
    
    default:
      return new AppError(
        'Database operation failed',
        500,
        'DATABASE_ERROR',
        { prismaCode: error.code }
      );
  }
}

/**
 * Handle validation errors
 */
function handleValidationError(error: any): ApiError {
  if (error.name === 'ValidationError') {
    return new AppError(
      'Validation failed',
      400,
      'VALIDATION_ERROR',
      error.details
    );
  }
  
  if (error.name === 'ZodError') {
    return new AppError(
      'Invalid input data',
      400,
      'VALIDATION_ERROR',
      error.errors
    );
  }
  
  return new AppError(error.message, 400, 'VALIDATION_ERROR');
}

/**
 * Handle JWT errors
 */
function handleJWTError(error: any): ApiError {
  if (error.name === 'JsonWebTokenError') {
    return new AppError(
      'Invalid token',
      401,
      'INVALID_TOKEN'
    );
  }
  
  if (error.name === 'TokenExpiredError') {
    return new AppError(
      'Token has expired',
      401,
      'TOKEN_EXPIRED'
    );
  }
  
  return new AppError(
    'Authentication failed',
    401,
    'AUTH_ERROR'
  );
}

/**
 * Handle rate limiting errors
 */
function handleRateLimitError(error: any): ApiError {
  return new AppError(
    'Too many requests, please try again later',
    429,
    'RATE_LIMIT_EXCEEDED',
    {
      retryAfter: error.retryAfter,
      limit: error.limit,
      remaining: error.remaining,
    }
  );
}

/**
 * Main error handler middleware
 */
export function errorHandler(
  error: any,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  let apiError: ApiError;

  // Handle known error types
  if (error instanceof AppError) {
    apiError = error;
  } else if (error instanceof PrismaClientKnownRequestError) {
    apiError = handlePrismaError(error);
  } else if (error.name === 'ValidationError' || error.name === 'ZodError') {
    apiError = handleValidationError(error);
  } else if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
    apiError = handleJWTError(error);
  } else if (error.name === 'RateLimitError') {
    apiError = handleRateLimitError(error);
  } else {
    // Unknown error
    apiError = new AppError(
      process.env.NODE_ENV === 'production' 
        ? 'Something went wrong' 
        : error.message || 'Internal server error',
      error.statusCode || 500,
      error.code || 'INTERNAL_ERROR'
    );
  }

  // Log error
  const logData = {
    error: {
      message: apiError.message,
      code: apiError.code,
      statusCode: apiError.statusCode,
      stack: apiError.stack,
    },
    request: {
      method: req.method,
      url: req.url,
      headers: req.headers,
      body: req.body,
      params: req.params,
      query: req.query,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    },
    user: (req as any).user?.id,
  };

  if (apiError.statusCode >= 500) {
    logger.error('API Error', logData, apiError);
  } else {
    logger.warn('API Warning', logData);
  }

  // Send error response
  const errorResponse = createErrorResponse(apiError, req);
  res.status(apiError.statusCode).json(errorResponse);
}

/**
 * Async error wrapper for route handlers
 */
export function asyncHandler(fn: Function) {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

/**
 * Not found handler middleware
 */
export function notFoundHandler(req: Request, res: Response, next: NextFunction): void {
  const error = new AppError(
    `Route ${req.method} ${req.path} not found`,
    404,
    'ROUTE_NOT_FOUND'
  );
  next(error);
}

/**
 * Validation middleware factory
 */
export function validateRequest(schema: any, property: 'body' | 'query' | 'params' = 'body') {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const validated = schema.parse(req[property]);
      req[property] = validated;
      next();
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Create API response helper
 */
export function createResponse(data: any, message?: string, statusCode: number = 200) {
  return {
    success: true,
    data,
    message,
    timestamp: new Date().toISOString(),
  };
}

/**
 * Send success response helper
 */
export function sendSuccess(
  res: Response,
  data: any,
  message?: string,
  statusCode: number = 200
) {
  res.status(statusCode).json(createResponse(data, message, statusCode));
}

/**
 * Send paginated response helper
 */
export function sendPaginatedResponse(
  res: Response,
  data: any[],
  page: number,
  limit: number,
  total: number,
  message?: string
) {
  const pages = Math.ceil(total / limit);
  
  res.json({
    success: true,
    data,
    message,
    pagination: {
      page,
      limit,
      total,
      pages,
      hasNext: page < pages,
      hasPrev: page > 1,
    },
    timestamp: new Date().toISOString(),
  });
}

/**
 * Handle async route errors
 */
export function handleAsync(fn: Function) {
  return (req: Request, res: Response, next: NextFunction) => {
    fn(req, res, next).catch(next);
  };
}
