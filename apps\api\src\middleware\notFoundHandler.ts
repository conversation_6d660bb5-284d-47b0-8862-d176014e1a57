/**
 * TokenForge Not Found Handler Middleware
 * Handles 404 errors for undefined routes
 */

import { Request, Response, NextFunction } from 'express';
import { AppError } from './errorHandler';

/**
 * Not found handler middleware
 */
export function notFoundHandler(req: Request, res: Response, next: NextFunction): void {
  const error = new AppError(
    `Route ${req.method} ${req.path} not found`,
    404,
    'ROUTE_NOT_FOUND',
    {
      method: req.method,
      path: req.path,
      originalUrl: req.originalUrl,
    }
  );
  
  next(error);
}
