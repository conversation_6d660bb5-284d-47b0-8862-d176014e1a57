/**
 * TokenForge Rate Limiting Middleware
 * Advanced rate limiting with Redis backend and subscription-based limits
 */

import { Request, Response, NextFunction } from 'express';
import { redisManager } from '../../../../config/redis';
import { logger } from '@tokenforge/shared';
import { AppError } from './errorHandler';
import { db } from '../database';

interface RateLimitOptions {
  windowMs: number;
  maxRequests: number;
  keyGenerator?: (req: Request) => string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  message?: string;
  headers?: boolean;
}

interface RateLimitInfo {
  totalHits: number;
  totalHitsPerWindow: number;
  resetTime: Date;
  remaining: number;
}

/**
 * Create rate limiter middleware
 */
export function createRateLimit(options: RateLimitOptions) {
  const {
    windowMs,
    maxRequests,
    keyGenerator = (req) => req.ip,
    skipSuccessfulRequests = false,
    skipFailedRequests = false,
    message = 'Too many requests, please try again later',
    headers = true,
  } = options;

  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const key = `ratelimit:${keyGenerator(req)}`;
      const now = Date.now();
      const windowStart = now - windowMs;

      // Get current count from Redis
      const redis = redisManager.getClient();
      const current = await redis.get(key);
      const currentCount = current ? parseInt(current, 10) : 0;

      // Check if limit exceeded
      if (currentCount >= maxRequests) {
        const resetTime = new Date(now + windowMs);
        
        if (headers) {
          res.set({
            'X-RateLimit-Limit': maxRequests.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': Math.ceil(resetTime.getTime() / 1000).toString(),
            'Retry-After': Math.ceil(windowMs / 1000).toString(),
          });
        }

        // Log rate limit hit
        logger.warn('Rate limit exceeded', {
          key,
          currentCount,
          maxRequests,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          path: req.path,
        });

        throw new AppError(message, 429, 'RATE_LIMIT_EXCEEDED', {
          retryAfter: Math.ceil(windowMs / 1000),
          limit: maxRequests,
          remaining: 0,
          resetTime: resetTime.getTime(),
        });
      }

      // Increment counter
      const multi = redis.multi();
      multi.incr(key);
      multi.expire(key, Math.ceil(windowMs / 1000));
      await multi.exec();

      const remaining = Math.max(0, maxRequests - currentCount - 1);

      if (headers) {
        res.set({
          'X-RateLimit-Limit': maxRequests.toString(),
          'X-RateLimit-Remaining': remaining.toString(),
          'X-RateLimit-Reset': Math.ceil((now + windowMs) / 1000).toString(),
        });
      }

      // Add rate limit info to request
      (req as any).rateLimit = {
        totalHits: currentCount + 1,
        totalHitsPerWindow: currentCount + 1,
        resetTime: new Date(now + windowMs),
        remaining,
      } as RateLimitInfo;

      next();
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        logger.error('Rate limiter error', {}, error as Error);
        // Continue without rate limiting if Redis fails
        next();
      }
    }
  };
}

/**
 * Subscription-based rate limiter
 */
export function createSubscriptionRateLimit(options: Omit<RateLimitOptions, 'maxRequests'>) {
  const {
    windowMs,
    keyGenerator = (req) => (req as any).user?.id || req.ip,
    message = 'Rate limit exceeded for your subscription tier',
    headers = true,
  } = options;

  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const user = (req as any).user;
      let maxRequests = 100; // Default for unauthenticated users

      // Get user's subscription limits
      if (user) {
        const userRecord = await db.user.findUnique({
          where: { id: user.id },
          select: { subscriptionPlan: true, subscriptionStatus: true },
        });

        if (userRecord) {
          switch (userRecord.subscriptionPlan) {
            case 'FREE':
              maxRequests = 100;
              break;
            case 'PRO':
              maxRequests = 1000;
              break;
            case 'ENTERPRISE':
              maxRequests = 10000;
              break;
            default:
              maxRequests = 100;
          }

          // Reduce limits for inactive subscriptions
          if (userRecord.subscriptionStatus !== 'ACTIVE' && userRecord.subscriptionPlan !== 'FREE') {
            maxRequests = 100; // Fall back to free tier limits
          }
        }
      }

      // Apply rate limiting with subscription-specific limits
      const rateLimiter = createRateLimit({
        windowMs,
        maxRequests,
        keyGenerator,
        message,
        headers,
      });

      return rateLimiter(req, res, next);
    } catch (error) {
      logger.error('Subscription rate limiter error', {}, error as Error);
      next();
    }
  };
}

/**
 * API key rate limiter
 */
export function createApiKeyRateLimit(options: Omit<RateLimitOptions, 'maxRequests' | 'keyGenerator'>) {
  const {
    windowMs,
    message = 'API key rate limit exceeded',
    headers = true,
  } = options;

  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const apiKey = req.get('X-API-Key') || req.get('Authorization')?.replace('Bearer ', '');
      
      if (!apiKey) {
        return next();
      }

      // Get API key details
      const apiKeyRecord = await db.apiKey.findUnique({
        where: { key: apiKey },
        include: {
          user: {
            select: { subscriptionPlan: true, subscriptionStatus: true },
          },
        },
      });

      if (!apiKeyRecord || !apiKeyRecord.isActive) {
        throw new AppError('Invalid API key', 401, 'INVALID_API_KEY');
      }

      // Determine rate limits based on subscription
      let maxRequests = 100;
      switch (apiKeyRecord.user.subscriptionPlan) {
        case 'PRO':
          maxRequests = 1000;
          break;
        case 'ENTERPRISE':
          maxRequests = 10000;
          break;
      }

      // Apply rate limiting
      const rateLimiter = createRateLimit({
        windowMs,
        maxRequests,
        keyGenerator: () => `apikey:${apiKeyRecord.id}`,
        message,
        headers,
      });

      // Update API key usage
      await db.apiKey.update({
        where: { id: apiKeyRecord.id },
        data: {
          lastUsedAt: new Date(),
          requestCount: { increment: 1 },
        },
      });

      return rateLimiter(req, res, next);
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        logger.error('API key rate limiter error', {}, error as Error);
        next();
      }
    }
  };
}

/**
 * Endpoint-specific rate limiters
 */
export const rateLimiters = {
  // General API rate limiting
  general: createRateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 1000,
    message: 'Too many requests from this IP',
  }),

  // Authentication endpoints
  auth: createRateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 10,
    message: 'Too many authentication attempts',
  }),

  // Token analysis endpoints
  analysis: createSubscriptionRateLimit({
    windowMs: 60 * 1000, // 1 minute
    message: 'Analysis rate limit exceeded',
  }),

  // Search endpoints
  search: createRateLimit({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 60,
    message: 'Search rate limit exceeded',
  }),

  // Admin endpoints
  admin: createRateLimit({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100,
    keyGenerator: (req) => `admin:${(req as any).user?.id || req.ip}`,
    message: 'Admin rate limit exceeded',
  }),
};

/**
 * Reset rate limit for a specific key
 */
export async function resetRateLimit(key: string): Promise<void> {
  try {
    const redis = redisManager.getClient();
    await redis.del(`ratelimit:${key}`);
    logger.info('Rate limit reset', { key });
  } catch (error) {
    logger.error('Failed to reset rate limit', { key }, error as Error);
  }
}

/**
 * Get rate limit status for a key
 */
export async function getRateLimitStatus(key: string, windowMs: number, maxRequests: number): Promise<RateLimitInfo> {
  try {
    const redis = redisManager.getClient();
    const current = await redis.get(`ratelimit:${key}`);
    const currentCount = current ? parseInt(current, 10) : 0;
    const ttl = await redis.ttl(`ratelimit:${key}`);
    
    const resetTime = new Date(Date.now() + (ttl > 0 ? ttl * 1000 : windowMs));
    const remaining = Math.max(0, maxRequests - currentCount);

    return {
      totalHits: currentCount,
      totalHitsPerWindow: currentCount,
      resetTime,
      remaining,
    };
  } catch (error) {
    logger.error('Failed to get rate limit status', { key }, error as Error);
    throw error;
  }
}
