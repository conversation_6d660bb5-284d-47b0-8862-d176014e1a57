/**
 * TokenForge Security Middleware
 * Comprehensive security middleware for API protection
 */

import { Request, Response, NextFunction } from 'express';
import helmet from 'helmet';
import cors from 'cors';
import { logger } from '@tokenforge/shared';
import { AppError } from './errorHandler';
import { db } from '../database';

/**
 * CORS configuration
 */
export const corsOptions = {
  origin: (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) => {
    const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || [
      'http://localhost:3000',
      'https://tokenforge.ai',
      'https://www.tokenforge.ai',
    ];

    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) return callback(null, true);

    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      logger.warn('CORS blocked request', { origin });
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-API-Key',
    'X-Request-ID',
  ],
  exposedHeaders: [
    'X-RateLimit-Limit',
    'X-RateLimit-Remaining',
    'X-RateLimit-Reset',
    'X-Request-ID',
  ],
  maxAge: 86400, // 24 hours
};

/**
 * Helmet security configuration
 */
export const helmetOptions = {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
      fontSrc: ["'self'", 'https://fonts.gstatic.com'],
      imgSrc: ["'self'", 'data:', 'https:'],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", 'https://api.tokenforge.ai'],
    },
  },
  crossOriginEmbedderPolicy: false,
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true,
  },
};

/**
 * Request ID middleware
 */
export function requestId(req: Request, res: Response, next: NextFunction): void {
  const requestId = req.get('X-Request-ID') || 
    `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  req.headers['x-request-id'] = requestId;
  res.set('X-Request-ID', requestId);
  
  next();
}

/**
 * IP whitelist middleware
 */
export function ipWhitelist(allowedIPs: string[]) {
  return (req: Request, res: Response, next: NextFunction) => {
    const clientIP = req.ip || req.connection.remoteAddress;
    
    if (!clientIP || !allowedIPs.includes(clientIP)) {
      logger.warn('IP not whitelisted', { 
        ip: clientIP, 
        path: req.path,
        userAgent: req.get('User-Agent'),
      });
      
      throw new AppError('Access denied', 403, 'IP_NOT_ALLOWED');
    }
    
    next();
  };
}

/**
 * API key validation middleware
 */
export function validateApiKey(req: Request, res: Response, next: NextFunction): void {
  const apiKey = req.get('X-API-Key') || 
    (req.get('Authorization')?.startsWith('Bearer ') ? 
     req.get('Authorization')?.substring(7) : null);

  if (!apiKey) {
    throw new AppError('API key required', 401, 'API_KEY_REQUIRED');
  }

  // Validate API key format
  if (!/^tf_[a-fA-F0-9]{48}$/.test(apiKey)) {
    throw new AppError('Invalid API key format', 401, 'INVALID_API_KEY_FORMAT');
  }

  // Store API key in request for further validation
  (req as any).apiKey = apiKey;
  next();
}

/**
 * Input sanitization middleware
 */
export function sanitizeInput(req: Request, res: Response, next: NextFunction): void {
  // Sanitize request body
  if (req.body && typeof req.body === 'object') {
    req.body = sanitizeObject(req.body);
  }

  // Sanitize query parameters
  if (req.query && typeof req.query === 'object') {
    req.query = sanitizeObject(req.query);
  }

  next();
}

/**
 * Recursively sanitize object properties
 */
function sanitizeObject(obj: any): any {
  if (typeof obj !== 'object' || obj === null) {
    return sanitizeValue(obj);
  }

  if (Array.isArray(obj)) {
    return obj.map(sanitizeObject);
  }

  const sanitized: any = {};
  for (const [key, value] of Object.entries(obj)) {
    const sanitizedKey = sanitizeValue(key);
    sanitized[sanitizedKey] = sanitizeObject(value);
  }

  return sanitized;
}

/**
 * Sanitize individual values
 */
function sanitizeValue(value: any): any {
  if (typeof value !== 'string') {
    return value;
  }

  return value
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .trim();
}

/**
 * SQL injection protection middleware
 */
export function sqlInjectionProtection(req: Request, res: Response, next: NextFunction): void {
  const suspiciousPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi,
    /(\b(OR|AND)\s+\d+\s*=\s*\d+)/gi,
    /(--|\/\*|\*\/|;)/g,
    /(\b(SCRIPT|JAVASCRIPT|VBSCRIPT)\b)/gi,
  ];

  const checkValue = (value: any): boolean => {
    if (typeof value !== 'string') return false;
    return suspiciousPatterns.some(pattern => pattern.test(value));
  };

  const checkObject = (obj: any): boolean => {
    if (typeof obj !== 'object' || obj === null) {
      return checkValue(obj);
    }

    if (Array.isArray(obj)) {
      return obj.some(checkObject);
    }

    return Object.values(obj).some(checkObject);
  };

  if (checkObject(req.body) || checkObject(req.query) || checkObject(req.params)) {
    logger.warn('Potential SQL injection attempt detected', {
      ip: req.ip,
      path: req.path,
      body: req.body,
      query: req.query,
      params: req.params,
      userAgent: req.get('User-Agent'),
    });

    throw new AppError('Invalid input detected', 400, 'INVALID_INPUT');
  }

  next();
}

/**
 * Audit logging middleware
 */
export function auditLog(req: Request, res: Response, next: NextFunction): void {
  const startTime = Date.now();
  const originalSend = res.send;

  res.send = function(body) {
    const responseTime = Date.now() - startTime;
    const user = (req as any).user;

    // Log the request
    setImmediate(async () => {
      try {
        await db.auditLog.create({
          data: {
            userId: user?.id,
            action: `${req.method} ${req.path}`,
            resource: req.path,
            details: {
              method: req.method,
              path: req.path,
              query: req.query,
              statusCode: res.statusCode,
              responseTime,
              userAgent: req.get('User-Agent'),
              referer: req.get('Referer'),
            },
            ipAddress: req.ip,
            userAgent: req.get('User-Agent'),
            success: res.statusCode < 400,
            error: res.statusCode >= 400 ? `HTTP ${res.statusCode}` : undefined,
          },
        });
      } catch (error) {
        logger.error('Failed to create audit log', {}, error as Error);
      }
    });

    return originalSend.call(this, body);
  };

  next();
}

/**
 * Content type validation middleware
 */
export function validateContentType(allowedTypes: string[]) {
  return (req: Request, res: Response, next: NextFunction) => {
    if (req.method === 'GET' || req.method === 'DELETE') {
      return next();
    }

    const contentType = req.get('Content-Type');
    if (!contentType || !allowedTypes.some(type => contentType.includes(type))) {
      throw new AppError(
        `Invalid content type. Allowed types: ${allowedTypes.join(', ')}`,
        415,
        'INVALID_CONTENT_TYPE'
      );
    }

    next();
  };
}

/**
 * Request size limit middleware
 */
export function requestSizeLimit(maxSize: number) {
  return (req: Request, res: Response, next: NextFunction) => {
    const contentLength = parseInt(req.get('Content-Length') || '0', 10);
    
    if (contentLength > maxSize) {
      throw new AppError(
        `Request too large. Maximum size: ${maxSize} bytes`,
        413,
        'REQUEST_TOO_LARGE'
      );
    }

    next();
  };
}

/**
 * Security headers middleware
 */
export function securityHeaders(req: Request, res: Response, next: NextFunction): void {
  res.set({
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'geolocation=(), microphone=(), camera=()',
  });

  next();
}

/**
 * Combined security middleware
 */
export const securityMiddleware = [
  helmet(helmetOptions),
  cors(corsOptions),
  requestId,
  securityHeaders,
  sanitizeInput,
  sqlInjectionProtection,
  validateContentType(['application/json', 'multipart/form-data']),
  requestSizeLimit(10 * 1024 * 1024), // 10MB limit
];
