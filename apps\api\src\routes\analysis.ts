import express from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { authMiddleware, optionalAuthMiddleware, premiumMiddleware } from '../middleware/auth';
import { logger } from '../utils/logger';
import { AIServiceClient } from '../services/AIServiceClient';
import { CacheService } from '../services/CacheService';
import { rateLimit } from 'express-rate-limit';

const router = express.Router();
const prisma = new PrismaClient();
const aiService = new AIServiceClient();
const cacheService = new CacheService();

// Rate limiting for analysis endpoints
const analysisLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50, // limit each IP to 50 requests per windowMs
  message: { error: 'Too many analysis requests, please try again later.' },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * @swagger
 * /analysis/{chain}/{address}:
 *   post:
 *     summary: Analyze a token for security risks
 *     tags: [Analysis]
 *     parameters:
 *       - in: path
 *         name: chain
 *         required: true
 *         schema:
 *           type: string
 *         description: Blockchain network
 *       - in: path
 *         name: address
 *         required: true
 *         schema:
 *           type: string
 *         description: Token contract address
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               includeSocial:
 *                 type: boolean
 *                 default: true
 *               includeDeveloper:
 *                 type: boolean
 *                 default: true
 *               forceRefresh:
 *                 type: boolean
 *                 default: false
 *     responses:
 *       200:
 *         description: Analysis completed successfully
 */
router.post('/:chain/:address',
  analysisLimiter,
  optionalAuthMiddleware,
  [
    param('chain').isIn(['ethereum', 'bsc', 'polygon', 'arbitrum', 'optimism', 'solana']),
    param('address').custom((value, { req }) => {
      const chain = req.params?.chain;
      if (chain === 'solana') {
        // Solana address validation (base58, 32-44 characters)
        if (!/^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(value)) {
          throw new Error('Invalid Solana address');
        }
      } else {
        // Ethereum-like address validation
        if (!/^0x[a-fA-F0-9]{40}$/.test(value)) {
          throw new Error('Invalid Ethereum address');
        }
      }
      return true;
    }),
    body('includeSocial').optional().isBoolean(),
    body('includeDeveloper').optional().isBoolean(),
    body('forceRefresh').optional().isBoolean(),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
        });
      }

      const { chain, address } = req.params;
      const { includeSocial = true, includeDeveloper = true, forceRefresh = false } = req.body;
      const normalizedAddress = address.toLowerCase();

      // Check rate limits for non-premium users
      if (req.user && !req.user.isPremium) {
        const dailyLimit = 10;
        const today = new Date().toISOString().split('T')[0];
        const cacheKey = `analysis_count:${req.user.userId}:${today}`;
        
        const currentCount = await cacheService.get(cacheKey) || 0;
        if (currentCount >= dailyLimit) {
          return res.status(429).json({
            success: false,
            error: 'Daily analysis limit reached. Upgrade to Pro for unlimited analyses.',
            code: 'DAILY_LIMIT_EXCEEDED',
          });
        }
      }

      // Check cache first (unless force refresh)
      const cacheKey = `analysis:${chain}:${normalizedAddress}:${includeSocial}:${includeDeveloper}`;
      if (!forceRefresh) {
        const cachedResult = await cacheService.get(cacheKey);
        if (cachedResult) {
          logger.info('Returning cached analysis', { 
            chain, 
            address: normalizedAddress,
            userId: req.user?.userId,
          });
          
          return res.json({
            success: true,
            data: cachedResult,
            cached: true,
          });
        }
      }

      // Get or create token record
      let token = await prisma.token.findUnique({
        where: {
          address_chain: {
            address: normalizedAddress,
            chain: chain,
          },
        },
      });

      if (!token) {
        // Create token record
        token = await prisma.token.create({
          data: {
            address: normalizedAddress,
            chain: chain,
            symbol: 'UNKNOWN',
            name: 'Unknown Token',
            decimals: 18,
          },
        });
      }

      // Call AI service for analysis
      const analysisResult = await aiService.analyzeToken(chain, normalizedAddress, {
        includeSocial,
        includeDeveloper,
        forceRefresh,
      });

      // Save analysis to database
      const riskAssessment = await prisma.riskAssessment.create({
        data: {
          tokenId: token.id,
          overallScore: analysisResult.risk_score,
          riskLevel: analysisResult.risk_level.toUpperCase(),
          confidence: analysisResult.confidence,
          factors: analysisResult.factors || [],
          recommendations: analysisResult.recommendations || [],
        },
      });

      // Save contract analysis if available
      if (analysisResult.contract_analysis) {
        await prisma.contractAnalysis.create({
          data: {
            tokenId: token.id,
            isVerified: analysisResult.contract_analysis.is_verified || false,
            hasOwner: analysisResult.contract_analysis.has_owner || false,
            hasMintFunction: analysisResult.contract_analysis.has_mint_function || false,
            hasBurnFunction: analysisResult.contract_analysis.has_burn_function || false,
            hasPauseFunction: analysisResult.contract_analysis.has_pause_function || false,
            hasBlacklist: analysisResult.contract_analysis.has_blacklist || false,
            hasWhitelist: analysisResult.contract_analysis.has_whitelist || false,
            hasHoneypot: analysisResult.contract_analysis.has_honeypot || false,
            hasRugPull: analysisResult.contract_analysis.has_rug_pull || false,
            ownershipRenounced: analysisResult.contract_analysis.ownership_renounced || false,
            isProxy: analysisResult.contract_analysis.is_proxy || false,
            functions: analysisResult.contract_analysis.functions || [],
            events: analysisResult.contract_analysis.events || [],
          },
        });
      }

      // Save social analysis if available
      if (analysisResult.social_analysis) {
        await prisma.socialAnalysis.create({
          data: {
            tokenId: token.id,
            sentimentScore: analysisResult.social_analysis.sentiment_score || 0,
            sentimentLabel: analysisResult.social_analysis.sentiment_label || 'neutral',
            confidence: analysisResult.social_analysis.confidence || 0,
            totalMentions: analysisResult.social_analysis.total_mentions || 0,
            trendingScore: analysisResult.social_analysis.trending_score || 0,
            timeframe: '24h',
          },
        });
      }

      // Record analysis history
      if (req.user) {
        await prisma.analysisHistory.create({
          data: {
            userId: req.user.userId,
            tokenId: token.id,
            analysisType: 'full_analysis',
            parameters: { includeSocial, includeDeveloper },
            results: analysisResult,
          },
        });

        // Update daily count for non-premium users
        if (!req.user.isPremium) {
          const today = new Date().toISOString().split('T')[0];
          const countKey = `analysis_count:${req.user.userId}:${today}`;
          const currentCount = await cacheService.get(countKey) || 0;
          await cacheService.set(countKey, currentCount + 1, 86400); // 24 hours
        }
      }

      // Cache result for 1 hour
      await cacheService.set(cacheKey, analysisResult, 3600);

      logger.info('Token analysis completed', {
        chain,
        address: normalizedAddress,
        riskScore: analysisResult.risk_score,
        riskLevel: analysisResult.risk_level,
        userId: req.user?.userId,
      });

      res.json({
        success: true,
        data: analysisResult,
      });
    } catch (error) {
      logger.error('Token analysis failed', {
        error: error.message,
        chain: req.params.chain,
        address: req.params.address,
        userId: req.user?.userId,
      });

      res.status(500).json({
        success: false,
        error: 'Analysis failed',
        message: error.message,
      });
    }
  }
);

/**
 * @swagger
 * /analysis/{chain}/{address}/history:
 *   get:
 *     summary: Get analysis history for a token
 *     tags: [Analysis]
 *     parameters:
 *       - in: path
 *         name: chain
 *         required: true
 *         schema:
 *           type: string
 *       - in: path
 *         name: address
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *     responses:
 *       200:
 *         description: Analysis history retrieved successfully
 */
router.get('/:chain/:address/history',
  optionalAuthMiddleware,
  [
    param('chain').isIn(['ethereum', 'bsc', 'polygon', 'arbitrum', 'optimism', 'solana']),
    param('address').isLength({ min: 32, max: 44 }),
    query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
        });
      }

      const { chain, address } = req.params;
      const { limit = 10 } = req.query;
      const normalizedAddress = address.toLowerCase();

      // Find token
      const token = await prisma.token.findUnique({
        where: {
          address_chain: {
            address: normalizedAddress,
            chain: chain,
          },
        },
      });

      if (!token) {
        return res.status(404).json({
          success: false,
          error: 'Token not found',
        });
      }

      // Get risk assessments history
      const history = await prisma.riskAssessment.findMany({
        where: { tokenId: token.id },
        orderBy: { createdAt: 'desc' },
        take: limit as number,
        select: {
          id: true,
          overallScore: true,
          riskLevel: true,
          confidence: true,
          createdAt: true,
        },
      });

      res.json({
        success: true,
        data: history,
      });
    } catch (error) {
      logger.error('Failed to get analysis history', {
        error: error.message,
        chain: req.params.chain,
        address: req.params.address,
      });

      res.status(500).json({
        success: false,
        error: 'Failed to get analysis history',
      });
    }
  }
);

/**
 * @swagger
 * /analysis/batch:
 *   post:
 *     summary: Analyze multiple tokens in batch
 *     tags: [Analysis]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               tokens:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     chain:
 *                       type: string
 *                     address:
 *                       type: string
 *     responses:
 *       200:
 *         description: Batch analysis completed
 */
router.post('/batch',
  authMiddleware,
  premiumMiddleware,
  [
    body('tokens').isArray({ min: 1, max: 10 }).withMessage('Must provide 1-10 tokens'),
    body('tokens.*.chain').isIn(['ethereum', 'bsc', 'polygon', 'arbitrum', 'optimism', 'solana']),
    body('tokens.*.address').isLength({ min: 32, max: 44 }),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
        });
      }

      const { tokens } = req.body;

      // Call AI service for batch analysis
      const results = await aiService.batchAnalyze(tokens);

      logger.info('Batch analysis completed', {
        tokenCount: tokens.length,
        userId: req.user.userId,
      });

      res.json({
        success: true,
        data: results,
      });
    } catch (error) {
      logger.error('Batch analysis failed', {
        error: error.message,
        userId: req.user.userId,
      });

      res.status(500).json({
        success: false,
        error: 'Batch analysis failed',
      });
    }
  }
);

/**
 * @swagger
 * /analysis/sentiment:
 *   post:
 *     summary: Analyze social sentiment for a query
 *     tags: [Analysis]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               query:
 *                 type: string
 *               timeframe:
 *                 type: string
 *                 default: "24h"
 *               sources:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: Sentiment analysis completed
 */
router.post('/sentiment',
  analysisLimiter,
  optionalAuthMiddleware,
  [
    body('query').notEmpty().withMessage('Query is required'),
    body('timeframe').optional().isIn(['1h', '24h', '7d', '30d']),
    body('sources').optional().isArray(),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
        });
      }

      const { query, timeframe = '24h', sources = ['twitter', 'reddit'] } = req.body;

      // Call AI service for sentiment analysis
      const result = await aiService.analyzeSentiment(query, { timeframe, sources });

      logger.info('Sentiment analysis completed', {
        query,
        timeframe,
        userId: req.user?.userId,
      });

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      logger.error('Sentiment analysis failed', {
        error: error.message,
        query: req.body.query,
        userId: req.user?.userId,
      });

      res.status(500).json({
        success: false,
        error: 'Sentiment analysis failed',
      });
    }
  }
);

export default router;
