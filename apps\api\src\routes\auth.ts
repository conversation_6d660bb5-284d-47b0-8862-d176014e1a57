import express from 'express';
import { body, validationResult } from 'express-validator';
import jwt from 'jsonwebtoken';
import { ethers } from 'ethers';
import { authMiddleware } from '../middleware/auth';
import { logger, generateSecureRandom, hashPassword, verifyPassword } from '@tokenforge/shared';
import { db } from '../database';
import { sendSuccess, handleAsync, AppError } from '../middleware/errorHandler';
import { rateLimit } from 'express-rate-limit';

const router = express.Router();

// Rate limiting for auth endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // limit each IP to 10 requests per windowMs
  message: { error: 'Too many authentication attempts, please try again later.' },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * @swagger
 * /auth/nonce:
 *   post:
 *     summary: Get authentication nonce for wallet address
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - address
 *             properties:
 *               address:
 *                 type: string
 *                 description: Ethereum wallet address
 *     responses:
 *       200:
 *         description: Nonce generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 nonce:
 *                   type: string
 *                   description: Random nonce for message signing
 */
router.post('/nonce', 
  authLimiter,
  [
    body('address')
      .isEthereumAddress()
      .withMessage('Invalid Ethereum address')
      .customSanitizer(value => value.toLowerCase()),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
        });
      }

      const { address } = req.body;
      const nonce = generateSecureRandom();

      // Store or update nonce in database
      await db.userSession.upsert({
        where: { address },
        update: { 
          nonce,
          expiresAt: new Date(Date.now() + 10 * 60 * 1000), // 10 minutes
        },
        create: {
          address,
          nonce,
          expiresAt: new Date(Date.now() + 10 * 60 * 1000),
        },
      });

      logger.info('Nonce generated', { address });

      res.json({
        success: true,
        data: { nonce },
      });
    } catch (error) {
      logger.error('Nonce generation failed', { error: error.message, address: req.body.address });
      res.status(500).json({
        success: false,
        error: 'Failed to generate nonce',
      });
    }
  }
);

/**
 * @swagger
 * /auth/verify:
 *   post:
 *     summary: Verify wallet signature and authenticate user
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - address
 *               - message
 *               - signature
 *             properties:
 *               address:
 *                 type: string
 *                 description: Ethereum wallet address
 *               message:
 *                 type: string
 *                 description: Signed message
 *               signature:
 *                 type: string
 *                 description: Message signature
 */
router.post('/verify',
  authLimiter,
  [
    body('address').isEthereumAddress().withMessage('Invalid Ethereum address'),
    body('message').notEmpty().withMessage('Message is required'),
    body('signature').notEmpty().withMessage('Signature is required'),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
        });
      }

      const { address, message, signature } = req.body;
      const normalizedAddress = address.toLowerCase();

      // Get nonce from database
      const authNonce = await prisma.authNonce.findUnique({
        where: { address: normalizedAddress },
      });

      if (!authNonce || authNonce.expiresAt < new Date()) {
        return res.status(400).json({
          success: false,
          error: 'Invalid or expired nonce',
        });
      }

      // Verify signature
      try {
        const recoveredAddress = ethers.verifyMessage(message, signature);
        
        if (recoveredAddress.toLowerCase() !== normalizedAddress) {
          return res.status(400).json({
            success: false,
            error: 'Invalid signature',
          });
        }
      } catch (error) {
        return res.status(400).json({
          success: false,
          error: 'Invalid signature format',
        });
      }

      // Check if message contains the correct nonce
      if (!message.includes(authNonce.nonce)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid message nonce',
        });
      }

      // Create or update user
      const user = await prisma.user.upsert({
        where: { address: normalizedAddress },
        update: { 
          lastLoginAt: new Date(),
        },
        create: {
          address: normalizedAddress,
          username: `user_${normalizedAddress.slice(2, 8)}`,
          isVerified: false,
          isPremium: false,
          preferences: {
            theme: 'system',
            currency: 'USD',
            notifications: true,
          },
        },
      });

      // Generate JWT token
      const token = jwt.sign(
        { 
          userId: user.id, 
          address: user.address,
        },
        process.env.JWT_SECRET!,
        { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
      );

      // Delete used nonce
      await prisma.authNonce.delete({
        where: { address: normalizedAddress },
      });

      logger.info('User authenticated', { userId: user.id, address: normalizedAddress });

      res.json({
        success: true,
        data: {
          token,
          user: {
            id: user.id,
            address: user.address,
            username: user.username,
            email: user.email,
            avatar: user.avatar,
            isVerified: user.isVerified,
            isPremium: user.isPremium,
            createdAt: user.createdAt,
            preferences: user.preferences,
          },
        },
      });
    } catch (error) {
      logger.error('Authentication verification failed', { 
        error: error.message, 
        address: req.body.address 
      });
      res.status(500).json({
        success: false,
        error: 'Authentication failed',
      });
    }
  }
);

/**
 * @swagger
 * /auth/me:
 *   get:
 *     summary: Get current user profile
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile retrieved successfully
 */
router.get('/me', authMiddleware, async (req, res) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: req.user.userId },
      select: {
        id: true,
        address: true,
        username: true,
        email: true,
        avatar: true,
        bio: true,
        isVerified: true,
        isPremium: true,
        createdAt: true,
        updatedAt: true,
        lastLoginAt: true,
        preferences: true,
      },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
      });
    }

    res.json({
      success: true,
      data: user,
    });
  } catch (error) {
    logger.error('Failed to get user profile', { error: error.message, userId: req.user.userId });
    res.status(500).json({
      success: false,
      error: 'Failed to get user profile',
    });
  }
});

/**
 * @swagger
 * /auth/profile:
 *   patch:
 *     summary: Update user profile
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               username:
 *                 type: string
 *               email:
 *                 type: string
 *               bio:
 *                 type: string
 *               preferences:
 *                 type: object
 */
router.patch('/profile', 
  authMiddleware,
  [
    body('username').optional().isLength({ min: 3, max: 30 }).withMessage('Username must be 3-30 characters'),
    body('email').optional().isEmail().withMessage('Invalid email address'),
    body('bio').optional().isLength({ max: 500 }).withMessage('Bio must be less than 500 characters'),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
        });
      }

      const { username, email, bio, preferences } = req.body;
      const updateData: any = {};

      if (username) updateData.username = username;
      if (email) updateData.email = email;
      if (bio !== undefined) updateData.bio = bio;
      if (preferences) updateData.preferences = preferences;

      const user = await prisma.user.update({
        where: { id: req.user.userId },
        data: updateData,
        select: {
          id: true,
          address: true,
          username: true,
          email: true,
          avatar: true,
          bio: true,
          isVerified: true,
          isPremium: true,
          createdAt: true,
          updatedAt: true,
          preferences: true,
        },
      });

      logger.info('User profile updated', { userId: req.user.userId });

      res.json({
        success: true,
        data: user,
      });
    } catch (error) {
      if (error.code === 'P2002') {
        return res.status(400).json({
          success: false,
          error: 'Username or email already exists',
        });
      }

      logger.error('Failed to update user profile', { 
        error: error.message, 
        userId: req.user.userId 
      });
      res.status(500).json({
        success: false,
        error: 'Failed to update profile',
      });
    }
  }
);

/**
 * @swagger
 * /auth/logout:
 *   post:
 *     summary: Logout user
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Logged out successfully
 */
router.post('/logout', authMiddleware, async (req, res) => {
  try {
    // In a more sophisticated setup, you might want to blacklist the token
    // For now, we'll just log the logout event
    logger.info('User logged out', { userId: req.user.userId });

    res.json({
      success: true,
      message: 'Logged out successfully',
    });
  } catch (error) {
    logger.error('Logout failed', { error: error.message, userId: req.user.userId });
    res.status(500).json({
      success: false,
      error: 'Logout failed',
    });
  }
});

// Email/Password Authentication Routes

/**
 * @swagger
 * /auth/register:
 *   post:
 *     summary: Register with email and password
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - username
 *               - password
 *               - acceptTerms
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               username:
 *                 type: string
 *               password:
 *                 type: string
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               acceptTerms:
 *                 type: boolean
 */
router.post('/register',
  authLimiter,
  [
    body('email').isEmail().withMessage('Invalid email address'),
    body('username').isLength({ min: 3, max: 30 }).withMessage('Username must be 3-30 characters'),
    body('password').isLength({ min: 8 }).withMessage('Password must be at least 8 characters'),
    body('acceptTerms').equals('true').withMessage('You must accept the terms and conditions'),
  ],
  handleAsync(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Validation failed', 400, 'VALIDATION_ERROR', errors.array());
    }

    const { email, username, password, firstName, lastName } = req.body;

    // Check if user already exists
    const existingUser = await db.getUserByEmailOrUsername(email);
    if (existingUser) {
      if (existingUser.email === email) {
        throw new AppError('Email already registered', 409, 'EMAIL_EXISTS');
      }
      if (existingUser.username === username) {
        throw new AppError('Username already taken', 409, 'USERNAME_EXISTS');
      }
    }

    // Hash password
    const passwordHash = await hashPassword(password);

    // Create user
    const user = await db.user.create({
      data: {
        email,
        username,
        passwordHash,
        firstName,
        lastName,
        role: 'USER',
        status: 'ACTIVE',
        subscriptionPlan: 'FREE',
        subscriptionStatus: 'NONE',
      },
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        role: true,
        emailVerified: true,
        subscriptionPlan: true,
        createdAt: true,
      },
    });

    // Generate token
    const token = jwt.sign(
      {
        userId: user.id,
        email: user.email,
        username: user.username,
        role: user.role,
      },
      process.env.JWT_SECRET!,
      { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
    );

    logger.info('User registered via email', { userId: user.id, email: user.email });

    sendSuccess(res, {
      user,
      token,
      expiresIn: 24 * 60 * 60,
    }, 'User registered successfully', 201);
  })
);

/**
 * @swagger
 * /auth/login:
 *   post:
 *     summary: Login with email and password
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 */
router.post('/login',
  authLimiter,
  [
    body('email').isEmail().withMessage('Invalid email address'),
    body('password').notEmpty().withMessage('Password is required'),
  ],
  handleAsync(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Validation failed', 400, 'VALIDATION_ERROR', errors.array());
    }

    const { email, password } = req.body;

    // Find user
    const user = await db.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        username: true,
        passwordHash: true,
        firstName: true,
        lastName: true,
        role: true,
        status: true,
        emailVerified: true,
        subscriptionPlan: true,
        subscriptionStatus: true,
      },
    });

    if (!user || !user.passwordHash) {
      throw new AppError('Invalid email or password', 401, 'INVALID_CREDENTIALS');
    }

    // Check account status
    if (user.status !== 'ACTIVE') {
      throw new AppError('Account is suspended or banned', 403, 'ACCOUNT_SUSPENDED');
    }

    // Verify password
    const isValidPassword = await verifyPassword(password, user.passwordHash);
    if (!isValidPassword) {
      throw new AppError('Invalid email or password', 401, 'INVALID_CREDENTIALS');
    }

    // Update last login
    await db.user.update({
      where: { id: user.id },
      data: {
        lastLoginAt: new Date(),
        lastActiveAt: new Date(),
      },
    });

    // Generate token
    const token = jwt.sign(
      {
        userId: user.id,
        email: user.email,
        username: user.username,
        role: user.role,
      },
      process.env.JWT_SECRET!,
      { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
    );

    // Remove sensitive data
    const { passwordHash, ...userResponse } = user;

    logger.info('User logged in via email', { userId: user.id, email: user.email });

    sendSuccess(res, {
      user: userResponse,
      token,
      expiresIn: 24 * 60 * 60,
    }, 'Login successful');
  })
);

export { router as authRoutes };
export default router;
