/**
 * TokenForge Health Check Routes
 * System health and status endpoints
 */

import { Router, Request, Response } from 'express';
import { db } from '../database';
import { redisManager } from '../../../../config/redis';
import { blockchainManager } from '../../../../config/blockchain';
import { sendSuccess, handleAsync } from '../middleware/errorHandler';

const router = Router();

/**
 * @swagger
 * /health:
 *   get:
 *     summary: Basic health check
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: Service is healthy
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     status:
 *                       type: string
 *                     timestamp:
 *                       type: string
 *                     uptime:
 *                       type: number
 */
router.get('/', handleAsync(async (req: Request, res: Response) => {
  const uptime = process.uptime();
  
  sendSuccess(res, {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: Math.floor(uptime),
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
  });
}));

/**
 * @swagger
 * /health/detailed:
 *   get:
 *     summary: Detailed health check with all services
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: Detailed health status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     status:
 *                       type: string
 *                     services:
 *                       type: object
 */
router.get('/detailed', handleAsync(async (req: Request, res: Response) => {
  const startTime = Date.now();
  
  // Check all services in parallel
  const [dbHealth, redisHealth, blockchainHealth] = await Promise.allSettled([
    db.healthCheck(),
    redisManager.healthCheck(),
    blockchainManager.healthCheck(),
  ]);

  const services = {
    database: dbHealth.status === 'fulfilled' ? dbHealth.value : {
      status: 'unhealthy',
      error: dbHealth.status === 'rejected' ? dbHealth.reason.message : 'Unknown error'
    },
    redis: redisHealth.status === 'fulfilled' ? redisHealth.value : {
      status: 'unhealthy',
      error: redisHealth.status === 'rejected' ? redisHealth.reason.message : 'Unknown error'
    },
    blockchain: blockchainHealth.status === 'fulfilled' ? blockchainHealth.value : {
      status: 'unhealthy',
      error: blockchainHealth.status === 'rejected' ? blockchainHealth.reason.message : 'Unknown error'
    },
  };

  // Determine overall status
  const allHealthy = Object.values(services).every(service => 
    service.status === 'healthy'
  );
  const someHealthy = Object.values(services).some(service => 
    service.status === 'healthy'
  );

  let overallStatus = 'unhealthy';
  if (allHealthy) {
    overallStatus = 'healthy';
  } else if (someHealthy) {
    overallStatus = 'degraded';
  }

  const responseTime = Date.now() - startTime;

  sendSuccess(res, {
    status: overallStatus,
    timestamp: new Date().toISOString(),
    uptime: Math.floor(process.uptime()),
    responseTime: `${responseTime}ms`,
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    services,
  });
}));

/**
 * @swagger
 * /health/database:
 *   get:
 *     summary: Database health check
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: Database health status
 */
router.get('/database', handleAsync(async (req: Request, res: Response) => {
  const health = await db.healthCheck();
  const stats = await db.getStats();
  
  sendSuccess(res, {
    ...health,
    stats,
    timestamp: new Date().toISOString(),
  });
}));

/**
 * @swagger
 * /health/redis:
 *   get:
 *     summary: Redis health check
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: Redis health status
 */
router.get('/redis', handleAsync(async (req: Request, res: Response) => {
  const health = await redisManager.healthCheck();
  
  sendSuccess(res, {
    ...health,
    timestamp: new Date().toISOString(),
  });
}));

/**
 * @swagger
 * /health/blockchain:
 *   get:
 *     summary: Blockchain services health check
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: Blockchain services health status
 */
router.get('/blockchain', handleAsync(async (req: Request, res: Response) => {
  const health = await blockchainManager.healthCheck();
  
  sendSuccess(res, {
    ...health,
    timestamp: new Date().toISOString(),
  });
}));

/**
 * @swagger
 * /health/metrics:
 *   get:
 *     summary: System metrics
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: System performance metrics
 */
router.get('/metrics', handleAsync(async (req: Request, res: Response) => {
  const memoryUsage = process.memoryUsage();
  const cpuUsage = process.cpuUsage();
  
  sendSuccess(res, {
    timestamp: new Date().toISOString(),
    uptime: Math.floor(process.uptime()),
    memory: {
      rss: `${Math.round(memoryUsage.rss / 1024 / 1024)}MB`,
      heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`,
      heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
      external: `${Math.round(memoryUsage.external / 1024 / 1024)}MB`,
    },
    cpu: {
      user: cpuUsage.user,
      system: cpuUsage.system,
    },
    nodeVersion: process.version,
    platform: process.platform,
    arch: process.arch,
  });
}));

/**
 * @swagger
 * /health/readiness:
 *   get:
 *     summary: Readiness probe for Kubernetes
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: Service is ready to accept traffic
 *       503:
 *         description: Service is not ready
 */
router.get('/readiness', handleAsync(async (req: Request, res: Response) => {
  // Check critical services
  const [dbHealth, redisHealth] = await Promise.allSettled([
    db.healthCheck(),
    redisManager.healthCheck(),
  ]);

  const dbHealthy = dbHealth.status === 'fulfilled' && dbHealth.value.status === 'healthy';
  const redisHealthy = redisHealth.status === 'fulfilled' && redisHealth.value.status === 'healthy';

  if (dbHealthy && redisHealthy) {
    sendSuccess(res, {
      status: 'ready',
      timestamp: new Date().toISOString(),
    });
  } else {
    res.status(503).json({
      success: false,
      error: 'Service not ready',
      details: {
        database: dbHealthy ? 'healthy' : 'unhealthy',
        redis: redisHealthy ? 'healthy' : 'unhealthy',
      },
      timestamp: new Date().toISOString(),
    });
  }
}));

/**
 * @swagger
 * /health/liveness:
 *   get:
 *     summary: Liveness probe for Kubernetes
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: Service is alive
 */
router.get('/liveness', handleAsync(async (req: Request, res: Response) => {
  sendSuccess(res, {
    status: 'alive',
    timestamp: new Date().toISOString(),
    uptime: Math.floor(process.uptime()),
  });
}));

export { router as healthRoutes };
