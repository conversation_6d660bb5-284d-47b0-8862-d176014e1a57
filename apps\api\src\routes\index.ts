/**
 * TokenForge API Routes
 * Main router configuration and route definitions
 */

import { Router } from 'express';
import { authRoutes } from './auth';
import { userRoutes } from './users';
import { tokenRoutes } from './tokens';
import { portfolioRoutes } from './portfolios';
import { alertRoutes } from './alerts';
import { analyticsRoutes } from './analytics';
import { adminRoutes } from './admin';
import { healthRoutes } from './health';

const router = Router();

// Health check routes (no auth required)
router.use('/health', healthRoutes);

// Authentication routes
router.use('/auth', authRoutes);

// User management routes
router.use('/users', userRoutes);

// Token analysis routes
router.use('/tokens', tokenRoutes);

// Portfolio management routes
router.use('/portfolios', portfolioRoutes);

// Alert management routes
router.use('/alerts', alertRoutes);

// Analytics routes
router.use('/analytics', analyticsRoutes);

// Admin routes
router.use('/admin', adminRoutes);

export { router as apiRoutes };
