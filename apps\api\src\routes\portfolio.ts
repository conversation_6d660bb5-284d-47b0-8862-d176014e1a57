import express from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { authMiddleware } from '../middleware/auth';
import { logger } from '../utils/logger';
import { BlockchainService } from '../services/BlockchainService';
import { CacheService } from '../services/CacheService';

const router = express.Router();
const prisma = new PrismaClient();
const blockchainService = new BlockchainService();
const cacheService = new CacheService();

/**
 * @swagger
 * /portfolio:
 *   get:
 *     summary: Get user's portfolios
 *     tags: [Portfolio]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Portfolios retrieved successfully
 */
router.get('/', authMiddleware, async (req, res) => {
  try {
    const portfolios = await prisma.portfolio.findMany({
      where: { userId: req.user.userId },
      include: {
        tokens: {
          include: {
            token: {
              include: {
                metrics: {
                  orderBy: { updatedAt: 'desc' },
                  take: 1,
                },
                riskAssessments: {
                  orderBy: { createdAt: 'desc' },
                  take: 1,
                },
              },
            },
          },
        },
      },
      orderBy: [
        { isDefault: 'desc' },
        { createdAt: 'desc' },
      ],
    });

    // Calculate portfolio values
    const portfoliosWithValues = await Promise.all(
      portfolios.map(async (portfolio) => {
        let totalValue = 0;
        let totalChange24h = 0;

        const tokensWithValues = portfolio.tokens.map((portfolioToken) => {
          const token = portfolioToken.token;
          const metrics = token.metrics[0];
          const riskAssessment = token.riskAssessments[0];

          const currentPrice = metrics?.price || 0;
          const amount = parseFloat(portfolioToken.amount);
          const value = currentPrice * amount;
          const change24h = metrics?.priceChange24h || 0;
          const changeValue = value * (change24h / 100);

          totalValue += value;
          totalChange24h += changeValue;

          return {
            id: portfolioToken.id,
            amount: portfolioToken.amount,
            averagePrice: portfolioToken.averagePrice,
            addedAt: portfolioToken.addedAt,
            updatedAt: portfolioToken.updatedAt,
            token: {
              ...token,
              currentPrice,
              value,
              change24h,
              changeValue,
              riskScore: riskAssessment?.overallScore,
              riskLevel: riskAssessment?.riskLevel,
            },
          };
        });

        const totalChangePercent24h = totalValue > 0 ? (totalChange24h / totalValue) * 100 : 0;

        return {
          ...portfolio,
          tokens: tokensWithValues,
          totalValue,
          totalChange24h,
          totalChangePercent24h,
        };
      })
    );

    res.json({
      success: true,
      data: portfoliosWithValues,
    });
  } catch (error) {
    logger.error('Failed to get portfolios', {
      error: error.message,
      userId: req.user.userId,
    });

    res.status(500).json({
      success: false,
      error: 'Failed to get portfolios',
    });
  }
});

/**
 * @swagger
 * /portfolio:
 *   post:
 *     summary: Create a new portfolio
 *     tags: [Portfolio]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               isPublic:
 *                 type: boolean
 *     responses:
 *       201:
 *         description: Portfolio created successfully
 */
router.post('/',
  authMiddleware,
  [
    body('name').notEmpty().isLength({ min: 1, max: 100 }).withMessage('Name must be 1-100 characters'),
    body('description').optional().isLength({ max: 500 }).withMessage('Description must be less than 500 characters'),
    body('isPublic').optional().isBoolean(),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
        });
      }

      const { name, description, isPublic = false } = req.body;

      // Check if user already has a portfolio with this name
      const existingPortfolio = await prisma.portfolio.findFirst({
        where: {
          userId: req.user.userId,
          name: name,
        },
      });

      if (existingPortfolio) {
        return res.status(400).json({
          success: false,
          error: 'Portfolio with this name already exists',
        });
      }

      // Check if this should be the default portfolio
      const portfolioCount = await prisma.portfolio.count({
        where: { userId: req.user.userId },
      });

      const portfolio = await prisma.portfolio.create({
        data: {
          userId: req.user.userId,
          name,
          description,
          isPublic,
          isDefault: portfolioCount === 0, // First portfolio is default
        },
      });

      logger.info('Portfolio created', {
        portfolioId: portfolio.id,
        userId: req.user.userId,
        name,
      });

      res.status(201).json({
        success: true,
        data: portfolio,
      });
    } catch (error) {
      logger.error('Failed to create portfolio', {
        error: error.message,
        userId: req.user.userId,
      });

      res.status(500).json({
        success: false,
        error: 'Failed to create portfolio',
      });
    }
  }
);

/**
 * @swagger
 * /portfolio/{id}:
 *   get:
 *     summary: Get a specific portfolio
 *     tags: [Portfolio]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Portfolio retrieved successfully
 */
router.get('/:id',
  authMiddleware,
  [param('id').isUUID()],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
        });
      }

      const { id } = req.params;

      const portfolio = await prisma.portfolio.findFirst({
        where: {
          id,
          userId: req.user.userId,
        },
        include: {
          tokens: {
            include: {
              token: {
                include: {
                  metrics: {
                    orderBy: { updatedAt: 'desc' },
                    take: 1,
                  },
                  riskAssessments: {
                    orderBy: { createdAt: 'desc' },
                    take: 1,
                  },
                },
              },
            },
            orderBy: { addedAt: 'desc' },
          },
        },
      });

      if (!portfolio) {
        return res.status(404).json({
          success: false,
          error: 'Portfolio not found',
        });
      }

      // Calculate portfolio metrics
      let totalValue = 0;
      let totalChange24h = 0;
      let totalInvested = 0;

      const tokensWithValues = portfolio.tokens.map((portfolioToken) => {
        const token = portfolioToken.token;
        const metrics = token.metrics[0];
        const riskAssessment = token.riskAssessments[0];

        const currentPrice = metrics?.price || 0;
        const amount = parseFloat(portfolioToken.amount);
        const averagePrice = portfolioToken.averagePrice || 0;
        
        const value = currentPrice * amount;
        const invested = averagePrice * amount;
        const change24h = metrics?.priceChange24h || 0;
        const changeValue = value * (change24h / 100);
        const pnl = value - invested;
        const pnlPercent = invested > 0 ? (pnl / invested) * 100 : 0;

        totalValue += value;
        totalChange24h += changeValue;
        totalInvested += invested;

        return {
          id: portfolioToken.id,
          amount: portfolioToken.amount,
          averagePrice: portfolioToken.averagePrice,
          addedAt: portfolioToken.addedAt,
          updatedAt: portfolioToken.updatedAt,
          token: {
            ...token,
            currentPrice,
            value,
            invested,
            pnl,
            pnlPercent,
            change24h,
            changeValue,
            riskScore: riskAssessment?.overallScore,
            riskLevel: riskAssessment?.riskLevel,
          },
        };
      });

      const totalChangePercent24h = totalValue > 0 ? (totalChange24h / totalValue) * 100 : 0;
      const totalPnl = totalValue - totalInvested;
      const totalPnlPercent = totalInvested > 0 ? (totalPnl / totalInvested) * 100 : 0;

      const portfolioWithMetrics = {
        ...portfolio,
        tokens: tokensWithValues,
        totalValue,
        totalInvested,
        totalPnl,
        totalPnlPercent,
        totalChange24h,
        totalChangePercent24h,
      };

      res.json({
        success: true,
        data: portfolioWithMetrics,
      });
    } catch (error) {
      logger.error('Failed to get portfolio', {
        error: error.message,
        portfolioId: req.params.id,
        userId: req.user.userId,
      });

      res.status(500).json({
        success: false,
        error: 'Failed to get portfolio',
      });
    }
  }
);

/**
 * @swagger
 * /portfolio/{id}/tokens:
 *   post:
 *     summary: Add a token to portfolio
 *     tags: [Portfolio]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               chain:
 *                 type: string
 *               address:
 *                 type: string
 *               amount:
 *                 type: string
 *               averagePrice:
 *                 type: number
 *     responses:
 *       201:
 *         description: Token added to portfolio successfully
 */
router.post('/:id/tokens',
  authMiddleware,
  [
    param('id').isUUID(),
    body('chain').isIn(['ethereum', 'bsc', 'polygon', 'arbitrum', 'optimism', 'solana']),
    body('address').isLength({ min: 32, max: 44 }),
    body('amount').isNumeric().withMessage('Amount must be a number'),
    body('averagePrice').optional().isFloat({ min: 0 }),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
        });
      }

      const { id } = req.params;
      const { chain, address, amount, averagePrice } = req.body;
      const normalizedAddress = address.toLowerCase();

      // Verify portfolio ownership
      const portfolio = await prisma.portfolio.findFirst({
        where: {
          id,
          userId: req.user.userId,
        },
      });

      if (!portfolio) {
        return res.status(404).json({
          success: false,
          error: 'Portfolio not found',
        });
      }

      // Get or create token
      let token = await prisma.token.findUnique({
        where: {
          address_chain: {
            address: normalizedAddress,
            chain: chain,
          },
        },
      });

      if (!token) {
        // Fetch token info from blockchain
        try {
          const tokenInfo = await blockchainService.getTokenInfo(chain, normalizedAddress);
          token = await prisma.token.create({
            data: {
              address: normalizedAddress,
              chain: chain,
              symbol: tokenInfo.symbol,
              name: tokenInfo.name,
              decimals: tokenInfo.decimals,
              totalSupply: tokenInfo.totalSupply,
            },
          });
        } catch (blockchainError) {
          return res.status(400).json({
            success: false,
            error: 'Invalid token address or unable to fetch token information',
          });
        }
      }

      // Check if token already exists in portfolio
      const existingToken = await prisma.portfolioToken.findUnique({
        where: {
          portfolioId_tokenId: {
            portfolioId: id,
            tokenId: token.id,
          },
        },
      });

      if (existingToken) {
        return res.status(400).json({
          success: false,
          error: 'Token already exists in portfolio',
        });
      }

      // Add token to portfolio
      const portfolioToken = await prisma.portfolioToken.create({
        data: {
          portfolioId: id,
          tokenId: token.id,
          amount: amount.toString(),
          averagePrice,
        },
        include: {
          token: true,
        },
      });

      logger.info('Token added to portfolio', {
        portfolioId: id,
        tokenId: token.id,
        userId: req.user.userId,
        amount,
      });

      res.status(201).json({
        success: true,
        data: portfolioToken,
      });
    } catch (error) {
      logger.error('Failed to add token to portfolio', {
        error: error.message,
        portfolioId: req.params.id,
        userId: req.user.userId,
      });

      res.status(500).json({
        success: false,
        error: 'Failed to add token to portfolio',
      });
    }
  }
);

/**
 * @swagger
 * /portfolio/{id}/tokens/{tokenId}:
 *   patch:
 *     summary: Update token in portfolio
 *     tags: [Portfolio]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *       - in: path
 *         name: tokenId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               amount:
 *                 type: string
 *               averagePrice:
 *                 type: number
 *     responses:
 *       200:
 *         description: Token updated successfully
 */
router.patch('/:id/tokens/:tokenId',
  authMiddleware,
  [
    param('id').isUUID(),
    param('tokenId').isUUID(),
    body('amount').optional().isNumeric(),
    body('averagePrice').optional().isFloat({ min: 0 }),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
        });
      }

      const { id, tokenId } = req.params;
      const { amount, averagePrice } = req.body;

      // Verify portfolio ownership and token existence
      const portfolioToken = await prisma.portfolioToken.findFirst({
        where: {
          id: tokenId,
          portfolioId: id,
          portfolio: {
            userId: req.user.userId,
          },
        },
        include: {
          token: true,
        },
      });

      if (!portfolioToken) {
        return res.status(404).json({
          success: false,
          error: 'Token not found in portfolio',
        });
      }

      // Update token
      const updateData: any = {};
      if (amount !== undefined) updateData.amount = amount.toString();
      if (averagePrice !== undefined) updateData.averagePrice = averagePrice;

      const updatedToken = await prisma.portfolioToken.update({
        where: { id: tokenId },
        data: updateData,
        include: {
          token: true,
        },
      });

      logger.info('Portfolio token updated', {
        portfolioId: id,
        tokenId,
        userId: req.user.userId,
      });

      res.json({
        success: true,
        data: updatedToken,
      });
    } catch (error) {
      logger.error('Failed to update portfolio token', {
        error: error.message,
        portfolioId: req.params.id,
        tokenId: req.params.tokenId,
        userId: req.user.userId,
      });

      res.status(500).json({
        success: false,
        error: 'Failed to update token',
      });
    }
  }
);

/**
 * @swagger
 * /portfolio/{id}/tokens/{tokenId}:
 *   delete:
 *     summary: Remove token from portfolio
 *     tags: [Portfolio]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *       - in: path
 *         name: tokenId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Token removed successfully
 */
router.delete('/:id/tokens/:tokenId',
  authMiddleware,
  [
    param('id').isUUID(),
    param('tokenId').isUUID(),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
        });
      }

      const { id, tokenId } = req.params;

      // Verify portfolio ownership and token existence
      const portfolioToken = await prisma.portfolioToken.findFirst({
        where: {
          id: tokenId,
          portfolioId: id,
          portfolio: {
            userId: req.user.userId,
          },
        },
      });

      if (!portfolioToken) {
        return res.status(404).json({
          success: false,
          error: 'Token not found in portfolio',
        });
      }

      // Remove token from portfolio
      await prisma.portfolioToken.delete({
        where: { id: tokenId },
      });

      logger.info('Token removed from portfolio', {
        portfolioId: id,
        tokenId,
        userId: req.user.userId,
      });

      res.json({
        success: true,
        message: 'Token removed from portfolio',
      });
    } catch (error) {
      logger.error('Failed to remove token from portfolio', {
        error: error.message,
        portfolioId: req.params.id,
        tokenId: req.params.tokenId,
        userId: req.user.userId,
      });

      res.status(500).json({
        success: false,
        error: 'Failed to remove token',
      });
    }
  }
);

export default router;
