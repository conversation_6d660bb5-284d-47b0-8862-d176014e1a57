import express from 'express';
import { query, param, validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { optionalAuthMiddleware } from '../middleware/auth';
import { logger } from '../utils/logger';
import { BlockchainService } from '../services/BlockchainService';
import { CacheService } from '../services/CacheService';
import { rateLimit } from 'express-rate-limit';

const router = express.Router();
const prisma = new PrismaClient();
const blockchainService = new BlockchainService();
const cacheService = new CacheService();

// Rate limiting for token endpoints
const tokenLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: { error: 'Too many token requests, please try again later.' },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * @swagger
 * /tokens/search:
 *   get:
 *     summary: Search for tokens by symbol or address
 *     tags: [Tokens]
 *     parameters:
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *         description: Search query (symbol or address)
 *       - in: query
 *         name: chain
 *         schema:
 *           type: string
 *         description: Blockchain network filter
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Maximum number of results
 *     responses:
 *       200:
 *         description: Search results
 */
router.get('/search',
  tokenLimiter,
  optionalAuthMiddleware,
  [
    query('q').notEmpty().withMessage('Search query is required'),
    query('chain').optional().isIn(['ethereum', 'bsc', 'polygon', 'arbitrum', 'optimism']),
    query('limit').optional().isInt({ min: 1, max: 50 }).toInt(),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
        });
      }

      const { q: query, chain, limit = 10 } = req.query;
      const cacheKey = `token_search:${query}:${chain || 'all'}:${limit}`;

      // Check cache first
      const cachedResults = await cacheService.get(cacheKey);
      if (cachedResults) {
        return res.json({
          success: true,
          data: cachedResults,
          cached: true,
        });
      }

      let searchResults = [];

      // Check if query is an address
      const isAddress = typeof query === 'string' && query.startsWith('0x') && query.length === 42;

      if (isAddress) {
        // Search by address
        const tokens = await prisma.token.findMany({
          where: {
            address: {
              equals: query as string,
              mode: 'insensitive',
            },
            ...(chain && { chain: chain as string }),
          },
          take: limit as number,
          include: {
            metrics: {
              orderBy: { updatedAt: 'desc' },
              take: 1,
            },
            riskAssessments: {
              orderBy: { createdAt: 'desc' },
              take: 1,
            },
          },
        });

        searchResults = tokens.map(token => ({
          address: token.address,
          symbol: token.symbol,
          name: token.name,
          chain: token.chain,
          logoUrl: token.logoUrl,
          isVerified: token.isVerified,
          marketCap: token.metrics[0]?.marketCap,
          price: token.metrics[0]?.price,
          volume24h: token.metrics[0]?.volume24h,
          riskScore: token.riskAssessments[0]?.overallScore,
          tags: token.tags,
        }));
      } else {
        // Search by symbol or name
        const tokens = await prisma.token.findMany({
          where: {
            OR: [
              {
                symbol: {
                  contains: query as string,
                  mode: 'insensitive',
                },
              },
              {
                name: {
                  contains: query as string,
                  mode: 'insensitive',
                },
              },
            ],
            ...(chain && { chain: chain as string }),
          },
          take: limit as number,
          orderBy: [
            { isVerified: 'desc' },
            { metrics: { marketCap: 'desc' } },
          ],
          include: {
            metrics: {
              orderBy: { updatedAt: 'desc' },
              take: 1,
            },
            riskAssessments: {
              orderBy: { createdAt: 'desc' },
              take: 1,
            },
          },
        });

        searchResults = tokens.map(token => ({
          address: token.address,
          symbol: token.symbol,
          name: token.name,
          chain: token.chain,
          logoUrl: token.logoUrl,
          isVerified: token.isVerified,
          marketCap: token.metrics[0]?.marketCap,
          price: token.metrics[0]?.price,
          volume24h: token.metrics[0]?.volume24h,
          riskScore: token.riskAssessments[0]?.overallScore,
          tags: token.tags,
        }));
      }

      // Cache results for 5 minutes
      await cacheService.set(cacheKey, searchResults, 300);

      logger.info('Token search completed', { 
        query, 
        chain, 
        resultsCount: searchResults.length,
        userId: req.user?.userId,
      });

      res.json({
        success: true,
        data: searchResults,
      });
    } catch (error) {
      logger.error('Token search failed', { 
        error: error.message, 
        query: req.query.q,
        userId: req.user?.userId,
      });
      res.status(500).json({
        success: false,
        error: 'Search failed',
      });
    }
  }
);

/**
 * @swagger
 * /tokens/{chain}/{address}:
 *   get:
 *     summary: Get detailed token information
 *     tags: [Tokens]
 *     parameters:
 *       - in: path
 *         name: chain
 *         required: true
 *         schema:
 *           type: string
 *         description: Blockchain network
 *       - in: path
 *         name: address
 *         required: true
 *         schema:
 *           type: string
 *         description: Token contract address
 *     responses:
 *       200:
 *         description: Token information
 */
router.get('/:chain/:address',
  tokenLimiter,
  optionalAuthMiddleware,
  [
    param('chain').isIn(['ethereum', 'bsc', 'polygon', 'arbitrum', 'optimism']),
    param('address').isEthereumAddress(),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
        });
      }

      const { chain, address } = req.params;
      const normalizedAddress = address.toLowerCase();
      const cacheKey = `token_info:${chain}:${normalizedAddress}`;

      // Check cache first
      const cachedToken = await cacheService.get(cacheKey);
      if (cachedToken) {
        return res.json({
          success: true,
          data: cachedToken,
          cached: true,
        });
      }

      // Get token from database
      let token = await prisma.token.findUnique({
        where: {
          address_chain: {
            address: normalizedAddress,
            chain: chain,
          },
        },
        include: {
          metrics: {
            orderBy: { updatedAt: 'desc' },
            take: 1,
          },
          riskAssessments: {
            orderBy: { createdAt: 'desc' },
            take: 1,
          },
          contractAnalysis: {
            orderBy: { updatedAt: 'desc' },
            take: 1,
          },
        },
      });

      // If token not in database, fetch from blockchain
      if (!token) {
        try {
          const tokenInfo = await blockchainService.getTokenInfo(chain, normalizedAddress);
          
          // Create token record
          token = await prisma.token.create({
            data: {
              address: normalizedAddress,
              chain: chain,
              symbol: tokenInfo.symbol,
              name: tokenInfo.name,
              decimals: tokenInfo.decimals,
              totalSupply: tokenInfo.totalSupply,
              isVerified: false,
            },
            include: {
              metrics: true,
              riskAssessments: true,
              contractAnalysis: true,
            },
          });

          logger.info('New token added to database', { 
            chain, 
            address: normalizedAddress,
            symbol: tokenInfo.symbol,
          });
        } catch (blockchainError) {
          logger.error('Failed to fetch token from blockchain', {
            error: blockchainError.message,
            chain,
            address: normalizedAddress,
          });
          
          return res.status(404).json({
            success: false,
            error: 'Token not found',
          });
        }
      }

      const tokenData = {
        address: token.address,
        symbol: token.symbol,
        name: token.name,
        decimals: token.decimals,
        totalSupply: token.totalSupply,
        chain: token.chain,
        logoUrl: token.logoUrl,
        website: token.website,
        description: token.description,
        isVerified: token.isVerified,
        tags: token.tags,
        createdAt: token.createdAt,
        updatedAt: token.updatedAt,
        metrics: token.metrics[0] || null,
        riskAssessment: token.riskAssessments[0] || null,
        contractAnalysis: token.contractAnalysis[0] || null,
      };

      // Cache for 10 minutes
      await cacheService.set(cacheKey, tokenData, 600);

      logger.info('Token info retrieved', { 
        chain, 
        address: normalizedAddress,
        userId: req.user?.userId,
      });

      res.json({
        success: true,
        data: tokenData,
      });
    } catch (error) {
      logger.error('Failed to get token info', { 
        error: error.message, 
        chain: req.params.chain,
        address: req.params.address,
        userId: req.user?.userId,
      });
      res.status(500).json({
        success: false,
        error: 'Failed to get token information',
      });
    }
  }
);

/**
 * @swagger
 * /tokens/popular:
 *   get:
 *     summary: Get popular tokens
 *     tags: [Tokens]
 *     parameters:
 *       - in: query
 *         name: chain
 *         schema:
 *           type: string
 *         description: Blockchain network filter
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Maximum number of results
 *     responses:
 *       200:
 *         description: Popular tokens list
 */
router.get('/popular',
  tokenLimiter,
  optionalAuthMiddleware,
  [
    query('chain').optional().isIn(['ethereum', 'bsc', 'polygon', 'arbitrum', 'optimism']),
    query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
        });
      }

      const { chain, limit = 20 } = req.query;
      const cacheKey = `popular_tokens:${chain || 'all'}:${limit}`;

      // Check cache first
      const cachedTokens = await cacheService.get(cacheKey);
      if (cachedTokens) {
        return res.json({
          success: true,
          data: cachedTokens,
          cached: true,
        });
      }

      const tokens = await prisma.token.findMany({
        where: {
          ...(chain && { chain: chain as string }),
          isVerified: true,
        },
        take: limit as number,
        orderBy: [
          { metrics: { marketCap: 'desc' } },
          { metrics: { volume24h: 'desc' } },
        ],
        include: {
          metrics: {
            orderBy: { updatedAt: 'desc' },
            take: 1,
          },
          riskAssessments: {
            orderBy: { createdAt: 'desc' },
            take: 1,
          },
        },
      });

      const popularTokens = tokens.map(token => ({
        address: token.address,
        symbol: token.symbol,
        name: token.name,
        chain: token.chain,
        logoUrl: token.logoUrl,
        isVerified: token.isVerified,
        marketCap: token.metrics[0]?.marketCap,
        price: token.metrics[0]?.price,
        volume24h: token.metrics[0]?.volume24h,
        priceChange24h: token.metrics[0]?.priceChange24h,
        riskScore: token.riskAssessments[0]?.overallScore,
        tags: token.tags,
      }));

      // Cache for 15 minutes
      await cacheService.set(cacheKey, popularTokens, 900);

      res.json({
        success: true,
        data: popularTokens,
      });
    } catch (error) {
      logger.error('Failed to get popular tokens', { 
        error: error.message,
        userId: req.user?.userId,
      });
      res.status(500).json({
        success: false,
        error: 'Failed to get popular tokens',
      });
    }
  }
);

/**
 * @swagger
 * /tokens/trending:
 *   get:
 *     summary: Get trending tokens
 *     tags: [Tokens]
 *     parameters:
 *       - in: query
 *         name: timeframe
 *         schema:
 *           type: string
 *           enum: [1h, 24h, 7d]
 *           default: 24h
 *         description: Trending timeframe
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Maximum number of results
 *     responses:
 *       200:
 *         description: Trending tokens list
 */
router.get('/trending',
  tokenLimiter,
  optionalAuthMiddleware,
  [
    query('timeframe').optional().isIn(['1h', '24h', '7d']),
    query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
        });
      }

      const { timeframe = '24h', limit = 20 } = req.query;
      const cacheKey = `trending_tokens:${timeframe}:${limit}`;

      // Check cache first
      const cachedTokens = await cacheService.get(cacheKey);
      if (cachedTokens) {
        return res.json({
          success: true,
          data: cachedTokens,
          cached: true,
        });
      }

      // Calculate time threshold
      const now = new Date();
      let timeThreshold: Date;
      switch (timeframe) {
        case '1h':
          timeThreshold = new Date(now.getTime() - 60 * 60 * 1000);
          break;
        case '7d':
          timeThreshold = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        default: // 24h
          timeThreshold = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      }

      // Get tokens with recent high volume or price changes
      const tokens = await prisma.token.findMany({
        where: {
          metrics: {
            some: {
              updatedAt: {
                gte: timeThreshold,
              },
              OR: [
                { volumeChange24h: { gt: 50 } }, // Volume increased by 50%+
                { priceChange24h: { gt: 20 } },  // Price increased by 20%+
                { priceChange24h: { lt: -20 } }, // Price decreased by 20%+
              ],
            },
          },
        },
        take: limit as number,
        orderBy: [
          { metrics: { volumeChange24h: 'desc' } },
          { metrics: { volume24h: 'desc' } },
        ],
        include: {
          metrics: {
            orderBy: { updatedAt: 'desc' },
            take: 1,
          },
          riskAssessments: {
            orderBy: { createdAt: 'desc' },
            take: 1,
          },
        },
      });

      const trendingTokens = tokens.map(token => ({
        address: token.address,
        symbol: token.symbol,
        name: token.name,
        chain: token.chain,
        logoUrl: token.logoUrl,
        isVerified: token.isVerified,
        marketCap: token.metrics[0]?.marketCap,
        price: token.metrics[0]?.price,
        volume24h: token.metrics[0]?.volume24h,
        priceChange24h: token.metrics[0]?.priceChange24h,
        volumeChange24h: token.metrics[0]?.volumeChange24h,
        riskScore: token.riskAssessments[0]?.overallScore,
        tags: token.tags,
        trendingScore: Math.abs(token.metrics[0]?.priceChange24h || 0) + 
                      (token.metrics[0]?.volumeChange24h || 0),
      }));

      // Cache for 5 minutes (trending data should be fresh)
      await cacheService.set(cacheKey, trendingTokens, 300);

      res.json({
        success: true,
        data: trendingTokens,
      });
    } catch (error) {
      logger.error('Failed to get trending tokens', { 
        error: error.message,
        userId: req.user?.userId,
      });
      res.status(500).json({
        success: false,
        error: 'Failed to get trending tokens',
      });
    }
  }
);

export default router;
