/**
 * TokenForge Database Seeder
 * Seeds the database with initial data for development and testing
 */

import { PrismaClient } from '@prisma/client';
import { hashPassword } from '@tokenforge/shared';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  try {
    // Create admin user
    const adminUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        username: 'admin',
        passwordHash: await hashPassword('Admin123!'),
        firstName: 'Admin',
        lastName: 'User',
        emailVerified: true,
        role: 'ADMIN',
        status: 'ACTIVE',
        subscriptionPlan: 'ENTERPRISE',
        subscriptionStatus: 'ACTIVE',
      },
    });

    console.log('✅ Created admin user:', adminUser.email);

    // Create test user
    const testUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        username: 'testuser',
        passwordHash: await hashPassword('Test123!'),
        firstName: 'Test',
        lastName: 'User',
        emailVerified: true,
        role: 'USER',
        status: 'ACTIVE',
        subscriptionPlan: 'PRO',
        subscriptionStatus: 'ACTIVE',
      },
    });

    console.log('✅ Created test user:', testUser.email);

    // Create sample tokens
    const sampleTokens = [
      {
        address: '******************************************',
        chain: 'ethereum',
        symbol: 'USDC',
        name: 'USD Coin',
        decimals: 6,
        totalSupply: '1000000000000000',
        logoUrl: 'https://assets.coingecko.com/coins/images/6319/thumb/USD_Coin_icon.png',
        website: 'https://www.centre.io/',
        description: 'USD Coin (USDC) is a type of cryptocurrency that is referred to as a stablecoin.',
        isVerified: true,
        tags: ['stablecoin', 'defi'],
      },
      {
        address: '******************************************',
        chain: 'ethereum',
        symbol: 'USDT',
        name: 'Tether USD',
        decimals: 6,
        totalSupply: '1000000000000000',
        logoUrl: 'https://assets.coingecko.com/coins/images/325/thumb/Tether-logo.png',
        website: 'https://tether.to/',
        description: 'Tether gives you the joint benefits of open blockchain technology and traditional currency.',
        isVerified: true,
        tags: ['stablecoin'],
      },
      {
        address: '******************************************',
        chain: 'ethereum',
        symbol: 'UNI',
        name: 'Uniswap',
        decimals: 18,
        totalSupply: '1000000000000000000000000000',
        logoUrl: 'https://assets.coingecko.com/coins/images/12504/thumb/uniswap-uni.png',
        website: 'https://uniswap.org/',
        description: 'UNI is the governance token for Uniswap, an Automated Market Maker DEX on the Ethereum blockchain.',
        isVerified: true,
        tags: ['defi', 'dex', 'governance'],
      },
    ];

    const createdTokens = [];
    for (const tokenData of sampleTokens) {
      const token = await prisma.token.upsert({
        where: {
          address_chain: {
            address: tokenData.address,
            chain: tokenData.chain,
          },
        },
        update: {},
        create: tokenData,
      });
      createdTokens.push(token);
      console.log('✅ Created token:', token.symbol);
    }

    // Create sample token metrics
    for (const token of createdTokens) {
      await prisma.tokenMetrics.create({
        data: {
          tokenId: token.id,
          price: Math.random() * 100,
          marketCap: Math.random() * 1000000000,
          volume24h: Math.random() * 10000000,
          priceChange24h: (Math.random() - 0.5) * 20,
          volumeChange24h: (Math.random() - 0.5) * 50,
          circulatingSupply: Math.random() * 1000000000,
          holders: Math.floor(Math.random() * 100000),
          transactions24h: Math.floor(Math.random() * 10000),
        },
      });

      // Create sample risk assessment
      await prisma.riskAssessment.create({
        data: {
          tokenId: token.id,
          overallScore: Math.random() * 100,
          riskLevel: ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'][Math.floor(Math.random() * 4)] as any,
          confidence: 0.8 + Math.random() * 0.2,
          factors: [
            {
              name: 'Contract Verification',
              category: 'contract',
              severity: 'low',
              score: 10,
              weight: 0.2,
            },
            {
              name: 'Liquidity Analysis',
              category: 'liquidity',
              severity: 'medium',
              score: 30,
              weight: 0.3,
            },
          ],
          recommendations: [
            'Token appears to be legitimate',
            'Consider monitoring for unusual activity',
          ],
        },
      });

      console.log('✅ Created metrics and risk assessment for:', token.symbol);
    }

    // Create sample portfolio for test user
    const portfolio = await prisma.portfolio.create({
      data: {
        userId: testUser.id,
        name: 'My Portfolio',
        description: 'Test portfolio with sample tokens',
        isDefault: true,
        isPublic: false,
      },
    });

    // Add tokens to portfolio
    for (const token of createdTokens.slice(0, 2)) {
      await prisma.portfolioToken.create({
        data: {
          portfolioId: portfolio.id,
          tokenId: token.id,
          amount: (Math.random() * 1000).toString(),
          averagePrice: Math.random() * 10,
        },
      });
    }

    console.log('✅ Created sample portfolio');

    // Create sample alerts
    for (const token of createdTokens.slice(0, 2)) {
      await prisma.alert.create({
        data: {
          userId: testUser.id,
          tokenId: token.id,
          type: 'PRICE_ABOVE',
          condition: {
            threshold: Math.random() * 100,
            percentage: false,
          },
          isActive: true,
        },
      });
    }

    console.log('✅ Created sample alerts');

    // Create sample watchlist
    const watchlist = await prisma.watchlist.create({
      data: {
        userId: testUser.id,
        name: 'DeFi Tokens',
        description: 'Watching DeFi tokens for opportunities',
        isPublic: false,
      },
    });

    // Add tokens to watchlist
    for (const token of createdTokens) {
      await prisma.watchlistToken.create({
        data: {
          watchlistId: watchlist.id,
          tokenId: token.id,
        },
      });
    }

    console.log('✅ Created sample watchlist');

    // Create API key for test user
    await prisma.apiKey.create({
      data: {
        userId: testUser.id,
        name: 'Test API Key',
        key: 'tf_test_key_1234567890abcdef1234567890abcdef12345678',
        keyHash: 'hashed_key_value',
        permissions: ['read', 'analyze'],
        isActive: true,
      },
    });

    console.log('✅ Created sample API key');

    console.log('🎉 Database seeding completed successfully!');
  } catch (error) {
    console.error('❌ Error during seeding:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
