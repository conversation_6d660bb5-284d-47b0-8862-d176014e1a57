import Redis from 'ioredis';
import { logger } from '../utils/logger';

export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  prefix?: string;
  serialize?: boolean;
}

export class CacheService {
  private redis: Redis;
  private defaultTTL: number = 3600; // 1 hour
  private keyPrefix: string = 'cryptosentinel:';
  private isConnected: boolean = false;

  constructor() {
    this.initializeRedis();
  }

  private initializeRedis(): void {
    try {
      const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
      
      this.redis = new Redis(redisUrl, {
        retryDelayOnFailover: 100,
        enableReadyCheck: true,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
        keepAlive: 30000,
        connectTimeout: 10000,
        commandTimeout: 5000,
      });

      this.redis.on('connect', () => {
        this.isConnected = true;
        logger.info('Redis connected successfully');
      });

      this.redis.on('error', (error) => {
        this.isConnected = false;
        logger.error('Redis connection error', { error: error.message });
      });

      this.redis.on('close', () => {
        this.isConnected = false;
        logger.warn('Redis connection closed');
      });

      this.redis.on('reconnecting', () => {
        logger.info('Redis reconnecting...');
      });

    } catch (error) {
      logger.error('Failed to initialize Redis', { error: error.message });
    }
  }

  /**
   * Get value from cache
   */
  async get<T = any>(key: string, options: CacheOptions = {}): Promise<T | null> {
    try {
      if (!this.isConnected) {
        logger.warn('Redis not connected, skipping cache get');
        return null;
      }

      const fullKey = this.buildKey(key, options.prefix);
      const value = await this.redis.get(fullKey);

      if (value === null) {
        return null;
      }

      if (options.serialize !== false) {
        try {
          return JSON.parse(value);
        } catch (parseError) {
          logger.warn('Failed to parse cached value', { 
            key: fullKey, 
            error: parseError.message 
          });
          return value as T;
        }
      }

      return value as T;
    } catch (error) {
      logger.error('Cache get failed', { 
        key, 
        error: error.message 
      });
      return null;
    }
  }

  /**
   * Set value in cache
   */
  async set(
    key: string, 
    value: any, 
    ttl?: number, 
    options: CacheOptions = {}
  ): Promise<boolean> {
    try {
      if (!this.isConnected) {
        logger.warn('Redis not connected, skipping cache set');
        return false;
      }

      const fullKey = this.buildKey(key, options.prefix);
      const expiration = ttl || options.ttl || this.defaultTTL;
      
      let serializedValue: string;
      if (options.serialize !== false) {
        serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
      } else {
        serializedValue = value;
      }

      const result = await this.redis.setex(fullKey, expiration, serializedValue);
      return result === 'OK';
    } catch (error) {
      logger.error('Cache set failed', { 
        key, 
        error: error.message 
      });
      return false;
    }
  }

  /**
   * Delete value from cache
   */
  async del(key: string, options: CacheOptions = {}): Promise<boolean> {
    try {
      if (!this.isConnected) {
        return false;
      }

      const fullKey = this.buildKey(key, options.prefix);
      const result = await this.redis.del(fullKey);
      return result > 0;
    } catch (error) {
      logger.error('Cache delete failed', { 
        key, 
        error: error.message 
      });
      return false;
    }
  }

  /**
   * Check if key exists in cache
   */
  async exists(key: string, options: CacheOptions = {}): Promise<boolean> {
    try {
      if (!this.isConnected) {
        return false;
      }

      const fullKey = this.buildKey(key, options.prefix);
      const result = await this.redis.exists(fullKey);
      return result === 1;
    } catch (error) {
      logger.error('Cache exists check failed', { 
        key, 
        error: error.message 
      });
      return false;
    }
  }

  /**
   * Get multiple values from cache
   */
  async mget<T = any>(keys: string[], options: CacheOptions = {}): Promise<(T | null)[]> {
    try {
      if (!this.isConnected || keys.length === 0) {
        return keys.map(() => null);
      }

      const fullKeys = keys.map(key => this.buildKey(key, options.prefix));
      const values = await this.redis.mget(...fullKeys);

      return values.map(value => {
        if (value === null) {
          return null;
        }

        if (options.serialize !== false) {
          try {
            return JSON.parse(value);
          } catch (parseError) {
            return value as T;
          }
        }

        return value as T;
      });
    } catch (error) {
      logger.error('Cache mget failed', { 
        keys, 
        error: error.message 
      });
      return keys.map(() => null);
    }
  }

  /**
   * Set multiple values in cache
   */
  async mset(
    keyValuePairs: Array<{ key: string; value: any; ttl?: number }>,
    options: CacheOptions = {}
  ): Promise<boolean> {
    try {
      if (!this.isConnected || keyValuePairs.length === 0) {
        return false;
      }

      const pipeline = this.redis.pipeline();

      for (const { key, value, ttl } of keyValuePairs) {
        const fullKey = this.buildKey(key, options.prefix);
        const expiration = ttl || options.ttl || this.defaultTTL;
        
        let serializedValue: string;
        if (options.serialize !== false) {
          serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
        } else {
          serializedValue = value;
        }

        pipeline.setex(fullKey, expiration, serializedValue);
      }

      const results = await pipeline.exec();
      return results?.every(([error, result]) => error === null && result === 'OK') || false;
    } catch (error) {
      logger.error('Cache mset failed', { 
        count: keyValuePairs.length, 
        error: error.message 
      });
      return false;
    }
  }

  /**
   * Increment a numeric value in cache
   */
  async incr(key: string, options: CacheOptions = {}): Promise<number> {
    try {
      if (!this.isConnected) {
        return 0;
      }

      const fullKey = this.buildKey(key, options.prefix);
      const result = await this.redis.incr(fullKey);
      
      // Set expiration if it's a new key
      if (result === 1 && (options.ttl || this.defaultTTL)) {
        await this.redis.expire(fullKey, options.ttl || this.defaultTTL);
      }
      
      return result;
    } catch (error) {
      logger.error('Cache incr failed', { 
        key, 
        error: error.message 
      });
      return 0;
    }
  }

  /**
   * Decrement a numeric value in cache
   */
  async decr(key: string, options: CacheOptions = {}): Promise<number> {
    try {
      if (!this.isConnected) {
        return 0;
      }

      const fullKey = this.buildKey(key, options.prefix);
      return await this.redis.decr(fullKey);
    } catch (error) {
      logger.error('Cache decr failed', { 
        key, 
        error: error.message 
      });
      return 0;
    }
  }

  /**
   * Get keys matching a pattern
   */
  async keys(pattern: string, options: CacheOptions = {}): Promise<string[]> {
    try {
      if (!this.isConnected) {
        return [];
      }

      const fullPattern = this.buildKey(pattern, options.prefix);
      return await this.redis.keys(fullPattern);
    } catch (error) {
      logger.error('Cache keys failed', { 
        pattern, 
        error: error.message 
      });
      return [];
    }
  }

  /**
   * Clear all keys matching a pattern
   */
  async clear(pattern: string = '*', options: CacheOptions = {}): Promise<number> {
    try {
      if (!this.isConnected) {
        return 0;
      }

      const fullPattern = this.buildKey(pattern, options.prefix);
      const keys = await this.redis.keys(fullPattern);
      
      if (keys.length === 0) {
        return 0;
      }

      return await this.redis.del(...keys);
    } catch (error) {
      logger.error('Cache clear failed', { 
        pattern, 
        error: error.message 
      });
      return 0;
    }
  }

  /**
   * Set expiration for a key
   */
  async expire(key: string, ttl: number, options: CacheOptions = {}): Promise<boolean> {
    try {
      if (!this.isConnected) {
        return false;
      }

      const fullKey = this.buildKey(key, options.prefix);
      const result = await this.redis.expire(fullKey, ttl);
      return result === 1;
    } catch (error) {
      logger.error('Cache expire failed', { 
        key, 
        ttl, 
        error: error.message 
      });
      return false;
    }
  }

  /**
   * Get time to live for a key
   */
  async ttl(key: string, options: CacheOptions = {}): Promise<number> {
    try {
      if (!this.isConnected) {
        return -1;
      }

      const fullKey = this.buildKey(key, options.prefix);
      return await this.redis.ttl(fullKey);
    } catch (error) {
      logger.error('Cache ttl failed', { 
        key, 
        error: error.message 
      });
      return -1;
    }
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<any> {
    try {
      if (!this.isConnected) {
        return null;
      }

      const info = await this.redis.info('memory');
      const keyspace = await this.redis.info('keyspace');
      
      return {
        connected: this.isConnected,
        memory: info,
        keyspace: keyspace,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logger.error('Failed to get cache stats', { error: error.message });
      return null;
    }
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      if (!this.isConnected) {
        return false;
      }

      const result = await this.redis.ping();
      return result === 'PONG';
    } catch (error) {
      logger.error('Cache health check failed', { error: error.message });
      return false;
    }
  }

  /**
   * Close Redis connection
   */
  async disconnect(): Promise<void> {
    try {
      await this.redis.quit();
      this.isConnected = false;
      logger.info('Redis disconnected');
    } catch (error) {
      logger.error('Failed to disconnect Redis', { error: error.message });
    }
  }

  /**
   * Build full cache key with prefix
   */
  private buildKey(key: string, prefix?: string): string {
    const keyPrefix = prefix || this.keyPrefix;
    return `${keyPrefix}${key}`;
  }

  /**
   * Get connection status
   */
  public isHealthy(): boolean {
    return this.isConnected;
  }
}

// Export singleton instance
export default new CacheService();
