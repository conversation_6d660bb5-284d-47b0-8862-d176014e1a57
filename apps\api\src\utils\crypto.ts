import crypto from 'crypto';
import bcrypt from 'bcryptjs';

/**
 * Generate a cryptographically secure random nonce
 * @param length - Length of the nonce (default: 32)
 * @returns Random nonce string
 */
export const generateNonce = (length: number = 32): string => {
  return crypto.randomBytes(length).toString('hex');
};

/**
 * Generate a random API key
 * @param length - Length of the API key (default: 64)
 * @returns Random API key string
 */
export const generateApiKey = (length: number = 64): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

/**
 * Generate a secure random token
 * @param bytes - Number of bytes (default: 32)
 * @returns Base64 encoded token
 */
export const generateSecureToken = (bytes: number = 32): string => {
  return crypto.randomBytes(bytes).toString('base64url');
};

/**
 * Hash a password using bcrypt
 * @param password - Plain text password
 * @param rounds - Number of salt rounds (default: 12)
 * @returns Hashed password
 */
export const hashPassword = async (password: string, rounds: number = 12): Promise<string> => {
  return bcrypt.hash(password, rounds);
};

/**
 * Verify a password against its hash
 * @param password - Plain text password
 * @param hash - Hashed password
 * @returns True if password matches
 */
export const verifyPassword = async (password: string, hash: string): Promise<boolean> => {
  return bcrypt.compare(password, hash);
};

/**
 * Create HMAC signature
 * @param data - Data to sign
 * @param secret - Secret key
 * @param algorithm - HMAC algorithm (default: sha256)
 * @returns HMAC signature
 */
export const createHmacSignature = (
  data: string,
  secret: string,
  algorithm: string = 'sha256'
): string => {
  return crypto.createHmac(algorithm, secret).update(data).digest('hex');
};

/**
 * Verify HMAC signature
 * @param data - Original data
 * @param signature - Signature to verify
 * @param secret - Secret key
 * @param algorithm - HMAC algorithm (default: sha256)
 * @returns True if signature is valid
 */
export const verifyHmacSignature = (
  data: string,
  signature: string,
  secret: string,
  algorithm: string = 'sha256'
): boolean => {
  const expectedSignature = createHmacSignature(data, secret, algorithm);
  return crypto.timingSafeEqual(
    Buffer.from(signature, 'hex'),
    Buffer.from(expectedSignature, 'hex')
  );
};

/**
 * Encrypt data using AES-256-GCM
 * @param text - Text to encrypt
 * @param key - Encryption key (32 bytes)
 * @returns Encrypted data with IV and auth tag
 */
export const encrypt = (text: string, key: string): string => {
  const algorithm = 'aes-256-gcm';
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher(algorithm, key);
  cipher.setAAD(Buffer.from('CryptoSentinel', 'utf8'));
  
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  const authTag = cipher.getAuthTag();
  
  return JSON.stringify({
    iv: iv.toString('hex'),
    encrypted,
    authTag: authTag.toString('hex'),
  });
};

/**
 * Decrypt data using AES-256-GCM
 * @param encryptedData - Encrypted data string
 * @param key - Decryption key (32 bytes)
 * @returns Decrypted text
 */
export const decrypt = (encryptedData: string, key: string): string => {
  const algorithm = 'aes-256-gcm';
  const { iv, encrypted, authTag } = JSON.parse(encryptedData);
  
  const decipher = crypto.createDecipher(algorithm, key);
  decipher.setAAD(Buffer.from('CryptoSentinel', 'utf8'));
  decipher.setAuthTag(Buffer.from(authTag, 'hex'));
  
  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  
  return decrypted;
};

/**
 * Generate a deterministic hash from input
 * @param input - Input string
 * @param algorithm - Hash algorithm (default: sha256)
 * @returns Hash string
 */
export const generateHash = (input: string, algorithm: string = 'sha256'): string => {
  return crypto.createHash(algorithm).update(input).digest('hex');
};

/**
 * Generate a UUID v4
 * @returns UUID string
 */
export const generateUUID = (): string => {
  return crypto.randomUUID();
};

/**
 * Validate Ethereum address format
 * @param address - Ethereum address
 * @returns True if valid format
 */
export const isValidEthereumAddress = (address: string): boolean => {
  return /^0x[a-fA-F0-9]{40}$/.test(address);
};

/**
 * Normalize Ethereum address to lowercase
 * @param address - Ethereum address
 * @returns Normalized address
 */
export const normalizeEthereumAddress = (address: string): string => {
  if (!isValidEthereumAddress(address)) {
    throw new Error('Invalid Ethereum address format');
  }
  return address.toLowerCase();
};

/**
 * Generate a secure session ID
 * @returns Session ID string
 */
export const generateSessionId = (): string => {
  return generateSecureToken(48);
};

/**
 * Create a time-based one-time password (TOTP) secret
 * @returns Base32 encoded secret
 */
export const generateTotpSecret = (): string => {
  const secret = crypto.randomBytes(20);
  return secret.toString('base32');
};

/**
 * Mask sensitive data for logging
 * @param data - Sensitive data
 * @param visibleChars - Number of visible characters at start and end
 * @returns Masked string
 */
export const maskSensitiveData = (data: string, visibleChars: number = 4): string => {
  if (data.length <= visibleChars * 2) {
    return '*'.repeat(data.length);
  }
  
  const start = data.substring(0, visibleChars);
  const end = data.substring(data.length - visibleChars);
  const middle = '*'.repeat(data.length - visibleChars * 2);
  
  return `${start}${middle}${end}`;
};

/**
 * Generate a cryptographically secure random integer
 * @param min - Minimum value (inclusive)
 * @param max - Maximum value (exclusive)
 * @returns Random integer
 */
export const generateSecureRandomInt = (min: number, max: number): number => {
  const range = max - min;
  const bytesNeeded = Math.ceil(Math.log2(range) / 8);
  const maxValue = Math.pow(256, bytesNeeded);
  const threshold = maxValue - (maxValue % range);
  
  let randomValue;
  do {
    const randomBytes = crypto.randomBytes(bytesNeeded);
    randomValue = randomBytes.readUIntBE(0, bytesNeeded);
  } while (randomValue >= threshold);
  
  return min + (randomValue % range);
};

/**
 * Create a rate limiting key
 * @param identifier - User identifier (IP, user ID, etc.)
 * @param action - Action being rate limited
 * @returns Rate limiting key
 */
export const createRateLimitKey = (identifier: string, action: string): string => {
  return `rate_limit:${action}:${generateHash(identifier).substring(0, 16)}`;
};

/**
 * Validate and sanitize input for cryptographic operations
 * @param input - Input string
 * @param maxLength - Maximum allowed length
 * @returns Sanitized input
 */
export const sanitizeCryptoInput = (input: string, maxLength: number = 1000): string => {
  if (typeof input !== 'string') {
    throw new Error('Input must be a string');
  }
  
  if (input.length > maxLength) {
    throw new Error(`Input too long (max ${maxLength} characters)`);
  }
  
  // Remove null bytes and control characters
  return input.replace(/[\x00-\x1F\x7F]/g, '');
};

export default {
  generateNonce,
  generateApiKey,
  generateSecureToken,
  hashPassword,
  verifyPassword,
  createHmacSignature,
  verifyHmacSignature,
  encrypt,
  decrypt,
  generateHash,
  generateUUID,
  isValidEthereumAddress,
  normalizeEthereumAddress,
  generateSessionId,
  generateTotpSecret,
  maskSensitiveData,
  generateSecureRandomInt,
  createRateLimitKey,
  sanitizeCryptoInput,
};
