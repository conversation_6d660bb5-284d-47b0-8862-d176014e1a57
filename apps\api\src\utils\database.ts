import { PrismaClient } from '@prisma/client';
import { logger } from './logger';

// Global Prisma instance
declare global {
  var __prisma: PrismaClient | undefined;
}

// Database connection configuration
const DATABASE_CONFIG = {
  log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
  errorFormat: 'pretty' as const,
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
};

// Create Prisma client instance
function createPrismaClient(): PrismaClient {
  const prisma = new PrismaClient(DATABASE_CONFIG);

  // Add query logging middleware
  prisma.$use(async (params, next) => {
    const before = Date.now();
    const result = await next(params);
    const after = Date.now();
    
    if (process.env.LOG_DATABASE_QUERIES === 'true') {
      logger.debug(`Query ${params.model}.${params.action} took ${after - before}ms`, {
        model: params.model,
        action: params.action,
        duration: after - before,
      });
    }
    
    return result;
  });

  // Add error handling middleware
  prisma.$use(async (params, next) => {
    try {
      return await next(params);
    } catch (error) {
      logger.error('Database query failed', {
        model: params.model,
        action: params.action,
        error: error.message,
      });
      throw error;
    }
  });

  return prisma;
}

// Get or create Prisma client (singleton pattern)
export const prisma = globalThis.__prisma || createPrismaClient();

if (process.env.NODE_ENV !== 'production') {
  globalThis.__prisma = prisma;
}

// Database connection utilities
export class DatabaseManager {
  private static instance: DatabaseManager;
  private prismaClient: PrismaClient;
  private isConnected: boolean = false;

  private constructor() {
    this.prismaClient = prisma;
  }

  public static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager();
    }
    return DatabaseManager.instance;
  }

  /**
   * Connect to the database
   */
  public async connect(): Promise<void> {
    try {
      await this.prismaClient.$connect();
      this.isConnected = true;
      logger.info('Database connected successfully');
    } catch (error) {
      logger.error('Failed to connect to database', { error: error.message });
      throw error;
    }
  }

  /**
   * Disconnect from the database
   */
  public async disconnect(): Promise<void> {
    try {
      await this.prismaClient.$disconnect();
      this.isConnected = false;
      logger.info('Database disconnected successfully');
    } catch (error) {
      logger.error('Failed to disconnect from database', { error: error.message });
      throw error;
    }
  }

  /**
   * Check database connection health
   */
  public async healthCheck(): Promise<boolean> {
    try {
      await this.prismaClient.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      logger.error('Database health check failed', { error: error.message });
      return false;
    }
  }

  /**
   * Get database statistics
   */
  public async getStats(): Promise<any> {
    try {
      const [
        userCount,
        tokenCount,
        analysisCount,
        portfolioCount,
        alertCount,
      ] = await Promise.all([
        this.prismaClient.user.count(),
        this.prismaClient.token.count(),
        this.prismaClient.riskAssessment.count(),
        this.prismaClient.portfolio.count(),
        this.prismaClient.alert.count(),
      ]);

      return {
        users: userCount,
        tokens: tokenCount,
        analyses: analysisCount,
        portfolios: portfolioCount,
        alerts: alertCount,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logger.error('Failed to get database stats', { error: error.message });
      throw error;
    }
  }

  /**
   * Run database migrations
   */
  public async runMigrations(): Promise<void> {
    try {
      // This would typically be handled by Prisma CLI
      // but we can add custom migration logic here if needed
      logger.info('Running database migrations...');
      
      // Example: Create indexes if they don't exist
      await this.createIndexes();
      
      logger.info('Database migrations completed');
    } catch (error) {
      logger.error('Database migrations failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Create additional database indexes for performance
   */
  private async createIndexes(): Promise<void> {
    try {
      // Create composite indexes for better query performance
      const indexes = [
        // Token analysis performance
        'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tokens_chain_verified ON tokens(chain, is_verified)',
        'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_risk_assessments_score ON risk_assessments(overall_score)',
        'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_token_metrics_updated ON token_metrics(updated_at DESC)',
        
        // User activity indexes
        'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_analysis_history_user_created ON analysis_history(user_id, created_at DESC)',
        'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_portfolio_tokens_portfolio ON portfolio_tokens(portfolio_id, added_at DESC)',
        
        // Alert system indexes
        'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_alerts_user_active ON alerts(user_id, is_active)',
        'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_alerts_token_active ON alerts(token_id, is_active)',
      ];

      for (const indexQuery of indexes) {
        try {
          await this.prismaClient.$executeRawUnsafe(indexQuery);
          logger.debug('Created database index', { query: indexQuery });
        } catch (error) {
          // Index might already exist, log but don't fail
          logger.debug('Index creation skipped (might already exist)', { 
            query: indexQuery,
            error: error.message 
          });
        }
      }
    } catch (error) {
      logger.error('Failed to create database indexes', { error: error.message });
      // Don't throw here as indexes are optional performance improvements
    }
  }

  /**
   * Clean up old data
   */
  public async cleanup(): Promise<void> {
    try {
      logger.info('Starting database cleanup...');

      // Clean up old auth nonces (older than 1 hour)
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
      const deletedNonces = await this.prismaClient.authNonce.deleteMany({
        where: {
          expiresAt: {
            lt: oneHourAgo,
          },
        },
      });

      // Clean up old analysis history (older than 90 days for non-premium users)
      const ninetyDaysAgo = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
      const deletedHistory = await this.prismaClient.analysisHistory.deleteMany({
        where: {
          createdAt: {
            lt: ninetyDaysAgo,
          },
          user: {
            isPremium: false,
          },
        },
      });

      // Clean up old token metrics (keep only latest 30 days)
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const deletedMetrics = await this.prismaClient.tokenMetrics.deleteMany({
        where: {
          createdAt: {
            lt: thirtyDaysAgo,
          },
        },
      });

      logger.info('Database cleanup completed', {
        deletedNonces: deletedNonces.count,
        deletedHistory: deletedHistory.count,
        deletedMetrics: deletedMetrics.count,
      });
    } catch (error) {
      logger.error('Database cleanup failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Backup database (for development/testing)
   */
  public async backup(backupPath?: string): Promise<string> {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = backupPath || `backup-${timestamp}.sql`;
      
      // This would typically use pg_dump or similar
      // For now, just log the backup request
      logger.info('Database backup requested', { filename });
      
      return filename;
    } catch (error) {
      logger.error('Database backup failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Get connection status
   */
  public isConnectionHealthy(): boolean {
    return this.isConnected;
  }

  /**
   * Get Prisma client instance
   */
  public getClient(): PrismaClient {
    return this.prismaClient;
  }
}

// Transaction helper
export async function withTransaction<T>(
  callback: (prisma: PrismaClient) => Promise<T>
): Promise<T> {
  return prisma.$transaction(callback);
}

// Pagination helper
export interface PaginationOptions {
  page?: number;
  limit?: number;
  orderBy?: any;
}

export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export async function paginate<T>(
  model: any,
  options: PaginationOptions = {},
  where: any = {}
): Promise<PaginatedResult<T>> {
  const page = Math.max(1, options.page || 1);
  const limit = Math.min(100, Math.max(1, options.limit || 10));
  const skip = (page - 1) * limit;

  const [data, total] = await Promise.all([
    model.findMany({
      where,
      skip,
      take: limit,
      orderBy: options.orderBy,
    }),
    model.count({ where }),
  ]);

  const pages = Math.ceil(total / limit);

  return {
    data,
    pagination: {
      page,
      limit,
      total,
      pages,
      hasNext: page < pages,
      hasPrev: page > 1,
    },
  };
}

// Export default instance
export default DatabaseManager.getInstance();
