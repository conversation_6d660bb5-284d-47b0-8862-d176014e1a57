import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import path from 'path';

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define colors for each level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

// Tell winston that you want to link the colors
winston.addColors(colors);

// Define which level to log based on environment
const level = () => {
  const env = process.env.NODE_ENV || 'development';
  const isDevelopment = env === 'development';
  return isDevelopment ? 'debug' : 'warn';
};

// Define format for logs
const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`,
  ),
);

// Define format for file logs (without colors)
const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
);

// Create logs directory if it doesn't exist
const logsDir = path.join(process.cwd(), 'logs');

// Define transports
const transports = [
  // Console transport
  new winston.transports.Console({
    level: level(),
    format,
  }),
  
  // Error log file
  new DailyRotateFile({
    filename: path.join(logsDir, 'error-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    level: 'error',
    format: fileFormat,
    maxSize: '20m',
    maxFiles: '14d',
    zippedArchive: true,
  }),
  
  // Combined log file
  new DailyRotateFile({
    filename: path.join(logsDir, 'combined-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    format: fileFormat,
    maxSize: '20m',
    maxFiles: '14d',
    zippedArchive: true,
  }),
];

// Create the logger
const logger = winston.createLogger({
  level: level(),
  levels,
  format: fileFormat,
  transports,
  exitOnError: false,
});

// Create a stream object for Morgan HTTP logging
logger.stream = {
  write: (message: string) => {
    logger.http(message.trim());
  },
};

// Add request ID to logs for better tracing
export const addRequestId = (req: any, res: any, next: any) => {
  req.requestId = Math.random().toString(36).substring(2, 15);
  next();
};

// Enhanced logger with request context
export const createContextLogger = (context: string) => {
  return {
    error: (message: string, meta?: any) => {
      logger.error(`[${context}] ${message}`, meta);
    },
    warn: (message: string, meta?: any) => {
      logger.warn(`[${context}] ${message}`, meta);
    },
    info: (message: string, meta?: any) => {
      logger.info(`[${context}] ${message}`, meta);
    },
    http: (message: string, meta?: any) => {
      logger.http(`[${context}] ${message}`, meta);
    },
    debug: (message: string, meta?: any) => {
      logger.debug(`[${context}] ${message}`, meta);
    },
  };
};

// Performance logging utility
export const logPerformance = (operation: string, startTime: number, meta?: any) => {
  const duration = Date.now() - startTime;
  logger.info(`Performance: ${operation} completed in ${duration}ms`, {
    operation,
    duration,
    ...meta,
  });
};

// Database query logging
export const logDatabaseQuery = (query: string, duration: number, meta?: any) => {
  if (process.env.LOG_DATABASE_QUERIES === 'true') {
    logger.debug(`Database Query: ${query} (${duration}ms)`, {
      query,
      duration,
      ...meta,
    });
  }
};

// API request logging
export const logApiRequest = (method: string, url: string, statusCode: number, duration: number, meta?: any) => {
  const level = statusCode >= 400 ? 'warn' : 'info';
  logger[level](`API Request: ${method} ${url} - ${statusCode} (${duration}ms)`, {
    method,
    url,
    statusCode,
    duration,
    ...meta,
  });
};

// Error logging with stack trace
export const logError = (error: Error, context?: string, meta?: any) => {
  logger.error(`${context ? `[${context}] ` : ''}${error.message}`, {
    error: error.message,
    stack: error.stack,
    context,
    ...meta,
  });
};

// Security event logging
export const logSecurityEvent = (event: string, severity: 'low' | 'medium' | 'high' | 'critical', meta?: any) => {
  const level = severity === 'critical' || severity === 'high' ? 'error' : 'warn';
  logger[level](`Security Event: ${event}`, {
    event,
    severity,
    timestamp: new Date().toISOString(),
    ...meta,
  });
};

// Business logic logging
export const logBusinessEvent = (event: string, meta?: any) => {
  logger.info(`Business Event: ${event}`, {
    event,
    timestamp: new Date().toISOString(),
    ...meta,
  });
};

// Audit logging for sensitive operations
export const logAuditEvent = (action: string, userId?: string, meta?: any) => {
  logger.info(`Audit: ${action}`, {
    action,
    userId,
    timestamp: new Date().toISOString(),
    ...meta,
  });
};

export { logger };
export default logger;
