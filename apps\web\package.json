{"name": "@cryptosentinel/web", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "clean": "rm -rf .next out dist"}, "dependencies": {"next": "^14.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.8.4", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "recharts": "^2.8.0", "framer-motion": "^10.16.5", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "react-hot-toast": "^2.4.1", "react-intersection-observer": "^9.5.3", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8", "date-fns": "^2.30.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "lucide-react": "^0.294.0", "ethers": "^6.8.1", "@wagmi/core": "^1.4.12", "wagmi": "^1.4.12", "viem": "^1.19.9", "@rainbow-me/rainbowkit": "^1.3.0", "web3": "^4.2.2"}, "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.17", "@types/react-window": "^1.8.8", "typescript": "^5.3.2", "eslint": "^8.54.0", "eslint-config-next": "^14.0.3", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "@playwright/test": "^1.40.1", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}