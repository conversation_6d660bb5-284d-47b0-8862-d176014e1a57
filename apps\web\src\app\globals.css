@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');

/* CSS Variables for theming */
:root {
  --font-inter: 'Inter', system-ui, sans-serif;
  --font-jetbrains-mono: 'JetBrains Mono', 'Menlo', 'Monaco', monospace;
  
  /* Light theme colors */
  --color-background: 248 250 252;
  --color-foreground: 15 23 42;
  --color-card: 255 255 255;
  --color-card-foreground: 15 23 42;
  --color-popover: 255 255 255;
  --color-popover-foreground: 15 23 42;
  --color-primary: 59 130 246;
  --color-primary-foreground: 255 255 255;
  --color-secondary: 241 245 249;
  --color-secondary-foreground: 15 23 42;
  --color-muted: 241 245 249;
  --color-muted-foreground: 100 116 139;
  --color-accent: 241 245 249;
  --color-accent-foreground: 15 23 42;
  --color-destructive: 239 68 68;
  --color-destructive-foreground: 255 255 255;
  --color-border: 226 232 240;
  --color-input: 226 232 240;
  --color-ring: 59 130 246;
  --radius: 0.5rem;
}

.dark {
  /* Dark theme colors */
  --color-background: 2 6 23;
  --color-foreground: 248 250 252;
  --color-card: 15 23 42;
  --color-card-foreground: 248 250 252;
  --color-popover: 15 23 42;
  --color-popover-foreground: 248 250 252;
  --color-primary: 59 130 246;
  --color-primary-foreground: 255 255 255;
  --color-secondary: 30 41 59;
  --color-secondary-foreground: 248 250 252;
  --color-muted: 30 41 59;
  --color-muted-foreground: 148 163 184;
  --color-accent: 30 41 59;
  --color-accent-foreground: 248 250 252;
  --color-destructive: 239 68 68;
  --color-destructive-foreground: 255 255 255;
  --color-border: 51 65 85;
  --color-input: 51 65 85;
  --color-ring: 59 130 246;
}

/* Base styles */
* {
  @apply border-border;
}

html {
  scroll-behavior: smooth;
}

body {
  @apply bg-background text-foreground;
  font-feature-settings: "rlig" 1, "calt" 1;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-muted;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/30 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/50;
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: rgb(var(--color-muted-foreground) / 0.3) rgb(var(--color-muted));
}

/* Selection styles */
::selection {
  @apply bg-primary/20 text-primary-foreground;
}

::-moz-selection {
  @apply bg-primary/20 text-primary-foreground;
}

/* Focus styles */
:focus-visible {
  @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
}

/* Custom components */
@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }

  .btn-primary {
    @apply btn bg-primary text-primary-foreground hover:bg-primary/90;
  }

  .btn-secondary {
    @apply btn bg-secondary text-secondary-foreground hover:bg-secondary/80;
  }

  .btn-destructive {
    @apply btn bg-destructive text-destructive-foreground hover:bg-destructive/90;
  }

  .btn-outline {
    @apply btn border border-input bg-background hover:bg-accent hover:text-accent-foreground;
  }

  .btn-ghost {
    @apply btn hover:bg-accent hover:text-accent-foreground;
  }

  .btn-link {
    @apply btn text-primary underline-offset-4 hover:underline;
  }

  .card {
    @apply rounded-lg border bg-card text-card-foreground shadow-sm;
  }

  .input {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .textarea {
    @apply flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .label {
    @apply text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70;
  }

  .badge {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  .badge-default {
    @apply badge border-transparent bg-primary text-primary-foreground hover:bg-primary/80;
  }

  .badge-secondary {
    @apply badge border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80;
  }

  .badge-destructive {
    @apply badge border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80;
  }

  .badge-outline {
    @apply badge text-foreground;
  }

  /* Risk level badges */
  .badge-risk-low {
    @apply badge border-transparent bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400;
  }

  .badge-risk-medium {
    @apply badge border-transparent bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400;
  }

  .badge-risk-high {
    @apply badge border-transparent bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400;
  }

  .badge-risk-critical {
    @apply badge border-transparent bg-red-200 text-red-900 dark:bg-red-900/40 dark:text-red-300;
  }

  /* Glass morphism effect */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .glass-dark {
    @apply bg-black/10 backdrop-blur-md border border-white/10;
  }

  /* Gradient text */
  .text-gradient {
    @apply bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent;
  }

  /* Loading skeleton */
  .skeleton {
    @apply animate-pulse bg-muted rounded;
  }

  /* Custom animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }
}

/* Keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Print styles */
@media print {
  * {
    @apply text-black bg-white;
  }
  
  .no-print {
    display: none !important;
  }
}
