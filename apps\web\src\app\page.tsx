import { Metadata } from 'next';
import { HeroSection } from '@/components/sections/HeroSection';
import { FeaturesSection } from '@/components/sections/FeaturesSection';
import { StatsSection } from '@/components/sections/StatsSection';
import { HowItWorksSection } from '@/components/sections/HowItWorksSection';
import { TestimonialsSection } from '@/components/sections/TestimonialsSection';
import { CTASection } from '@/components/sections/CTASection';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';

export const metadata: Metadata = {
  title: 'CryptoSentinel - AI-Powered Token Risk Assessment',
  description: 'Protect your investments with advanced AI-powered cryptocurrency token analysis. Real-time scam detection, social sentiment analysis, and comprehensive security scoring.',
  openGraph: {
    title: 'CryptoSentinel - AI-Powered Token Risk Assessment',
    description: 'Protect your investments with advanced AI-powered cryptocurrency token analysis.',
    images: ['/og-home.png'],
  },
};

export default function HomePage() {
  return (
    <>
      <Header />
      <main className="flex-1">
        <HeroSection />
        <StatsSection />
        <FeaturesSection />
        <HowItWorksSection />
        <TestimonialsSection />
        <CTASection />
      </main>
      <Footer />
    </>
  );
}
