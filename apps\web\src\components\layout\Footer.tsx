'use client';

import Link from 'next/link';
import { 
  ShieldCheckIcon,
  EnvelopeIcon,
  MapPinIcon,
  PhoneIcon
} from '@heroicons/react/24/outline';
import {
  TwitterIcon,
  GitHubIcon,
  DiscordIcon,
  TelegramIcon
} from '@/components/ui/SocialIcons';

const navigation = {
  product: [
    { name: 'Token Analysis', href: '/analyze' },
    { name: 'Portfolio Tracker', href: '/portfolio' },
    { name: 'Risk Alerts', href: '/alerts' },
    { name: 'API Access', href: '/api' },
  ],
  company: [
    { name: 'About Us', href: '/about' },
    { name: 'Blog', href: '/blog' },
    { name: 'Careers', href: '/careers' },
    { name: 'Press Kit', href: '/press' },
  ],
  resources: [
    { name: 'Documentation', href: '/docs' },
    { name: 'Help Center', href: '/help' },
    { name: 'Security', href: '/security' },
    { name: 'Status', href: '/status' },
  ],
  legal: [
    { name: 'Privacy Policy', href: '/privacy' },
    { name: 'Terms of Service', href: '/terms' },
    { name: 'Cookie Policy', href: '/cookies' },
    { name: 'Disclaimer', href: '/disclaimer' },
  ],
};

const socialLinks = [
  {
    name: 'Twitter',
    href: 'https://twitter.com/cryptosentinel',
    icon: TwitterIcon,
  },
  {
    name: 'GitHub',
    href: 'https://github.com/HectorTa1989/cryptosentinel',
    icon: GitHubIcon,
  },
  {
    name: 'Discord',
    href: 'https://discord.gg/cryptosentinel',
    icon: DiscordIcon,
  },
  {
    name: 'Telegram',
    href: 'https://t.me/cryptosentinel',
    icon: TelegramIcon,
  },
];

export function Footer() {
  return (
    <footer className="bg-background border-t border-border">
      <div className="mx-auto max-w-7xl px-6 py-16 sm:py-20 lg:px-8">
        <div className="xl:grid xl:grid-cols-3 xl:gap-8">
          {/* Brand section */}
          <div className="space-y-8">
            <Link href="/" className="flex items-center space-x-2">
              <div className="relative">
                <ShieldCheckIcon className="h-8 w-8 text-primary" />
                <div className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-green-500 animate-pulse" />
              </div>
              <span className="text-xl font-bold text-gradient">CryptoSentinel</span>
            </Link>
            <p className="text-sm leading-6 text-muted-foreground max-w-md">
              AI-powered cryptocurrency token risk assessment platform. Protect your investments 
              with advanced security analysis, real-time scam detection, and comprehensive risk scoring.
            </p>
            <div className="flex space-x-6">
              {socialLinks.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  <span className="sr-only">{item.name}</span>
                  <item.icon className="h-6 w-6" />
                </a>
              ))}
            </div>
          </div>

          {/* Navigation links */}
          <div className="mt-16 grid grid-cols-2 gap-8 xl:col-span-2 xl:mt-0">
            <div className="md:grid md:grid-cols-2 md:gap-8">
              <div>
                <h3 className="text-sm font-semibold leading-6 text-foreground">Product</h3>
                <ul role="list" className="mt-6 space-y-4">
                  {navigation.product.map((item) => (
                    <li key={item.name}>
                      <Link
                        href={item.href}
                        className="text-sm leading-6 text-muted-foreground hover:text-primary transition-colors"
                      >
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
              <div className="mt-10 md:mt-0">
                <h3 className="text-sm font-semibold leading-6 text-foreground">Company</h3>
                <ul role="list" className="mt-6 space-y-4">
                  {navigation.company.map((item) => (
                    <li key={item.name}>
                      <Link
                        href={item.href}
                        className="text-sm leading-6 text-muted-foreground hover:text-primary transition-colors"
                      >
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
            <div className="md:grid md:grid-cols-2 md:gap-8">
              <div>
                <h3 className="text-sm font-semibold leading-6 text-foreground">Resources</h3>
                <ul role="list" className="mt-6 space-y-4">
                  {navigation.resources.map((item) => (
                    <li key={item.name}>
                      <Link
                        href={item.href}
                        className="text-sm leading-6 text-muted-foreground hover:text-primary transition-colors"
                      >
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
              <div className="mt-10 md:mt-0">
                <h3 className="text-sm font-semibold leading-6 text-foreground">Legal</h3>
                <ul role="list" className="mt-6 space-y-4">
                  {navigation.legal.map((item) => (
                    <li key={item.name}>
                      <Link
                        href={item.href}
                        className="text-sm leading-6 text-muted-foreground hover:text-primary transition-colors"
                      >
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Newsletter signup */}
        <div className="mt-16 border-t border-border pt-8 sm:mt-20 lg:mt-24">
          <div className="xl:grid xl:grid-cols-3 xl:gap-8">
            <div>
              <h3 className="text-sm font-semibold leading-6 text-foreground">
                Stay Updated
              </h3>
              <p className="mt-2 text-sm leading-6 text-muted-foreground">
                Get the latest security alerts and platform updates.
              </p>
            </div>
            <form className="mt-6 sm:flex sm:max-w-md xl:col-span-2 xl:mt-0">
              <label htmlFor="email-address" className="sr-only">
                Email address
              </label>
              <input
                type="email"
                name="email-address"
                id="email-address"
                autoComplete="email"
                required
                className="input w-full min-w-0 flex-auto rounded-md border-0 bg-background px-3.5 py-2 shadow-sm ring-1 ring-inset ring-border placeholder:text-muted-foreground focus:ring-2 focus:ring-inset focus:ring-primary sm:text-sm sm:leading-6"
                placeholder="Enter your email"
              />
              <div className="mt-4 sm:ml-4 sm:mt-0 sm:flex-shrink-0">
                <button
                  type="submit"
                  className="btn btn-primary w-full sm:w-auto"
                >
                  Subscribe
                </button>
              </div>
            </form>
          </div>
        </div>

        {/* Contact info */}
        <div className="mt-8 border-t border-border pt-8">
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-3">
            <div className="flex items-center space-x-3">
              <EnvelopeIcon className="h-5 w-5 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">
                <EMAIL>
              </span>
            </div>
            <div className="flex items-center space-x-3">
              <MapPinIcon className="h-5 w-5 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">
                Global, Decentralized
              </span>
            </div>
            <div className="flex items-center space-x-3">
              <PhoneIcon className="h-5 w-5 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">
                24/7 Community Support
              </span>
            </div>
          </div>
        </div>

        {/* Bottom section */}
        <div className="mt-8 border-t border-border pt-8 md:flex md:items-center md:justify-between">
          <div className="flex space-x-6 md:order-2">
            <div className="flex items-center space-x-2">
              <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-xs text-muted-foreground">All systems operational</span>
            </div>
          </div>
          <p className="mt-8 text-xs leading-5 text-muted-foreground md:order-1 md:mt-0">
            &copy; {new Date().getFullYear()} CryptoSentinel. All rights reserved. Built with ❤️ for the crypto community.
          </p>
        </div>

        {/* Security notice */}
        <div className="mt-8 rounded-lg bg-muted/50 p-4">
          <div className="flex items-start space-x-3">
            <ShieldCheckIcon className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="text-sm font-medium text-foreground">Security Notice</h4>
              <p className="mt-1 text-xs text-muted-foreground">
                CryptoSentinel provides risk assessment tools for educational purposes. Always conduct your own research 
                and never invest more than you can afford to lose. Cryptocurrency investments carry inherent risks.
              </p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
