'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';
import {
  ArrowRightIcon,
  ShieldCheckIcon,
  BoltIcon,
  GlobeAltIcon,
  CheckIcon,
} from '@heroicons/react/24/outline';

const features = [
  'AI-powered risk assessment',
  'Real-time scam detection',
  'Multi-chain support',
  'Portfolio monitoring',
  'Custom alerts',
  'Community insights',
];

const plans = [
  {
    name: 'Free',
    price: '$0',
    description: 'Perfect for getting started with token analysis',
    features: [
      '10 token analyses per month',
      'Basic risk scoring',
      'Community access',
      'Email support',
    ],
    cta: 'Get Started Free',
    href: '/signup',
    popular: false,
  },
  {
    name: 'Pro',
    price: '$29',
    description: 'Advanced features for serious traders and investors',
    features: [
      'Unlimited token analyses',
      'Advanced AI insights',
      'Real-time alerts',
      'Portfolio tracking',
      'Priority support',
      'API access',
    ],
    cta: 'Start Pro Trial',
    href: '/signup?plan=pro',
    popular: true,
  },
  {
    name: 'Enterprise',
    price: 'Custom',
    description: 'Tailored solutions for institutions and large teams',
    features: [
      'Everything in Pro',
      'Custom integrations',
      'Dedicated support',
      'SLA guarantees',
      'White-label options',
      'Advanced analytics',
    ],
    cta: 'Contact Sales',
    href: '/contact',
    popular: false,
  },
];

export function CTASection() {
  return (
    <section className="py-24 sm:py-32 bg-background">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        {/* Main CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="relative isolate overflow-hidden rounded-3xl bg-gradient-to-r from-primary via-purple-600 to-primary px-6 py-20 sm:px-16 text-center"
        >
          <div className="mx-auto max-w-2xl">
            <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
              Protect Your Crypto Investments Today
            </h2>
            <p className="mt-6 text-lg leading-8 text-white/90">
              Join thousands of smart investors who use CryptoSentinel to avoid scams, 
              identify opportunities, and make informed decisions in the crypto market.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Link
                href="/analyze"
                className="rounded-md bg-white px-6 py-3 text-base font-semibold text-primary shadow-sm hover:bg-gray-50 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white transition-colors group"
              >
                Start Free Analysis
                <ArrowRightIcon className="ml-2 h-5 w-5 inline transition-transform group-hover:translate-x-1" />
              </Link>
              <Link
                href="/demo"
                className="text-base font-semibold leading-6 text-white hover:text-white/80 transition-colors"
              >
                Watch Demo <span aria-hidden="true">→</span>
              </Link>
            </div>
          </div>

          {/* Background decoration */}
          <div className="absolute left-1/2 top-0 -z-10 -translate-x-1/2 blur-3xl xl:-top-6" aria-hidden="true">
            <div
              className="aspect-[1155/678] w-[72.1875rem] bg-gradient-to-tr from-white/20 to-white/10 opacity-30"
              style={{
                clipPath:
                  'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',
              }}
            />
          </div>
        </motion.div>

        {/* Features Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="mt-24 sm:mt-32"
        >
          <div className="mx-auto max-w-2xl text-center">
            <h3 className="text-2xl font-bold tracking-tight text-foreground sm:text-3xl">
              Everything You Need for Crypto Security
            </h3>
            <p className="mt-4 text-lg leading-8 text-muted-foreground">
              Comprehensive tools and insights to navigate the crypto market safely.
            </p>
          </div>

          <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-3">
            <div className="flex flex-col items-center text-center">
              <div className="flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
                <ShieldCheckIcon className="h-8 w-8 text-primary" />
              </div>
              <h4 className="mt-6 text-lg font-semibold text-foreground">Advanced Security</h4>
              <p className="mt-2 text-sm text-muted-foreground">
                AI-powered analysis detects scams, honeypots, and rug pulls before you invest.
              </p>
            </div>
            <div className="flex flex-col items-center text-center">
              <div className="flex h-16 w-16 items-center justify-center rounded-full bg-purple-500/10">
                <BoltIcon className="h-8 w-8 text-purple-500" />
              </div>
              <h4 className="mt-6 text-lg font-semibold text-foreground">Lightning Fast</h4>
              <p className="mt-2 text-sm text-muted-foreground">
                Get comprehensive risk assessments in seconds, not hours.
              </p>
            </div>
            <div className="flex flex-col items-center text-center">
              <div className="flex h-16 w-16 items-center justify-center rounded-full bg-green-500/10">
                <GlobeAltIcon className="h-8 w-8 text-green-500" />
              </div>
              <h4 className="mt-6 text-lg font-semibold text-foreground">Multi-Chain</h4>
              <p className="mt-2 text-sm text-muted-foreground">
                Support for Ethereum, BSC, Polygon, Arbitrum, and more blockchains.
              </p>
            </div>
          </div>
        </motion.div>

        {/* Pricing Plans */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-24 sm:mt-32"
        >
          <div className="mx-auto max-w-2xl text-center">
            <h3 className="text-2xl font-bold tracking-tight text-foreground sm:text-3xl">
              Choose Your Plan
            </h3>
            <p className="mt-4 text-lg leading-8 text-muted-foreground">
              Start free and upgrade as your needs grow. All plans include our core security features.
            </p>
          </div>

          <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-3">
            {plans.map((plan) => (
              <div
                key={plan.name}
                className={`relative rounded-2xl p-8 shadow-sm border ${
                  plan.popular
                    ? 'border-primary bg-primary/5 ring-1 ring-primary'
                    : 'border-border bg-card'
                }`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 -translate-x-1/2">
                    <span className="inline-flex items-center rounded-full bg-primary px-4 py-1 text-xs font-medium text-white">
                      Most Popular
                    </span>
                  </div>
                )}

                <div className="text-center">
                  <h4 className="text-lg font-semibold text-foreground">{plan.name}</h4>
                  <div className="mt-4">
                    <span className="text-4xl font-bold text-foreground">{plan.price}</span>
                    {plan.price !== 'Custom' && (
                      <span className="text-sm text-muted-foreground">/month</span>
                    )}
                  </div>
                  <p className="mt-4 text-sm text-muted-foreground">{plan.description}</p>
                </div>

                <ul className="mt-8 space-y-3">
                  {plan.features.map((feature) => (
                    <li key={feature} className="flex items-center text-sm">
                      <CheckIcon className="h-4 w-4 text-green-500 mr-3 flex-shrink-0" />
                      <span className="text-muted-foreground">{feature}</span>
                    </li>
                  ))}
                </ul>

                <div className="mt-8">
                  <Link
                    href={plan.href}
                    className={`block w-full rounded-md px-4 py-2 text-center text-sm font-semibold transition-colors ${
                      plan.popular
                        ? 'bg-primary text-white hover:bg-primary/90'
                        : 'bg-muted text-foreground hover:bg-muted/80'
                    }`}
                  >
                    {plan.cta}
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Final CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="mt-24 sm:mt-32 text-center"
        >
          <div className="mx-auto max-w-2xl">
            <h3 className="text-2xl font-bold tracking-tight text-foreground sm:text-3xl">
              Ready to Secure Your Crypto?
            </h3>
            <p className="mt-4 text-lg leading-8 text-muted-foreground">
              Join the community of smart investors who never fall for crypto scams.
            </p>
            <div className="mt-8 flex flex-col sm:flex-row items-center justify-center gap-4">
              <Link
                href="/signup"
                className="btn btn-primary text-lg px-8 py-3 group"
              >
                Get Started Now
                <ArrowRightIcon className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Link>
              <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                <div className="flex items-center space-x-1">
                  <CheckIcon className="h-4 w-4 text-green-500" />
                  <span>No credit card required</span>
                </div>
                <div className="flex items-center space-x-1">
                  <CheckIcon className="h-4 w-4 text-green-500" />
                  <span>Free forever plan</span>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
