'use client';

import { motion } from 'framer-motion';
import {
  ShieldCheckIcon,
  ChartBarIcon,
  EyeIcon,
  BoltIcon,
  CpuChipIcon,
  GlobeAltIcon,
  ClockIcon,
  UserGroupIcon,
  ExclamationTriangleIcon,
  DocumentMagnifyingGlassIcon,
  ChatBubbleLeftRightIcon,
  CodeBracketIcon,
} from '@heroicons/react/24/outline';

const features = [
  {
    name: 'AI-Powered Risk Assessment',
    description: 'Advanced machine learning algorithms analyze multiple risk factors to provide comprehensive security scores for any token.',
    icon: CpuChipIcon,
    color: 'text-blue-500',
    bgColor: 'bg-blue-500/10',
  },
  {
    name: 'Smart Contract Analysis',
    description: 'Deep analysis of smart contract code to detect honeypots, rug pulls, and other malicious patterns.',
    icon: CodeBracketIcon,
    color: 'text-purple-500',
    bgColor: 'bg-purple-500/10',
  },
  {
    name: 'Real-time Scam Detection',
    description: 'Instant alerts for newly discovered scams and suspicious token activities across all major blockchains.',
    icon: ExclamationTriangleIcon,
    color: 'text-red-500',
    bgColor: 'bg-red-500/10',
  },
  {
    name: 'Social Sentiment Analysis',
    description: 'Monitor social media platforms to gauge community sentiment and detect coordinated manipulation attempts.',
    icon: ChatBubbleLeftRightIcon,
    color: 'text-green-500',
    bgColor: 'bg-green-500/10',
  },
  {
    name: 'Developer Reputation Tracking',
    description: 'Analyze development team backgrounds, GitHub activity, and project history to assess credibility.',
    icon: UserGroupIcon,
    color: 'text-orange-500',
    bgColor: 'bg-orange-500/10',
  },
  {
    name: 'Multi-Chain Support',
    description: 'Comprehensive analysis across Ethereum, BSC, Polygon, Arbitrum, Optimism, and other major networks.',
    icon: GlobeAltIcon,
    color: 'text-indigo-500',
    bgColor: 'bg-indigo-500/10',
  },
  {
    name: 'Portfolio Risk Monitoring',
    description: 'Track your entire portfolio with continuous risk assessment and personalized alerts for your holdings.',
    icon: ChartBarIcon,
    color: 'text-cyan-500',
    bgColor: 'bg-cyan-500/10',
  },
  {
    name: 'Lightning Fast Analysis',
    description: 'Get comprehensive risk reports in seconds with our optimized analysis pipeline and caching system.',
    icon: BoltIcon,
    color: 'text-yellow-500',
    bgColor: 'bg-yellow-500/10',
  },
  {
    name: 'Historical Risk Tracking',
    description: 'View risk score changes over time and understand how token security profiles evolve.',
    icon: ClockIcon,
    color: 'text-pink-500',
    bgColor: 'bg-pink-500/10',
  },
  {
    name: 'Detailed Security Reports',
    description: 'In-depth analysis reports with actionable insights and recommendations for each token.',
    icon: DocumentMagnifyingGlassIcon,
    color: 'text-teal-500',
    bgColor: 'bg-teal-500/10',
  },
  {
    name: 'Community Verification',
    description: 'Crowdsourced verification system where community members can report and validate token information.',
    icon: ShieldCheckIcon,
    color: 'text-emerald-500',
    bgColor: 'bg-emerald-500/10',
  },
  {
    name: 'Advanced Monitoring',
    description: 'Set up custom alerts for price movements, risk changes, and suspicious activities.',
    icon: EyeIcon,
    color: 'text-violet-500',
    bgColor: 'bg-violet-500/10',
  },
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: [0.25, 0.25, 0, 1],
    },
  },
};

export function FeaturesSection() {
  return (
    <section className="py-24 sm:py-32 bg-background">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mx-auto max-w-2xl text-center"
        >
          <h2 className="text-base font-semibold leading-7 text-primary">
            Comprehensive Protection
          </h2>
          <p className="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
            Advanced AI-Powered Token Analysis
          </p>
          <p className="mt-6 text-lg leading-8 text-muted-foreground">
            Our cutting-edge platform combines artificial intelligence, blockchain analysis, 
            and community intelligence to provide the most comprehensive token risk assessment available.
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none"
        >
          <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
            {features.map((feature, index) => (
              <motion.div
                key={feature.name}
                variants={itemVariants}
                className="flex flex-col group"
              >
                <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <div className={`flex h-10 w-10 items-center justify-center rounded-lg ${feature.bgColor} group-hover:scale-110 transition-transform duration-200`}>
                    <feature.icon className={`h-6 w-6 ${feature.color}`} aria-hidden="true" />
                  </div>
                  {feature.name}
                </dt>
                <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p className="flex-auto">{feature.description}</p>
                </dd>
              </motion.div>
            ))}
          </dl>
        </motion.div>

        {/* Feature highlight */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-24 sm:mt-32"
        >
          <div className="relative isolate overflow-hidden bg-gradient-to-r from-primary/10 via-purple-500/10 to-primary/10 px-6 py-20 sm:px-10 sm:py-24 lg:px-20 rounded-3xl">
            <div className="mx-auto max-w-2xl text-center">
              <h3 className="text-2xl font-bold tracking-tight text-foreground sm:text-3xl">
                Powered by Advanced AI
              </h3>
              <p className="mt-6 text-lg leading-8 text-muted-foreground">
                Our proprietary machine learning models are trained on millions of token transactions, 
                smart contract patterns, and market behaviors to provide unparalleled accuracy in risk detection.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <div className="flex items-center gap-x-2">
                  <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium text-foreground">99.7% Accuracy</span>
                </div>
                <div className="flex items-center gap-x-2">
                  <div className="h-2 w-2 bg-blue-500 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium text-foreground">Real-time Analysis</span>
                </div>
                <div className="flex items-center gap-x-2">
                  <div className="h-2 w-2 bg-purple-500 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium text-foreground">Multi-chain Support</span>
                </div>
              </div>
            </div>
            
            {/* Background decoration */}
            <div className="absolute left-1/2 top-0 -z-10 -translate-x-1/2 blur-3xl xl:-top-6" aria-hidden="true">
              <div
                className="aspect-[1155/678] w-[72.1875rem] bg-gradient-to-tr from-primary to-purple-500 opacity-20"
                style={{
                  clipPath:
                    'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',
                }}
              />
            </div>
          </div>
        </motion.div>

        {/* Integration showcase */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="mt-24 sm:mt-32"
        >
          <div className="mx-auto max-w-2xl text-center">
            <h3 className="text-2xl font-bold tracking-tight text-foreground sm:text-3xl">
              Seamless Integration
            </h3>
            <p className="mt-6 text-lg leading-8 text-muted-foreground">
              Connect your favorite wallets and platforms for a unified security experience.
            </p>
          </div>
          
          <div className="mt-16 grid grid-cols-2 gap-8 sm:grid-cols-4 lg:grid-cols-6">
            {[
              'MetaMask', 'WalletConnect', 'Coinbase', 'Trust Wallet', 'Phantom', 'Rainbow'
            ].map((wallet, index) => (
              <motion.div
                key={wallet}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                className="flex items-center justify-center p-4 bg-muted/50 rounded-lg hover:bg-muted transition-colors"
              >
                <span className="text-sm font-medium text-muted-foreground">{wallet}</span>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}
