'use client';

import { useState } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  ShieldCheckIcon, 
  ChartBarIcon, 
  EyeIcon,
  ArrowRightIcon,
  PlayIcon
} from '@heroicons/react/24/outline';
import { TokenSearchBar } from '@/components/ui/TokenSearchBar';

const stats = [
  { name: 'Tokens Analyzed', value: '50K+', icon: ChartBarIcon },
  { name: 'Scams Detected', value: '2.3K+', icon: ShieldCheckIcon },
  { name: 'Users Protected', value: '15K+', icon: EyeIcon },
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: [0.25, 0.25, 0, 1],
    },
  },
};

export function HeroSection() {
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);

  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Background decoration */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-primary/20 rounded-full blur-3xl" />
        <div className="absolute bottom-0 right-0 translate-x-1/2 translate-y-1/2 w-96 h-96 bg-purple-500/20 rounded-full blur-3xl" />
      </div>

      <div className="relative mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="mx-auto max-w-4xl text-center"
        >
          {/* Badge */}
          <motion.div variants={itemVariants} className="mb-8">
            <div className="inline-flex items-center rounded-full bg-primary/10 px-4 py-2 text-sm font-medium text-primary ring-1 ring-inset ring-primary/20">
              <ShieldCheckIcon className="mr-2 h-4 w-4" />
              AI-Powered Risk Assessment
            </div>
          </motion.div>

          {/* Main heading */}
          <motion.h1
            variants={itemVariants}
            className="text-4xl font-bold tracking-tight text-foreground sm:text-6xl lg:text-7xl"
          >
            Protect Your{' '}
            <span className="text-gradient">Crypto Investments</span>{' '}
            with AI
          </motion.h1>

          {/* Subheading */}
          <motion.p
            variants={itemVariants}
            className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl"
          >
            Advanced AI-powered token analysis that detects scams, analyzes risks, and provides 
            real-time insights to keep your investments safe. Make informed decisions with 
            comprehensive security scoring.
          </motion.p>

          {/* Token search bar */}
          <motion.div variants={itemVariants} className="mt-10">
            <TokenSearchBar 
              placeholder="Enter token address or symbol to analyze..."
              className="max-w-2xl mx-auto"
            />
            <p className="mt-3 text-sm text-muted-foreground">
              Try: ****************************************** (UNI) or search by symbol
            </p>
          </motion.div>

          {/* CTA buttons */}
          <motion.div
            variants={itemVariants}
            className="mt-10 flex flex-col sm:flex-row items-center justify-center gap-4"
          >
            <Link
              href="/analyze"
              className="btn btn-primary text-lg px-8 py-3 group"
            >
              Start Analysis
              <ArrowRightIcon className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
            </Link>
            
            <button
              onClick={() => setIsVideoPlaying(true)}
              className="btn btn-outline text-lg px-8 py-3 group"
            >
              <PlayIcon className="mr-2 h-5 w-5" />
              Watch Demo
            </button>
          </motion.div>

          {/* Trust indicators */}
          <motion.div variants={itemVariants} className="mt-16">
            <p className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
              Trusted by crypto investors worldwide
            </p>
            <div className="mt-8 grid grid-cols-1 gap-8 sm:grid-cols-3">
              {stats.map((stat, index) => (
                <motion.div
                  key={stat.name}
                  variants={itemVariants}
                  transition={{ delay: index * 0.1 }}
                  className="flex flex-col items-center"
                >
                  <div className="flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 mb-4">
                    <stat.icon className="h-6 w-6 text-primary" />
                  </div>
                  <div className="text-3xl font-bold text-foreground">{stat.value}</div>
                  <div className="text-sm text-muted-foreground">{stat.name}</div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </motion.div>

        {/* Feature preview */}
        <motion.div
          variants={itemVariants}
          initial="hidden"
          animate="visible"
          transition={{ delay: 0.8 }}
          className="mt-20 mx-auto max-w-5xl"
        >
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-purple-500/20 rounded-2xl blur-2xl" />
            <div className="relative bg-card/80 backdrop-blur-sm border border-border rounded-2xl p-8 shadow-2xl">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-foreground mb-4">
                    Real-time Risk Assessment
                  </h3>
                  <p className="text-muted-foreground mb-6">
                    Our AI analyzes smart contracts, social sentiment, developer reputation, 
                    and market metrics to provide comprehensive risk scores in seconds.
                  </p>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full" />
                      <span className="text-sm text-muted-foreground">Contract Security Analysis</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full" />
                      <span className="text-sm text-muted-foreground">Social Sentiment Tracking</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full" />
                      <span className="text-sm text-muted-foreground">Developer Reputation</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-purple-500 rounded-full" />
                      <span className="text-sm text-muted-foreground">Liquidity Analysis</span>
                    </div>
                  </div>
                </div>
                <div className="relative">
                  <div className="bg-background rounded-lg border border-border p-6 shadow-lg">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-2">
                        <div className="w-8 h-8 bg-primary rounded-full" />
                        <div>
                          <div className="font-semibold">Sample Token</div>
                          <div className="text-sm text-muted-foreground">SAMPLE</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-green-500">72</div>
                        <div className="text-sm text-muted-foreground">Risk Score</div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Contract Security</span>
                        <span className="text-green-500">✓ Safe</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Liquidity</span>
                        <span className="text-yellow-500">⚠ Medium</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Social Sentiment</span>
                        <span className="text-green-500">✓ Positive</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Developer Activity</span>
                        <span className="text-green-500">✓ Active</span>
                      </div>
                    </div>
                    <div className="mt-4 pt-4 border-t border-border">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Overall Risk</span>
                        <span className="badge badge-risk-medium">Medium Risk</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Video modal */}
      {isVideoPlaying && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm">
          <div className="relative w-full max-w-4xl mx-4">
            <button
              onClick={() => setIsVideoPlaying(false)}
              className="absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors"
            >
              <span className="sr-only">Close video</span>
              <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
            <div className="aspect-video bg-black rounded-lg overflow-hidden">
              <iframe
                src="https://www.youtube.com/embed/dQw4w9WgXcQ"
                title="CryptoSentinel Demo"
                className="w-full h-full"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
              />
            </div>
          </div>
        </div>
      )}
    </section>
  );
}
