'use client';

import { motion } from 'framer-motion';
import {
  MagnifyingGlassIcon,
  CpuChipIcon,
  ShieldCheckIcon,
  ChartBarIcon,
  ArrowRightIcon,
} from '@heroicons/react/24/outline';

const steps = [
  {
    id: 1,
    name: 'Input Token Address',
    description: 'Simply paste any token contract address or search by symbol. Our system supports all major blockchains including Ethereum, BSC, Polygon, and more.',
    icon: MagnifyingGlassIcon,
    color: 'text-blue-500',
    bgColor: 'bg-blue-500/10',
    borderColor: 'border-blue-500/20',
  },
  {
    id: 2,
    name: 'AI Analysis Engine',
    description: 'Our advanced AI algorithms analyze the smart contract code, transaction patterns, social sentiment, and developer reputation in real-time.',
    icon: CpuChipIcon,
    color: 'text-purple-500',
    bgColor: 'bg-purple-500/10',
    borderColor: 'border-purple-500/20',
  },
  {
    id: 3,
    name: 'Risk Assessment',
    description: 'Get a comprehensive risk score with detailed breakdown of security factors, potential vulnerabilities, and red flags.',
    icon: ShieldCheckIcon,
    color: 'text-green-500',
    bgColor: 'bg-green-500/10',
    borderColor: 'border-green-500/20',
  },
  {
    id: 4,
    name: 'Actionable Insights',
    description: 'Receive clear recommendations, risk warnings, and detailed reports to make informed investment decisions.',
    icon: ChartBarIcon,
    color: 'text-orange-500',
    bgColor: 'bg-orange-500/10',
    borderColor: 'border-orange-500/20',
  },
];

const analysisFeatures = [
  {
    category: 'Smart Contract Analysis',
    items: [
      'Honeypot Detection',
      'Rug Pull Patterns',
      'Mint/Burn Functions',
      'Ownership Analysis',
      'Proxy Contracts',
    ],
  },
  {
    category: 'Social Intelligence',
    items: [
      'Sentiment Analysis',
      'Community Activity',
      'Influencer Mentions',
      'Manipulation Detection',
      'Trend Analysis',
    ],
  },
  {
    category: 'Developer Reputation',
    items: [
      'GitHub Activity',
      'Project History',
      'Team Verification',
      'Code Quality',
      'Update Frequency',
    ],
  },
  {
    category: 'Market Metrics',
    items: [
      'Liquidity Analysis',
      'Holder Distribution',
      'Trading Patterns',
      'Volume Analysis',
      'Price Stability',
    ],
  },
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: [0.25, 0.25, 0, 1],
    },
  },
};

export function HowItWorksSection() {
  return (
    <section className="py-24 sm:py-32 bg-background">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mx-auto max-w-2xl text-center"
        >
          <h2 className="text-base font-semibold leading-7 text-primary">
            How It Works
          </h2>
          <p className="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
            Token Analysis in 4 Simple Steps
          </p>
          <p className="mt-6 text-lg leading-8 text-muted-foreground">
            Our AI-powered platform makes token security analysis accessible to everyone. 
            Get comprehensive risk assessments in seconds, not hours.
          </p>
        </motion.div>

        {/* Process Steps */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none"
        >
          <div className="grid grid-cols-1 gap-8 lg:grid-cols-4">
            {steps.map((step, stepIdx) => (
              <motion.div
                key={step.id}
                variants={itemVariants}
                className="relative group"
              >
                <div className={`flex h-16 w-16 items-center justify-center rounded-xl ${step.bgColor} border ${step.borderColor} group-hover:scale-110 transition-transform duration-200`}>
                  <step.icon className={`h-8 w-8 ${step.color}`} />
                </div>
                
                <div className="mt-6">
                  <h3 className="text-lg font-semibold leading-8 text-foreground">
                    <span className="text-sm font-medium text-primary">Step {step.id}</span>
                    <br />
                    {step.name}
                  </h3>
                  <p className="mt-2 text-base leading-7 text-muted-foreground">
                    {step.description}
                  </p>
                </div>

                {/* Arrow connector */}
                {stepIdx < steps.length - 1 && (
                  <div className="hidden lg:block absolute top-8 left-full w-8 h-8 -ml-4">
                    <ArrowRightIcon className="h-6 w-6 text-muted-foreground" />
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Analysis Features Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-24 sm:mt-32"
        >
          <div className="mx-auto max-w-2xl text-center">
            <h3 className="text-2xl font-bold tracking-tight text-foreground sm:text-3xl">
              Comprehensive Analysis Framework
            </h3>
            <p className="mt-4 text-lg leading-8 text-muted-foreground">
              Our AI examines multiple dimensions of token security to provide the most accurate risk assessment.
            </p>
          </div>

          <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-2 xl:grid-cols-4">
            {analysisFeatures.map((feature, index) => (
              <motion.div
                key={feature.category}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="rounded-2xl bg-card p-6 shadow-sm border border-border hover:shadow-lg transition-shadow duration-300"
              >
                <h4 className="text-lg font-semibold text-foreground mb-4">
                  {feature.category}
                </h4>
                <ul className="space-y-2">
                  {feature.items.map((item) => (
                    <li key={item} className="flex items-center text-sm text-muted-foreground">
                      <div className="h-1.5 w-1.5 bg-primary rounded-full mr-3 flex-shrink-0" />
                      {item}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Demo Preview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="mt-24 sm:mt-32"
        >
          <div className="relative isolate overflow-hidden rounded-3xl bg-gradient-to-r from-primary/10 via-purple-500/10 to-primary/10 px-6 py-20 sm:px-16">
            <div className="mx-auto max-w-2xl text-center">
              <h3 className="text-2xl font-bold tracking-tight text-foreground sm:text-3xl">
                See It In Action
              </h3>
              <p className="mt-6 text-lg leading-8 text-muted-foreground">
                Watch how our AI analyzes a token in real-time and provides actionable security insights.
              </p>
              <div className="mt-10">
                <button className="btn btn-primary text-lg px-8 py-3 group">
                  Try Live Demo
                  <ArrowRightIcon className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </button>
              </div>
            </div>

            {/* Sample Analysis Preview */}
            <div className="mx-auto mt-16 max-w-4xl">
              <div className="rounded-2xl bg-background/80 backdrop-blur-sm border border-border p-6 shadow-xl">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-sm">T</span>
                    </div>
                    <div>
                      <div className="font-semibold text-foreground">Sample Token (SAMPLE)</div>
                      <div className="text-sm text-muted-foreground">0x1234...5678</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-orange-500">68</div>
                    <div className="text-sm text-muted-foreground">Risk Score</div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Contract Security</span>
                      <span className="text-yellow-500">⚠ Medium</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Social Sentiment</span>
                      <span className="text-green-500">✓ Positive</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Developer Activity</span>
                      <span className="text-green-500">✓ Active</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Liquidity</span>
                      <span className="text-red-500">✗ Low</span>
                    </div>
                  </div>
                </div>

                <div className="pt-4 border-t border-border">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-foreground">Overall Assessment</span>
                    <span className="badge badge-risk-medium">Medium Risk</span>
                  </div>
                  <p className="text-xs text-muted-foreground mt-2">
                    Proceed with caution. Low liquidity detected.
                  </p>
                </div>
              </div>
            </div>

            {/* Background decoration */}
            <div className="absolute left-1/2 top-0 -z-10 -translate-x-1/2 blur-3xl" aria-hidden="true">
              <div
                className="aspect-[1155/678] w-[72.1875rem] bg-gradient-to-tr from-primary/20 to-purple-500/20 opacity-30"
                style={{
                  clipPath:
                    'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',
                }}
              />
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
