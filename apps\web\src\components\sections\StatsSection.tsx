'use client';

import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import {
  ShieldCheckIcon,
  ChartBarIcon,
  EyeIcon,
  ExclamationTriangleIcon,
  GlobeAltIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';

interface Stat {
  id: string;
  name: string;
  value: string;
  change: string;
  changeType: 'increase' | 'decrease';
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  color: string;
  bgColor: string;
}

const stats: Stat[] = [
  {
    id: 'tokens-analyzed',
    name: 'Tokens Analyzed',
    value: '127,543',
    change: '+12%',
    changeType: 'increase',
    icon: ChartBarIcon,
    description: 'Total tokens analyzed across all chains',
    color: 'text-blue-500',
    bgColor: 'bg-blue-500/10',
  },
  {
    id: 'scams-detected',
    name: 'Scams Detected',
    value: '8,247',
    change: '+23%',
    changeType: 'increase',
    icon: ExclamationTriangleIcon,
    description: 'Malicious tokens identified and flagged',
    color: 'text-red-500',
    bgColor: 'bg-red-500/10',
  },
  {
    id: 'users-protected',
    name: 'Users Protected',
    value: '45,892',
    change: '+18%',
    changeType: 'increase',
    icon: ShieldCheckIcon,
    description: 'Active users safeguarded from scams',
    color: 'text-green-500',
    bgColor: 'bg-green-500/10',
  },
  {
    id: 'chains-supported',
    name: 'Chains Supported',
    value: '12',
    change: '+2',
    changeType: 'increase',
    icon: GlobeAltIcon,
    description: 'Blockchain networks monitored',
    color: 'text-purple-500',
    bgColor: 'bg-purple-500/10',
  },
  {
    id: 'alerts-sent',
    name: 'Alerts Sent',
    value: '234,567',
    change: '+31%',
    changeType: 'increase',
    icon: EyeIcon,
    description: 'Real-time security alerts delivered',
    color: 'text-orange-500',
    bgColor: 'bg-orange-500/10',
  },
  {
    id: 'avg-response-time',
    name: 'Avg Response Time',
    value: '1.2s',
    change: '-15%',
    changeType: 'decrease',
    icon: ClockIcon,
    description: 'Average analysis completion time',
    color: 'text-cyan-500',
    bgColor: 'bg-cyan-500/10',
  },
];

function AnimatedNumber({ value, duration = 2000 }: { value: string; duration?: number }) {
  const [displayValue, setDisplayValue] = useState('0');
  
  useEffect(() => {
    // Extract numeric value and suffix
    const numericValue = parseFloat(value.replace(/[^\d.]/g, ''));
    const suffix = value.replace(/[\d.,]/g, '');
    
    if (isNaN(numericValue)) {
      setDisplayValue(value);
      return;
    }
    
    let startTime: number;
    let animationFrame: number;
    
    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime;
      const progress = Math.min((currentTime - startTime) / duration, 1);
      
      // Easing function for smooth animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const currentValue = numericValue * easeOutQuart;
      
      // Format the number based on its size
      let formattedValue: string;
      if (numericValue >= 1000000) {
        formattedValue = (currentValue / 1000000).toFixed(1) + 'M';
      } else if (numericValue >= 1000) {
        formattedValue = (currentValue / 1000).toFixed(1) + 'K';
      } else if (numericValue < 10 && numericValue % 1 !== 0) {
        formattedValue = currentValue.toFixed(1);
      } else {
        formattedValue = Math.floor(currentValue).toLocaleString();
      }
      
      setDisplayValue(formattedValue + suffix.replace(/[KM]/g, ''));
      
      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };
    
    animationFrame = requestAnimationFrame(animate);
    
    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [value, duration]);
  
  return <span>{displayValue}</span>;
}

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: [0.25, 0.25, 0, 1],
    },
  },
};

export function StatsSection() {
  return (
    <section className="py-24 sm:py-32 bg-muted/30">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mx-auto max-w-2xl text-center"
        >
          <h2 className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
            Trusted by the Crypto Community
          </h2>
          <p className="mt-4 text-lg leading-8 text-muted-foreground">
            Our platform has become the go-to solution for crypto security, protecting millions in assets 
            and helping users make informed investment decisions.
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 sm:mt-20 sm:grid-cols-2 lg:mx-0 lg:max-w-none lg:grid-cols-3"
        >
          {stats.map((stat) => (
            <motion.div
              key={stat.id}
              variants={itemVariants}
              className="group relative overflow-hidden rounded-2xl bg-card p-8 shadow-sm border border-border hover:shadow-lg transition-all duration-300"
            >
              {/* Background gradient */}
              <div className={`absolute inset-0 ${stat.bgColor} opacity-0 group-hover:opacity-100 transition-opacity duration-300`} />
              
              <div className="relative">
                <div className="flex items-center justify-between">
                  <div className={`flex h-12 w-12 items-center justify-center rounded-lg ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                  <div className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                    stat.changeType === 'increase' 
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' 
                      : 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
                  }`}>
                    {stat.changeType === 'increase' ? '↗' : '↘'} {stat.change}
                  </div>
                </div>
                
                <div className="mt-6">
                  <div className="text-3xl font-bold text-foreground">
                    <AnimatedNumber value={stat.value} />
                  </div>
                  <div className="mt-1 text-sm font-medium text-foreground">
                    {stat.name}
                  </div>
                  <div className="mt-2 text-sm text-muted-foreground">
                    {stat.description}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Additional metrics */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-16 sm:mt-20"
        >
          <div className="relative isolate overflow-hidden rounded-2xl bg-gradient-to-r from-primary/5 via-purple-500/5 to-primary/5 px-6 py-16 sm:px-16">
            <div className="mx-auto max-w-2xl text-center">
              <h3 className="text-2xl font-bold tracking-tight text-foreground">
                Real-time Protection
              </h3>
              <p className="mt-4 text-lg leading-8 text-muted-foreground">
                Our AI systems work around the clock to monitor the crypto ecosystem and protect our users.
              </p>
            </div>
            
            <div className="mx-auto mt-12 grid max-w-lg grid-cols-2 gap-8 sm:max-w-xl sm:grid-cols-2 lg:max-w-4xl lg:grid-cols-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-foreground">
                  <AnimatedNumber value="99.7%" />
                </div>
                <div className="mt-1 text-sm text-muted-foreground">Accuracy Rate</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-foreground">
                  <AnimatedNumber value="24/7" />
                </div>
                <div className="mt-1 text-sm text-muted-foreground">Monitoring</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-foreground">
                  <AnimatedNumber value="<2s" />
                </div>
                <div className="mt-1 text-sm text-muted-foreground">Analysis Time</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-foreground">
                  <AnimatedNumber value="$2.3B" />
                </div>
                <div className="mt-1 text-sm text-muted-foreground">Assets Protected</div>
              </div>
            </div>
            
            {/* Background decoration */}
            <div className="absolute left-1/2 top-0 -z-10 -translate-x-1/2 blur-3xl" aria-hidden="true">
              <div
                className="aspect-[1155/678] w-[72.1875rem] bg-gradient-to-tr from-primary/20 to-purple-500/20 opacity-30"
                style={{
                  clipPath:
                    'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',
                }}
              />
            </div>
          </div>
        </motion.div>

        {/* Live activity indicator */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="mt-16 text-center"
        >
          <div className="inline-flex items-center space-x-2 rounded-full bg-green-100 px-4 py-2 text-sm font-medium text-green-800 dark:bg-green-900/20 dark:text-green-400">
            <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>Live: Analyzing tokens in real-time</span>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
