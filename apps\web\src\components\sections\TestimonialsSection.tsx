'use client';

import { motion } from 'framer-motion';
import { StarIcon } from '@heroicons/react/24/solid';
import { TwitterIcon, GitHubIcon } from '@/components/ui/SocialIcons';

const testimonials = [
  {
    id: 1,
    content: "CryptoSentinel saved me from investing in what turned out to be a major rug pull. The AI detected suspicious patterns that I completely missed. This platform is a game-changer for crypto security.",
    author: {
      name: "<PERSON>",
      role: "DeFi Trader",
      avatar: "AC",
      verified: true,
      platform: "twitter",
    },
    rating: 5,
    date: "2 days ago",
  },
  {
    id: 2,
    content: "As a developer, I appreciate the technical depth of CryptoSentinel's analysis. The smart contract security reports are incredibly detailed and have helped me identify vulnerabilities in my own projects.",
    author: {
      name: "<PERSON>",
      role: "Blockchain Developer",
      avatar: "SM",
      verified: true,
      platform: "github",
    },
    rating: 5,
    date: "1 week ago",
  },
  {
    id: 3,
    content: "The social sentiment analysis is spot on. I've been using it to gauge community sentiment before making investment decisions, and it's been incredibly accurate in predicting market movements.",
    author: {
      name: "<PERSON>",
      role: "Crypto Analyst",
      avatar: "M<PERSON>",
      verified: true,
      platform: "twitter",
    },
    rating: 5,
    date: "3 days ago",
  },
  {
    id: 4,
    content: "CryptoSentinel's real-time alerts have become an essential part of my trading strategy. The platform caught several honeypots that could have cost me thousands. Highly recommended!",
    author: {
      name: "Emma Rodriguez",
      role: "Portfolio Manager",
      avatar: "ER",
      verified: true,
      platform: "twitter",
    },
    rating: 5,
    date: "5 days ago",
  },
  {
    id: 5,
    content: "The user interface is intuitive and the analysis is comprehensive. Even as a beginner, I can understand the risk assessments and make informed decisions about my investments.",
    author: {
      name: "David Kim",
      role: "Crypto Enthusiast",
      avatar: "DK",
      verified: false,
      platform: "twitter",
    },
    rating: 5,
    date: "1 week ago",
  },
  {
    id: 6,
    content: "I've tried many token analysis tools, but CryptoSentinel's AI-powered approach is by far the most accurate. The developer reputation analysis alone has saved me from several bad investments.",
    author: {
      name: "Lisa Wang",
      role: "Investment Advisor",
      avatar: "LW",
      verified: true,
      platform: "twitter",
    },
    rating: 5,
    date: "4 days ago",
  },
];

const stats = [
  { label: "User Rating", value: "4.9/5" },
  { label: "Active Users", value: "45K+" },
  { label: "Tokens Analyzed", value: "127K+" },
  { label: "Scams Prevented", value: "8.2K+" },
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: [0.25, 0.25, 0, 1],
    },
  },
};

function StarRating({ rating }: { rating: number }) {
  return (
    <div className="flex items-center space-x-1">
      {[...Array(5)].map((_, i) => (
        <StarIcon
          key={i}
          className={`h-4 w-4 ${
            i < rating ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600'
          }`}
        />
      ))}
    </div>
  );
}

function PlatformIcon({ platform }: { platform: string }) {
  switch (platform) {
    case 'twitter':
      return <TwitterIcon className="h-4 w-4" />;
    case 'github':
      return <GitHubIcon className="h-4 w-4" />;
    default:
      return null;
  }
}

export function TestimonialsSection() {
  return (
    <section className="py-24 sm:py-32 bg-muted/30">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mx-auto max-w-2xl text-center"
        >
          <h2 className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
            Trusted by Crypto Professionals
          </h2>
          <p className="mt-4 text-lg leading-8 text-muted-foreground">
            Join thousands of traders, developers, and investors who rely on CryptoSentinel 
            to protect their crypto investments and make informed decisions.
          </p>
        </motion.div>

        {/* Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="mx-auto mt-16 grid max-w-2xl grid-cols-2 gap-8 sm:grid-cols-4 lg:mx-0 lg:max-w-none"
        >
          {stats.map((stat) => (
            <div key={stat.label} className="text-center">
              <div className="text-2xl font-bold text-foreground">{stat.value}</div>
              <div className="mt-1 text-sm text-muted-foreground">{stat.label}</div>
            </div>
          ))}
        </motion.div>

        {/* Testimonials Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-3"
        >
          {testimonials.map((testimonial) => (
            <motion.div
              key={testimonial.id}
              variants={itemVariants}
              className="group relative overflow-hidden rounded-2xl bg-card p-6 shadow-sm border border-border hover:shadow-lg transition-all duration-300"
            >
              {/* Quote mark */}
              <div className="absolute top-4 right-4 text-4xl text-primary/20 font-serif">
                "
              </div>

              <div className="relative">
                {/* Rating */}
                <div className="flex items-center justify-between mb-4">
                  <StarRating rating={testimonial.rating} />
                  <span className="text-xs text-muted-foreground">{testimonial.date}</span>
                </div>

                {/* Content */}
                <blockquote className="text-sm leading-6 text-muted-foreground mb-6">
                  {testimonial.content}
                </blockquote>

                {/* Author */}
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                      <span className="text-sm font-medium text-primary">
                        {testimonial.author.avatar}
                      </span>
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <p className="text-sm font-medium text-foreground truncate">
                        {testimonial.author.name}
                      </p>
                      {testimonial.author.verified && (
                        <div className="flex items-center space-x-1">
                          <div className="h-4 w-4 rounded-full bg-blue-500 flex items-center justify-center">
                            <svg className="h-2.5 w-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </div>
                          <PlatformIcon platform={testimonial.author.platform} />
                        </div>
                      )}
                    </div>
                    <p className="text-xs text-muted-foreground truncate">
                      {testimonial.author.role}
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Community Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-24 sm:mt-32"
        >
          <div className="relative isolate overflow-hidden rounded-3xl bg-gradient-to-r from-primary/10 via-purple-500/10 to-primary/10 px-6 py-20 sm:px-16">
            <div className="mx-auto max-w-2xl text-center">
              <h3 className="text-2xl font-bold tracking-tight text-foreground sm:text-3xl">
                Join Our Community
              </h3>
              <p className="mt-6 text-lg leading-8 text-muted-foreground">
                Connect with fellow crypto enthusiasts, share insights, and stay updated 
                on the latest security threats and market trends.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <a
                  href="https://discord.gg/cryptosentinel"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="btn btn-primary"
                >
                  Join Discord
                </a>
                <a
                  href="https://t.me/cryptosentinel"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="btn btn-outline"
                >
                  Telegram Group
                </a>
              </div>
            </div>

            {/* Community stats */}
            <div className="mx-auto mt-16 grid max-w-lg grid-cols-3 gap-8 sm:max-w-xl lg:max-w-4xl">
              <div className="text-center">
                <div className="text-2xl font-bold text-foreground">12K+</div>
                <div className="mt-1 text-sm text-muted-foreground">Discord Members</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-foreground">8.5K+</div>
                <div className="mt-1 text-sm text-muted-foreground">Telegram Users</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-foreground">24/7</div>
                <div className="mt-1 text-sm text-muted-foreground">Community Support</div>
              </div>
            </div>

            {/* Background decoration */}
            <div className="absolute left-1/2 top-0 -z-10 -translate-x-1/2 blur-3xl" aria-hidden="true">
              <div
                className="aspect-[1155/678] w-[72.1875rem] bg-gradient-to-tr from-primary/20 to-purple-500/20 opacity-30"
                style={{
                  clipPath:
                    'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',
                }}
              />
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
