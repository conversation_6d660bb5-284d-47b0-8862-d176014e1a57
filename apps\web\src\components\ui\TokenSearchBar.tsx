'use client';

import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  MagnifyingGlassIcon, 
  XMarkIcon,
  ClockIcon,
  FireIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';
import { useDebounce } from '@/hooks/useDebounce';
import { useTokenSearch } from '@/hooks/useTokenSearch';
import { cn } from '@/lib/utils';

interface TokenSearchBarProps {
  placeholder?: string;
  className?: string;
  onSelect?: (token: any) => void;
  autoFocus?: boolean;
}

interface SearchResult {
  address: string;
  symbol: string;
  name: string;
  logoUrl?: string;
  chain: string;
  marketCap?: number;
  riskScore?: number;
  isVerified?: boolean;
}

const popularTokens: SearchResult[] = [
  {
    address: '******************************************',
    symbol: 'UNI',
    name: 'Uniswap',
    chain: 'ethereum',
    marketCap: 4200000000,
    riskScore: 25,
    isVerified: true,
  },
  {
    address: '******************************************',
    symbol: 'DAI',
    name: 'Dai Stablecoin',
    chain: 'ethereum',
    marketCap: 5100000000,
    riskScore: 15,
    isVerified: true,
  },
  {
    address: '******************************************',
    symbol: 'LINK',
    name: 'Chainlink',
    chain: 'ethereum',
    marketCap: 8900000000,
    riskScore: 20,
    isVerified: true,
  },
];

const recentSearches: SearchResult[] = [
  {
    address: '******************************************',
    symbol: 'SHIB',
    name: 'Shiba Inu',
    chain: 'ethereum',
    riskScore: 65,
  },
  {
    address: '******************************************',
    symbol: 'WBTC',
    name: 'Wrapped Bitcoin',
    chain: 'ethereum',
    riskScore: 30,
    isVerified: true,
  },
];

export function TokenSearchBar({ 
  placeholder = "Search tokens by address or symbol...", 
  className,
  onSelect,
  autoFocus = false 
}: TokenSearchBarProps) {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();
  
  const debouncedQuery = useDebounce(query, 300);
  const { data: searchResults, isLoading } = useTokenSearch(debouncedQuery);

  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    setSelectedIndex(-1);
    setIsOpen(value.length > 0);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    const results = searchResults || [];
    const showingResults = query.length > 0 ? results : [...recentSearches, ...popularTokens];

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < showingResults.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && showingResults[selectedIndex]) {
          handleSelect(showingResults[selectedIndex]);
        } else if (query.trim()) {
          handleAnalyze(query.trim());
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  const handleSelect = (token: SearchResult) => {
    setQuery(`${token.symbol} - ${token.name}`);
    setIsOpen(false);
    setSelectedIndex(-1);
    
    if (onSelect) {
      onSelect(token);
    } else {
      router.push(`/analyze/${token.chain}/${token.address}`);
    }
  };

  const handleAnalyze = (searchQuery: string) => {
    setIsOpen(false);
    
    // Check if it's an address (starts with 0x and is 42 characters)
    if (searchQuery.startsWith('0x') && searchQuery.length === 42) {
      router.push(`/analyze/ethereum/${searchQuery}`);
    } else {
      // Search by symbol
      router.push(`/analyze?q=${encodeURIComponent(searchQuery)}`);
    }
  };

  const clearSearch = () => {
    setQuery('');
    setIsOpen(false);
    setSelectedIndex(-1);
    inputRef.current?.focus();
  };

  const formatMarketCap = (marketCap: number) => {
    if (marketCap >= 1e9) {
      return `$${(marketCap / 1e9).toFixed(1)}B`;
    } else if (marketCap >= 1e6) {
      return `$${(marketCap / 1e6).toFixed(1)}M`;
    } else if (marketCap >= 1e3) {
      return `$${(marketCap / 1e3).toFixed(1)}K`;
    }
    return `$${marketCap}`;
  };

  const getRiskBadge = (riskScore?: number) => {
    if (!riskScore) return null;
    
    if (riskScore < 30) {
      return <span className="badge badge-risk-low text-xs">Low Risk</span>;
    } else if (riskScore < 60) {
      return <span className="badge badge-risk-medium text-xs">Medium Risk</span>;
    } else if (riskScore < 80) {
      return <span className="badge badge-risk-high text-xs">High Risk</span>;
    } else {
      return <span className="badge badge-risk-critical text-xs">Critical</span>;
    }
  };

  const showingResults = query.length > 0 ? searchResults || [] : [];
  const showingSuggestions = query.length === 0;

  return (
    <div className={cn("relative w-full max-w-2xl", className)}>
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
          <MagnifyingGlassIcon className="h-5 w-5 text-muted-foreground" />
        </div>
        
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsOpen(true)}
          placeholder={placeholder}
          className="input w-full pl-12 pr-12 py-4 text-lg rounded-xl border-2 border-border focus:border-primary transition-colors"
        />
        
        {query && (
          <button
            onClick={clearSearch}
            className="absolute inset-y-0 right-0 pr-4 flex items-center text-muted-foreground hover:text-foreground transition-colors"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        )}
      </div>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full left-0 right-0 mt-2 bg-card border border-border rounded-xl shadow-lg z-50 max-h-96 overflow-y-auto"
          >
            {isLoading && query.length > 0 && (
              <div className="p-4 text-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"></div>
                <p className="text-sm text-muted-foreground mt-2">Searching tokens...</p>
              </div>
            )}

            {showingSuggestions && (
              <>
                {recentSearches.length > 0 && (
                  <div className="p-4 border-b border-border">
                    <div className="flex items-center space-x-2 mb-3">
                      <ClockIcon className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium text-muted-foreground">Recent Searches</span>
                    </div>
                    <div className="space-y-1">
                      {recentSearches.map((token, index) => (
                        <button
                          key={`recent-${token.address}`}
                          onClick={() => handleSelect(token)}
                          className={cn(
                            "w-full flex items-center justify-between p-3 rounded-lg hover:bg-muted transition-colors text-left",
                            selectedIndex === index && "bg-muted"
                          )}
                        >
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                              <span className="text-xs font-bold text-primary">
                                {token.symbol.charAt(0)}
                              </span>
                            </div>
                            <div>
                              <div className="font-medium">{token.symbol}</div>
                              <div className="text-sm text-muted-foreground">{token.name}</div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            {token.isVerified && (
                              <ShieldCheckIcon className="h-4 w-4 text-green-500" />
                            )}
                            {getRiskBadge(token.riskScore)}
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                <div className="p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <FireIcon className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium text-muted-foreground">Popular Tokens</span>
                  </div>
                  <div className="space-y-1">
                    {popularTokens.map((token, index) => (
                      <button
                        key={`popular-${token.address}`}
                        onClick={() => handleSelect(token)}
                        className={cn(
                          "w-full flex items-center justify-between p-3 rounded-lg hover:bg-muted transition-colors text-left",
                          selectedIndex === recentSearches.length + index && "bg-muted"
                        )}
                      >
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                            <span className="text-xs font-bold text-primary">
                              {token.symbol.charAt(0)}
                            </span>
                          </div>
                          <div>
                            <div className="font-medium">{token.symbol}</div>
                            <div className="text-sm text-muted-foreground">{token.name}</div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {token.marketCap && (
                            <span className="text-xs text-muted-foreground">
                              {formatMarketCap(token.marketCap)}
                            </span>
                          )}
                          {token.isVerified && (
                            <ShieldCheckIcon className="h-4 w-4 text-green-500" />
                          )}
                          {getRiskBadge(token.riskScore)}
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              </>
            )}

            {showingResults.length > 0 && (
              <div className="p-4">
                <div className="space-y-1">
                  {showingResults.map((token, index) => (
                    <button
                      key={token.address}
                      onClick={() => handleSelect(token)}
                      className={cn(
                        "w-full flex items-center justify-between p-3 rounded-lg hover:bg-muted transition-colors text-left",
                        selectedIndex === index && "bg-muted"
                      )}
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                          <span className="text-xs font-bold text-primary">
                            {token.symbol.charAt(0)}
                          </span>
                        </div>
                        <div>
                          <div className="font-medium">{token.symbol}</div>
                          <div className="text-sm text-muted-foreground">{token.name}</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {token.marketCap && (
                          <span className="text-xs text-muted-foreground">
                            {formatMarketCap(token.marketCap)}
                          </span>
                        )}
                        {token.isVerified && (
                          <ShieldCheckIcon className="h-4 w-4 text-green-500" />
                        )}
                        {getRiskBadge(token.riskScore)}
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            )}

            {query.length > 0 && !isLoading && showingResults.length === 0 && (
              <div className="p-4 text-center">
                <p className="text-muted-foreground">No tokens found</p>
                <button
                  onClick={() => handleAnalyze(query)}
                  className="mt-2 text-sm text-primary hover:underline"
                >
                  Analyze "{query}" anyway
                </button>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
