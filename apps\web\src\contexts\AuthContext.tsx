'use client';

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { useAccount, useSignMessage, useDisconnect } from 'wagmi';
import { toast } from 'react-hot-toast';
import { apiClient } from '@/lib/api';

interface User {
  id: string;
  address: string;
  username?: string;
  email?: string;
  avatar?: string;
  isVerified: boolean;
  isPremium: boolean;
  createdAt: string;
  preferences: {
    theme: 'light' | 'dark' | 'system';
    currency: string;
    notifications: boolean;
  };
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: () => Promise<void>;
  logout: () => Promise<void>;
  updateProfile: (data: Partial<User>) => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  const { address, isConnected } = useAccount();
  const { signMessageAsync } = useSignMessage();
  const { disconnect } = useDisconnect();

  const isAuthenticated = !!user && isConnected;

  // Generate authentication message
  const generateAuthMessage = useCallback((address: string, nonce: string) => {
    return `Welcome to CryptoSentinel!

This request will not trigger a blockchain transaction or cost any gas fees.

Your authentication status will reset after 24 hours.

Wallet address: ${address}
Nonce: ${nonce}`;
  }, []);

  // Login with wallet signature
  const login = useCallback(async () => {
    if (!address || !isConnected) {
      toast.error('Please connect your wallet first');
      return;
    }

    try {
      setIsLoading(true);

      // Get nonce from server
      const { data: nonceData } = await apiClient.post('/auth/nonce', {
        address: address.toLowerCase(),
      });

      const message = generateAuthMessage(address, nonceData.nonce);

      // Sign message
      const signature = await signMessageAsync({
        message,
      });

      // Verify signature and login
      const { data: authData } = await apiClient.post('/auth/verify', {
        address: address.toLowerCase(),
        message,
        signature,
      });

      // Store token
      localStorage.setItem('auth_token', authData.token);
      
      // Set user data
      setUser(authData.user);
      
      toast.success('Successfully authenticated!');
    } catch (error: any) {
      console.error('Authentication failed:', error);
      toast.error(error.response?.data?.message || 'Authentication failed');
    } finally {
      setIsLoading(false);
    }
  }, [address, isConnected, signMessageAsync, generateAuthMessage]);

  // Logout
  const logout = useCallback(async () => {
    try {
      setIsLoading(true);
      
      // Call logout endpoint
      await apiClient.post('/auth/logout');
      
      // Clear local storage
      localStorage.removeItem('auth_token');
      
      // Clear user state
      setUser(null);
      
      // Disconnect wallet
      disconnect();
      
      toast.success('Logged out successfully');
    } catch (error) {
      console.error('Logout failed:', error);
      // Clear local state anyway
      localStorage.removeItem('auth_token');
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  }, [disconnect]);

  // Update user profile
  const updateProfile = useCallback(async (data: Partial<User>) => {
    if (!user) return;

    try {
      const { data: updatedUser } = await apiClient.patch('/auth/profile', data);
      setUser(updatedUser);
      toast.success('Profile updated successfully');
    } catch (error: any) {
      console.error('Profile update failed:', error);
      toast.error(error.response?.data?.message || 'Failed to update profile');
      throw error;
    }
  }, [user]);

  // Refresh user data
  const refreshUser = useCallback(async () => {
    const token = localStorage.getItem('auth_token');
    if (!token) {
      setIsLoading(false);
      return;
    }

    try {
      const { data: userData } = await apiClient.get('/auth/me');
      setUser(userData);
    } catch (error: any) {
      console.error('Failed to refresh user:', error);
      // If token is invalid, clear it
      if (error.response?.status === 401) {
        localStorage.removeItem('auth_token');
        setUser(null);
      }
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Initialize auth state
  useEffect(() => {
    refreshUser();
  }, [refreshUser]);

  // Handle wallet connection changes
  useEffect(() => {
    if (!isConnected && user) {
      // Wallet disconnected, but user is still logged in
      // Keep user logged in but show warning
      toast.error('Wallet disconnected. Please reconnect to continue using all features.');
    }
  }, [isConnected, user]);

  // Auto-login when wallet connects
  useEffect(() => {
    if (isConnected && address && !user && !isLoading) {
      // Check if we have a stored token for this address
      const token = localStorage.getItem('auth_token');
      if (token) {
        refreshUser();
      }
    }
  }, [isConnected, address, user, isLoading, refreshUser]);

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    updateProfile,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Higher-order component for protected routes
export function withAuth<P extends object>(
  Component: React.ComponentType<P>
) {
  return function AuthenticatedComponent(props: P) {
    const { isAuthenticated, isLoading, login } = useAuth();
    const { isConnected } = useAccount();

    if (isLoading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      );
    }

    if (!isConnected) {
      return (
        <div className="flex flex-col items-center justify-center min-h-screen space-y-4">
          <h2 className="text-2xl font-bold">Wallet Required</h2>
          <p className="text-muted-foreground text-center max-w-md">
            Please connect your wallet to access this feature.
          </p>
        </div>
      );
    }

    if (!isAuthenticated) {
      return (
        <div className="flex flex-col items-center justify-center min-h-screen space-y-4">
          <h2 className="text-2xl font-bold">Authentication Required</h2>
          <p className="text-muted-foreground text-center max-w-md">
            Please sign a message to verify your wallet ownership and access this feature.
          </p>
          <button onClick={login} className="btn btn-primary">
            Sign Message to Continue
          </button>
        </div>
      );
    }

    return <Component {...props} />;
  };
}
