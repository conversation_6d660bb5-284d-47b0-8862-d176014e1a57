import { useQuery } from '@tanstack/react-query';
import { TokenAPI } from '@/lib/api';

export interface TokenSearchResult {
  address: string;
  symbol: string;
  name: string;
  logoUrl?: string;
  chain: string;
  marketCap?: number;
  price?: number;
  volume24h?: number;
  riskScore?: number;
  isVerified?: boolean;
  tags?: string[];
}

export function useTokenSearch(query: string, enabled: boolean = true) {
  return useQuery({
    queryKey: ['tokenSearch', query],
    queryFn: async () => {
      if (!query || query.length < 2) {
        return [];
      }
      
      const response = await TokenAPI.searchTokens(query);
      return response.data as TokenSearchResult[];
    },
    enabled: enabled && query.length >= 2,
    staleTime: 30000, // 30 seconds
    cacheTime: 300000, // 5 minutes
    retry: 2,
  });
}

export function usePopularTokens(chain?: string) {
  return useQuery({
    queryKey: ['popularTokens', chain],
    queryFn: async () => {
      const response = await TokenAPI.getPopularTokens(chain);
      return response.data as TokenSearchResult[];
    },
    staleTime: 300000, // 5 minutes
    cacheTime: 600000, // 10 minutes
  });
}

export function useTrendingTokens(timeframe: string = '24h') {
  return useQuery({
    queryKey: ['trendingTokens', timeframe],
    queryFn: async () => {
      const response = await TokenAPI.getTrendingTokens(timeframe);
      return response.data as TokenSearchResult[];
    },
    staleTime: 60000, // 1 minute
    cacheTime: 300000, // 5 minutes
    refetchInterval: 60000, // Refetch every minute
  });
}
