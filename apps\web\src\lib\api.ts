import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { toast } from 'react-hot-toast';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
const AI_SERVICE_URL = process.env.NEXT_PUBLIC_AI_SERVICE_URL || 'http://localhost:8000';

// Create axios instances
const createApiClient = (baseURL: string): AxiosInstance => {
  const client = axios.create({
    baseURL,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Request interceptor to add auth token
  client.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem('auth_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Response interceptor for error handling
  client.interceptors.response.use(
    (response: AxiosResponse) => {
      return response;
    },
    (error) => {
      const { response } = error;

      if (response?.status === 401) {
        // Unauthorized - clear token and redirect to login
        localStorage.removeItem('auth_token');
        if (typeof window !== 'undefined') {
          window.location.href = '/';
        }
        toast.error('Session expired. Please login again.');
      } else if (response?.status === 403) {
        toast.error('Access denied. Insufficient permissions.');
      } else if (response?.status === 429) {
        toast.error('Too many requests. Please try again later.');
      } else if (response?.status >= 500) {
        toast.error('Server error. Please try again later.');
      } else if (!response) {
        toast.error('Network error. Please check your connection.');
      }

      return Promise.reject(error);
    }
  );

  return client;
};

// API clients
export const apiClient = createApiClient(`${API_BASE_URL}/api`);
export const aiClient = createApiClient(AI_SERVICE_URL);

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  timestamp: string;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// API Service Classes
export class AuthAPI {
  static async getNonce(address: string) {
    return apiClient.post<{ nonce: string }>('/auth/nonce', { address });
  }

  static async verify(address: string, message: string, signature: string) {
    return apiClient.post<{ token: string; user: any }>('/auth/verify', {
      address,
      message,
      signature,
    });
  }

  static async getProfile() {
    return apiClient.get<any>('/auth/me');
  }

  static async updateProfile(data: any) {
    return apiClient.patch<any>('/auth/profile', data);
  }

  static async logout() {
    return apiClient.post('/auth/logout');
  }
}

export class TokenAPI {
  static async searchTokens(query: string) {
    return apiClient.get<any[]>('/tokens/search', {
      params: { q: query },
    });
  }

  static async getTokenInfo(chain: string, address: string) {
    return apiClient.get<any>(`/tokens/${chain}/${address}`);
  }

  static async getTokenMetrics(chain: string, address: string) {
    return apiClient.get<any>(`/tokens/${chain}/${address}/metrics`);
  }

  static async getPopularTokens(chain?: string) {
    return apiClient.get<any[]>('/tokens/popular', {
      params: { chain },
    });
  }

  static async getTrendingTokens(timeframe: string = '24h') {
    return apiClient.get<any[]>('/tokens/trending', {
      params: { timeframe },
    });
  }
}

export class AnalysisAPI {
  static async analyzeToken(chain: string, address: string, options?: {
    includeSocial?: boolean;
    includeDeveloper?: boolean;
    forceRefresh?: boolean;
  }) {
    return aiClient.post<any>('/analyze/token', {
      token_address: address,
      chain,
      include_social: options?.includeSocial ?? true,
      include_developer: options?.includeDeveloper ?? true,
      force_refresh: options?.forceRefresh ?? false,
    });
  }

  static async analyzeSentiment(query: string, options?: {
    timeframe?: string;
    sources?: string[];
  }) {
    return aiClient.post<any>('/analyze/sentiment', {
      query,
      timeframe: options?.timeframe ?? '24h',
      sources: options?.sources ?? ['twitter', 'reddit'],
    });
  }

  static async batchAnalyze(tokens: Array<{ chain: string; address: string }>) {
    return aiClient.post<any[]>('/analyze/batch', 
      tokens.map(token => ({
        token_address: token.address,
        chain: token.chain,
      }))
    );
  }

  static async getAnalysisHistory(chain: string, address: string) {
    return apiClient.get<any[]>(`/analysis/${chain}/${address}/history`);
  }
}

export class PortfolioAPI {
  static async getPortfolios() {
    return apiClient.get<any[]>('/portfolio');
  }

  static async createPortfolio(data: { name: string; description?: string }) {
    return apiClient.post<any>('/portfolio', data);
  }

  static async getPortfolio(id: string) {
    return apiClient.get<any>(`/portfolio/${id}`);
  }

  static async updatePortfolio(id: string, data: any) {
    return apiClient.patch<any>(`/portfolio/${id}`, data);
  }

  static async deletePortfolio(id: string) {
    return apiClient.delete(`/portfolio/${id}`);
  }

  static async addToken(portfolioId: string, tokenData: {
    chain: string;
    address: string;
    amount: string;
    averagePrice?: number;
  }) {
    return apiClient.post<any>(`/portfolio/${portfolioId}/tokens`, tokenData);
  }

  static async removeToken(portfolioId: string, tokenId: string) {
    return apiClient.delete(`/portfolio/${portfolioId}/tokens/${tokenId}`);
  }

  static async updateToken(portfolioId: string, tokenId: string, data: any) {
    return apiClient.patch<any>(`/portfolio/${portfolioId}/tokens/${tokenId}`, data);
  }
}

export class AlertAPI {
  static async getAlerts() {
    return apiClient.get<any[]>('/alerts');
  }

  static async createAlert(data: {
    tokenAddress: string;
    chain: string;
    type: string;
    condition: any;
  }) {
    return apiClient.post<any>('/alerts', data);
  }

  static async updateAlert(id: string, data: any) {
    return apiClient.patch<any>(`/alerts/${id}`, data);
  }

  static async deleteAlert(id: string) {
    return apiClient.delete(`/alerts/${id}`);
  }

  static async toggleAlert(id: string, isActive: boolean) {
    return apiClient.patch<any>(`/alerts/${id}/toggle`, { isActive });
  }
}

export class SocialAPI {
  static async getSocialMetrics(tokenSymbol: string, timeframe: string = '24h') {
    return apiClient.get<any>('/social/metrics', {
      params: { symbol: tokenSymbol, timeframe },
    });
  }

  static async getSocialMentions(tokenSymbol: string, options?: {
    platform?: string;
    limit?: number;
    offset?: number;
  }) {
    return apiClient.get<any[]>('/social/mentions', {
      params: { symbol: tokenSymbol, ...options },
    });
  }

  static async getTrendingTopics(timeframe: string = '24h') {
    return apiClient.get<any[]>('/social/trending', {
      params: { timeframe },
    });
  }
}

export class BlockchainAPI {
  static async getChainInfo(chain: string) {
    return apiClient.get<any>(`/blockchain/${chain}/info`);
  }

  static async getTransactionHistory(chain: string, address: string, options?: {
    limit?: number;
    offset?: number;
  }) {
    return apiClient.get<any[]>(`/blockchain/${chain}/${address}/transactions`, {
      params: options,
    });
  }

  static async getHolders(chain: string, address: string, options?: {
    limit?: number;
    offset?: number;
  }) {
    return apiClient.get<any[]>(`/blockchain/${chain}/${address}/holders`, {
      params: options,
    });
  }

  static async getLiquidityPools(chain: string, address: string) {
    return apiClient.get<any[]>(`/blockchain/${chain}/${address}/liquidity`);
  }
}

// Utility functions
export const handleApiError = (error: any, defaultMessage: string = 'An error occurred') => {
  const message = error.response?.data?.message || error.message || defaultMessage;
  toast.error(message);
  console.error('API Error:', error);
  return message;
};

export const isApiError = (error: any): boolean => {
  return error.response && error.response.status;
};

// Request configuration helpers
export const withTimeout = (timeout: number): AxiosRequestConfig => ({
  timeout,
});

export const withRetry = (retries: number = 3) => {
  return {
    'axios-retry': {
      retries,
      retryDelay: (retryCount: number) => retryCount * 1000,
    },
  };
};

// Export default client for backward compatibility
export default apiClient;
