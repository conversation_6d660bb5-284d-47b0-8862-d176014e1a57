/**
 * TokenForge Blockchain Configuration
 * Multi-chain RPC provider configuration and management
 */

const { ethers } = require('ethers');

// Blockchain Network Configurations
const networkConfigs = {
  ethereum: {
    chainId: 1,
    name: 'Ethereum Mainnet',
    symbol: 'ETH',
    rpcUrl: process.env.ETHEREUM_RPC_URL,
    wsUrl: process.env.ETHEREUM_WS_URL,
    explorerUrl: 'https://etherscan.io',
    explorerApiUrl: 'https://api.etherscan.io/api',
    explorerApiKey: process.env.ETHERSCAN_API_KEY,
    multicallAddress: '******************************************',
    gasLimit: 21000,
    maxFeePerGas: '**********0', // 20 gwei
    maxPriorityFeePerGas: '**********', // 2 gwei
  },
  bsc: {
    chainId: 56,
    name: 'Binance Smart Chain',
    symbol: 'BN<PERSON>',
    rpcUrl: process.env.BSC_RPC_URL || 'https://bsc-dataseed.binance.org/',
    wsUrl: process.env.BSC_WS_URL,
    explorerUrl: 'https://bscscan.com',
    explorerApiUrl: 'https://api.bscscan.com/api',
    explorerApiKey: process.env.BSCSCAN_API_KEY,
    multicallAddress: '******************************************',
    gasLimit: 21000,
    gasPrice: '5000000000', // 5 gwei
  },
  polygon: {
    chainId: 137,
    name: 'Polygon',
    symbol: 'MATIC',
    rpcUrl: process.env.POLYGON_RPC_URL || 'https://polygon-rpc.com/',
    wsUrl: process.env.POLYGON_WS_URL,
    explorerUrl: 'https://polygonscan.com',
    explorerApiUrl: 'https://api.polygonscan.com/api',
    explorerApiKey: process.env.POLYGONSCAN_API_KEY,
    multicallAddress: '******************************************',
    gasLimit: 21000,
    gasPrice: '30000000000', // 30 gwei
  },
  arbitrum: {
    chainId: 42161,
    name: 'Arbitrum One',
    symbol: 'ETH',
    rpcUrl: process.env.ARBITRUM_RPC_URL || 'https://arb1.arbitrum.io/rpc',
    wsUrl: process.env.ARBITRUM_WS_URL,
    explorerUrl: 'https://arbiscan.io',
    explorerApiUrl: 'https://api.arbiscan.io/api',
    explorerApiKey: process.env.ARBISCAN_API_KEY,
    multicallAddress: '******************************************',
    gasLimit: 21000,
    gasPrice: '100000000', // 0.1 gwei
  },
  optimism: {
    chainId: 10,
    name: 'Optimism',
    symbol: 'ETH',
    rpcUrl: process.env.OPTIMISM_RPC_URL || 'https://mainnet.optimism.io',
    wsUrl: process.env.OPTIMISM_WS_URL,
    explorerUrl: 'https://optimistic.etherscan.io',
    explorerApiUrl: 'https://api-optimistic.etherscan.io/api',
    explorerApiKey: process.env.OPTIMISTIC_ETHERSCAN_API_KEY,
    multicallAddress: '******************************************',
    gasLimit: 21000,
    gasPrice: '1000000', // 0.001 gwei
  },
  avalanche: {
    chainId: 43114,
    name: 'Avalanche C-Chain',
    symbol: 'AVAX',
    rpcUrl: process.env.AVALANCHE_RPC_URL || 'https://api.avax.network/ext/bc/C/rpc',
    wsUrl: process.env.AVALANCHE_WS_URL,
    explorerUrl: 'https://snowtrace.io',
    explorerApiUrl: 'https://api.snowtrace.io/api',
    explorerApiKey: process.env.SNOWTRACE_API_KEY,
    multicallAddress: '******************************************',
    gasLimit: 21000,
    gasPrice: '25000000000', // 25 gwei
  },
  fantom: {
    chainId: 250,
    name: 'Fantom Opera',
    symbol: 'FTM',
    rpcUrl: process.env.FANTOM_RPC_URL || 'https://rpc.ftm.tools/',
    wsUrl: process.env.FANTOM_WS_URL,
    explorerUrl: 'https://ftmscan.com',
    explorerApiUrl: 'https://api.ftmscan.com/api',
    explorerApiKey: process.env.FTMSCAN_API_KEY,
    multicallAddress: '******************************************',
    gasLimit: 21000,
    gasPrice: '1000000000', // 1 gwei
  }
};

// Testnet configurations
const testnetConfigs = {
  goerli: {
    chainId: 5,
    name: 'Goerli Testnet',
    symbol: 'ETH',
    rpcUrl: process.env.GOERLI_RPC_URL,
    explorerUrl: 'https://goerli.etherscan.io',
    explorerApiUrl: 'https://api-goerli.etherscan.io/api',
    explorerApiKey: process.env.ETHERSCAN_API_KEY,
  },
  bscTestnet: {
    chainId: 97,
    name: 'BSC Testnet',
    symbol: 'tBNB',
    rpcUrl: process.env.BSC_TESTNET_RPC_URL || 'https://data-seed-prebsc-1-s1.binance.org:8545/',
    explorerUrl: 'https://testnet.bscscan.com',
    explorerApiUrl: 'https://api-testnet.bscscan.com/api',
    explorerApiKey: process.env.BSCSCAN_API_KEY,
  },
  mumbai: {
    chainId: 80001,
    name: 'Polygon Mumbai',
    symbol: 'MATIC',
    rpcUrl: process.env.MUMBAI_RPC_URL || 'https://rpc-mumbai.maticvigil.com/',
    explorerUrl: 'https://mumbai.polygonscan.com',
    explorerApiUrl: 'https://api-testnet.polygonscan.com/api',
    explorerApiKey: process.env.POLYGONSCAN_API_KEY,
  }
};

class BlockchainManager {
  constructor() {
    this.providers = new Map();
    this.wsProviders = new Map();
    this.signers = new Map();
    this.environment = process.env.NODE_ENV || 'development';
    this.configs = this.environment === 'production' ? networkConfigs : { ...networkConfigs, ...testnetConfigs };
  }

  // Initialize blockchain providers
  async initialize() {
    try {
      for (const [network, config] of Object.entries(this.configs)) {
        if (config.rpcUrl) {
          // HTTP Provider
          const provider = new ethers.JsonRpcProvider(config.rpcUrl, {
            chainId: config.chainId,
            name: config.name
          });

          // Test connection
          try {
            await provider.getNetwork();
            this.providers.set(network, provider);
            console.log(`✅ ${config.name} HTTP provider connected`);
          } catch (error) {
            console.warn(`⚠️ ${config.name} HTTP provider failed:`, error.message);
          }

          // WebSocket Provider (if available)
          if (config.wsUrl) {
            try {
              const wsProvider = new ethers.WebSocketProvider(config.wsUrl);
              await wsProvider.getNetwork();
              this.wsProviders.set(network, wsProvider);
              console.log(`✅ ${config.name} WebSocket provider connected`);
            } catch (error) {
              console.warn(`⚠️ ${config.name} WebSocket provider failed:`, error.message);
            }
          }
        }
      }

      console.log(`✅ Blockchain manager initialized with ${this.providers.size} networks`);
      return this.providers;
    } catch (error) {
      console.error('❌ Blockchain manager initialization failed:', error);
      throw error;
    }
  }

  // Get provider for specific network
  getProvider(network) {
    const provider = this.providers.get(network);
    if (!provider) {
      throw new Error(`Provider not found for network: ${network}`);
    }
    return provider;
  }

  // Get WebSocket provider for specific network
  getWSProvider(network) {
    const wsProvider = this.wsProviders.get(network);
    if (!wsProvider) {
      throw new Error(`WebSocket provider not found for network: ${network}`);
    }
    return wsProvider;
  }

  // Get network configuration
  getNetworkConfig(network) {
    const config = this.configs[network];
    if (!config) {
      throw new Error(`Network configuration not found: ${network}`);
    }
    return config;
  }

  // Get all available networks
  getAvailableNetworks() {
    return Array.from(this.providers.keys());
  }

  // Detect network from chain ID
  getNetworkByChainId(chainId) {
    for (const [network, config] of Object.entries(this.configs)) {
      if (config.chainId === chainId) {
        return network;
      }
    }
    throw new Error(`Network not found for chain ID: ${chainId}`);
  }

  // Get gas price for network
  async getGasPrice(network) {
    try {
      const provider = this.getProvider(network);
      const gasPrice = await provider.getFeeData();
      return gasPrice;
    } catch (error) {
      console.error(`❌ Error getting gas price for ${network}:`, error);
      throw error;
    }
  }

  // Get block number for network
  async getBlockNumber(network) {
    try {
      const provider = this.getProvider(network);
      return await provider.getBlockNumber();
    } catch (error) {
      console.error(`❌ Error getting block number for ${network}:`, error);
      throw error;
    }
  }

  // Health check for all providers
  async healthCheck() {
    const health = {
      networks: {},
      timestamp: new Date().toISOString()
    };

    for (const [network, provider] of this.providers) {
      try {
        const start = Date.now();
        await provider.getBlockNumber();
        const latency = Date.now() - start;
        
        health.networks[network] = {
          status: 'healthy',
          latency: `${latency}ms`,
          chainId: this.configs[network].chainId
        };
      } catch (error) {
        health.networks[network] = {
          status: 'unhealthy',
          error: error.message,
          chainId: this.configs[network].chainId
        };
      }
    }

    return health;
  }

  // Graceful shutdown
  async close() {
    try {
      // Close WebSocket providers
      for (const [network, wsProvider] of this.wsProviders) {
        try {
          await wsProvider.destroy();
          console.log(`✅ ${network} WebSocket provider closed`);
        } catch (error) {
          console.error(`❌ Error closing ${network} WebSocket provider:`, error);
        }
      }

      console.log('✅ All blockchain providers closed');
    } catch (error) {
      console.error('❌ Error closing blockchain providers:', error);
    }
  }
}

// Singleton instance
const blockchainManager = new BlockchainManager();

module.exports = {
  BlockchainManager,
  blockchainManager,
  networkConfigs,
  testnetConfigs
};
