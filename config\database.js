/**
 * TokenForge Database Configuration
 * Handles PostgreSQL and MongoDB connections with connection pooling
 */

const { Pool } = require('pg');
const { MongoClient } = require('mongodb');

// PostgreSQL Configuration
const postgresConfig = {
  development: {
    host: process.env.DATABASE_HOST || 'localhost',
    port: parseInt(process.env.DATABASE_PORT) || 5432,
    database: process.env.DATABASE_NAME || 'tokenforge',
    user: process.env.DATABASE_USER || 'tokenforge',
    password: process.env.DATABASE_PASSWORD || 'password',
    ssl: process.env.DATABASE_SSL === 'true' ? { rejectUnauthorized: false } : false,
    max: parseInt(process.env.DATABASE_POOL_MAX) || 10,
    min: parseInt(process.env.DATABASE_POOL_MIN) || 2,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
  },
  test: {
    host: process.env.TEST_DATABASE_HOST || 'localhost',
    port: parseInt(process.env.TEST_DATABASE_PORT) || 5432,
    database: process.env.TEST_DATABASE_NAME || 'tokenforge_test',
    user: process.env.TEST_DATABASE_USER || 'tokenforge',
    password: process.env.TEST_DATABASE_PASSWORD || 'password',
    ssl: false,
    max: 5,
    min: 1,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
  },
  production: {
    connectionString: process.env.DATABASE_URL,
    ssl: { rejectUnauthorized: false },
    max: parseInt(process.env.DATABASE_POOL_MAX) || 20,
    min: parseInt(process.env.DATABASE_POOL_MIN) || 5,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
  }
};

// MongoDB Configuration
const mongoConfig = {
  development: {
    url: process.env.MONGODB_URL || 'mongodb://localhost:27017/tokenforge_analytics',
    options: {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      bufferMaxEntries: 0,
      useNewUrlParser: true,
      useUnifiedTopology: true,
    }
  },
  test: {
    url: process.env.TEST_MONGODB_URL || 'mongodb://localhost:27017/tokenforge_analytics_test',
    options: {
      maxPoolSize: 5,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      bufferMaxEntries: 0,
      useNewUrlParser: true,
      useUnifiedTopology: true,
    }
  },
  production: {
    url: process.env.MONGODB_URL,
    options: {
      maxPoolSize: 20,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      bufferMaxEntries: 0,
      useNewUrlParser: true,
      useUnifiedTopology: true,
    }
  }
};

class DatabaseManager {
  constructor() {
    this.pgPool = null;
    this.mongoClient = null;
    this.mongoDb = null;
    this.environment = process.env.NODE_ENV || 'development';
  }

  // Initialize PostgreSQL connection
  async initializePostgreSQL() {
    try {
      const config = postgresConfig[this.environment];
      this.pgPool = new Pool(config);

      // Test connection
      const client = await this.pgPool.connect();
      console.log('✅ PostgreSQL connected successfully');
      client.release();

      // Handle pool errors
      this.pgPool.on('error', (err) => {
        console.error('❌ PostgreSQL pool error:', err);
      });

      return this.pgPool;
    } catch (error) {
      console.error('❌ PostgreSQL connection failed:', error);
      throw error;
    }
  }

  // Initialize MongoDB connection
  async initializeMongoDB() {
    try {
      const config = mongoConfig[this.environment];
      this.mongoClient = new MongoClient(config.url, config.options);
      
      await this.mongoClient.connect();
      this.mongoDb = this.mongoClient.db();
      
      console.log('✅ MongoDB connected successfully');
      return this.mongoDb;
    } catch (error) {
      console.error('❌ MongoDB connection failed:', error);
      throw error;
    }
  }

  // Get PostgreSQL pool
  getPostgreSQLPool() {
    if (!this.pgPool) {
      throw new Error('PostgreSQL not initialized. Call initializePostgreSQL() first.');
    }
    return this.pgPool;
  }

  // Get MongoDB database
  getMongoDB() {
    if (!this.mongoDb) {
      throw new Error('MongoDB not initialized. Call initializeMongoDB() first.');
    }
    return this.mongoDb;
  }

  // Execute PostgreSQL query with error handling
  async executeQuery(query, params = []) {
    const client = await this.pgPool.connect();
    try {
      const result = await client.query(query, params);
      return result;
    } catch (error) {
      console.error('❌ PostgreSQL query error:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  // Execute MongoDB operation with error handling
  async executeMongoOperation(collection, operation, ...args) {
    try {
      const coll = this.mongoDb.collection(collection);
      return await coll[operation](...args);
    } catch (error) {
      console.error('❌ MongoDB operation error:', error);
      throw error;
    }
  }

  // Health check for both databases
  async healthCheck() {
    const health = {
      postgresql: false,
      mongodb: false,
      timestamp: new Date().toISOString()
    };

    // Check PostgreSQL
    try {
      await this.executeQuery('SELECT 1');
      health.postgresql = true;
    } catch (error) {
      console.error('PostgreSQL health check failed:', error);
    }

    // Check MongoDB
    try {
      await this.mongoDb.admin().ping();
      health.mongodb = true;
    } catch (error) {
      console.error('MongoDB health check failed:', error);
    }

    return health;
  }

  // Graceful shutdown
  async close() {
    try {
      if (this.pgPool) {
        await this.pgPool.end();
        console.log('✅ PostgreSQL connection closed');
      }
      
      if (this.mongoClient) {
        await this.mongoClient.close();
        console.log('✅ MongoDB connection closed');
      }
    } catch (error) {
      console.error('❌ Error closing database connections:', error);
    }
  }
}

// Singleton instance
const databaseManager = new DatabaseManager();

module.exports = {
  DatabaseManager,
  databaseManager,
  postgresConfig,
  mongoConfig
};
