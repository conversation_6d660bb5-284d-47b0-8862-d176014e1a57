/**
 * TokenForge Redis Configuration
 * Handles Redis connections for caching, sessions, and rate limiting
 */

const Redis = require('ioredis');

// Redis Configuration
const redisConfig = {
  development: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT) || 6379,
    password: process.env.REDIS_PASSWORD || undefined,
    db: parseInt(process.env.REDIS_DB) || 0,
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3,
    lazyConnect: true,
    keepAlive: 30000,
    connectTimeout: 10000,
    commandTimeout: 5000,
  },
  test: {
    host: process.env.TEST_REDIS_HOST || 'localhost',
    port: parseInt(process.env.TEST_REDIS_PORT) || 6379,
    password: process.env.TEST_REDIS_PASSWORD || undefined,
    db: parseInt(process.env.TEST_REDIS_DB) || 1,
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3,
    lazyConnect: true,
  },
  production: {
    host: process.env.REDIS_HOST,
    port: parseInt(process.env.REDIS_PORT) || 6379,
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB) || 0,
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3,
    lazyConnect: true,
    keepAlive: 30000,
    connectTimeout: 10000,
    commandTimeout: 5000,
    tls: process.env.REDIS_TLS === 'true' ? {} : undefined,
  }
};

class RedisManager {
  constructor() {
    this.client = null;
    this.subscriber = null;
    this.publisher = null;
    this.environment = process.env.NODE_ENV || 'development';
    this.defaultTTL = parseInt(process.env.REDIS_TTL) || 3600; // 1 hour default
  }

  // Initialize Redis connections
  async initialize() {
    try {
      const config = redisConfig[this.environment];
      
      // Main Redis client
      this.client = new Redis(config);
      
      // Separate clients for pub/sub
      this.subscriber = new Redis(config);
      this.publisher = new Redis(config);

      // Event handlers
      this.setupEventHandlers();

      // Test connection
      await this.client.ping();
      console.log('✅ Redis connected successfully');

      return this.client;
    } catch (error) {
      console.error('❌ Redis connection failed:', error);
      throw error;
    }
  }

  // Setup event handlers
  setupEventHandlers() {
    this.client.on('connect', () => {
      console.log('🔗 Redis client connected');
    });

    this.client.on('ready', () => {
      console.log('✅ Redis client ready');
    });

    this.client.on('error', (error) => {
      console.error('❌ Redis client error:', error);
    });

    this.client.on('close', () => {
      console.log('🔌 Redis client connection closed');
    });

    this.client.on('reconnecting', () => {
      console.log('🔄 Redis client reconnecting...');
    });
  }

  // Get Redis client
  getClient() {
    if (!this.client) {
      throw new Error('Redis not initialized. Call initialize() first.');
    }
    return this.client;
  }

  // Get subscriber client
  getSubscriber() {
    if (!this.subscriber) {
      throw new Error('Redis not initialized. Call initialize() first.');
    }
    return this.subscriber;
  }

  // Get publisher client
  getPublisher() {
    if (!this.publisher) {
      throw new Error('Redis not initialized. Call initialize() first.');
    }
    return this.publisher;
  }

  // Cache operations
  async set(key, value, ttl = this.defaultTTL) {
    try {
      const serializedValue = JSON.stringify(value);
      if (ttl > 0) {
        return await this.client.setex(key, ttl, serializedValue);
      } else {
        return await this.client.set(key, serializedValue);
      }
    } catch (error) {
      console.error('❌ Redis SET error:', error);
      throw error;
    }
  }

  async get(key) {
    try {
      const value = await this.client.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('❌ Redis GET error:', error);
      throw error;
    }
  }

  async del(key) {
    try {
      return await this.client.del(key);
    } catch (error) {
      console.error('❌ Redis DEL error:', error);
      throw error;
    }
  }

  async exists(key) {
    try {
      return await this.client.exists(key);
    } catch (error) {
      console.error('❌ Redis EXISTS error:', error);
      throw error;
    }
  }

  async expire(key, ttl) {
    try {
      return await this.client.expire(key, ttl);
    } catch (error) {
      console.error('❌ Redis EXPIRE error:', error);
      throw error;
    }
  }

  // Hash operations
  async hset(key, field, value) {
    try {
      const serializedValue = JSON.stringify(value);
      return await this.client.hset(key, field, serializedValue);
    } catch (error) {
      console.error('❌ Redis HSET error:', error);
      throw error;
    }
  }

  async hget(key, field) {
    try {
      const value = await this.client.hget(key, field);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('❌ Redis HGET error:', error);
      throw error;
    }
  }

  async hgetall(key) {
    try {
      const hash = await this.client.hgetall(key);
      const result = {};
      for (const [field, value] of Object.entries(hash)) {
        result[field] = JSON.parse(value);
      }
      return result;
    } catch (error) {
      console.error('❌ Redis HGETALL error:', error);
      throw error;
    }
  }

  // List operations
  async lpush(key, ...values) {
    try {
      const serializedValues = values.map(v => JSON.stringify(v));
      return await this.client.lpush(key, ...serializedValues);
    } catch (error) {
      console.error('❌ Redis LPUSH error:', error);
      throw error;
    }
  }

  async rpop(key) {
    try {
      const value = await this.client.rpop(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('❌ Redis RPOP error:', error);
      throw error;
    }
  }

  // Rate limiting
  async incrementCounter(key, window = 900) { // 15 minutes default
    try {
      const multi = this.client.multi();
      multi.incr(key);
      multi.expire(key, window);
      const results = await multi.exec();
      return results[0][1]; // Return the incremented value
    } catch (error) {
      console.error('❌ Redis rate limiting error:', error);
      throw error;
    }
  }

  // Pub/Sub operations
  async publish(channel, message) {
    try {
      const serializedMessage = JSON.stringify(message);
      return await this.publisher.publish(channel, serializedMessage);
    } catch (error) {
      console.error('❌ Redis PUBLISH error:', error);
      throw error;
    }
  }

  async subscribe(channel, callback) {
    try {
      await this.subscriber.subscribe(channel);
      this.subscriber.on('message', (receivedChannel, message) => {
        if (receivedChannel === channel) {
          try {
            const parsedMessage = JSON.parse(message);
            callback(parsedMessage);
          } catch (parseError) {
            console.error('❌ Redis message parse error:', parseError);
          }
        }
      });
    } catch (error) {
      console.error('❌ Redis SUBSCRIBE error:', error);
      throw error;
    }
  }

  // Health check
  async healthCheck() {
    try {
      const start = Date.now();
      await this.client.ping();
      const latency = Date.now() - start;
      
      return {
        status: 'healthy',
        latency: `${latency}ms`,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  // Graceful shutdown
  async close() {
    try {
      if (this.client) {
        await this.client.quit();
        console.log('✅ Redis client connection closed');
      }
      
      if (this.subscriber) {
        await this.subscriber.quit();
        console.log('✅ Redis subscriber connection closed');
      }
      
      if (this.publisher) {
        await this.publisher.quit();
        console.log('✅ Redis publisher connection closed');
      }
    } catch (error) {
      console.error('❌ Error closing Redis connections:', error);
    }
  }
}

// Singleton instance
const redisManager = new RedisManager();

module.exports = {
  RedisManager,
  redisManager,
  redisConfig
};
