version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: cryptosentinel-postgres
    environment:
      POSTGRES_DB: cryptosentinel
      POSTGRES_USER: cryptosentinel
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD:-password}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - cryptosentinel-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U cryptosentinel"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: cryptosentinel-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - cryptosentinel-network
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API Service
  api:
    build:
      context: ../../
      dockerfile: deployment/docker/Dockerfile.api
    container_name: cryptosentinel-api
    environment:
      NODE_ENV: production
      PORT: 3001
      DATABASE_URL: postgresql://cryptosentinel:${DATABASE_PASSWORD:-password}@postgres:5432/cryptosentinel
      REDIS_URL: redis://redis:6379
      JWT_SECRET: ${JWT_SECRET}
      CORS_ORIGIN: ${CORS_ORIGIN:-http://localhost:3000}
    ports:
      - "3001:3001"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - cryptosentinel-network
    restart: unless-stopped
    volumes:
      - api_logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # AI Service
  ai-service:
    build:
      context: ../../
      dockerfile: deployment/docker/Dockerfile.ai
    container_name: cryptosentinel-ai
    environment:
      PORT: 8000
      REDIS_URL: redis://redis:6379
      DATABASE_URL: postgresql://cryptosentinel:${DATABASE_PASSWORD:-password}@postgres:5432/cryptosentinel
    ports:
      - "8000:8000"
    depends_on:
      redis:
        condition: service_healthy
      postgres:
        condition: service_healthy
    networks:
      - cryptosentinel-network
    restart: unless-stopped
    volumes:
      - ai_models:/app/models
      - ai_logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Blockchain Service
  blockchain-service:
    build:
      context: ../../
      dockerfile: deployment/docker/Dockerfile.blockchain
    container_name: cryptosentinel-blockchain
    environment:
      PORT: 3002
      REDIS_URL: redis://redis:6379
      ETHEREUM_RPC_URL: ${ETHEREUM_RPC_URL}
      BSC_RPC_URL: ${BSC_RPC_URL}
      POLYGON_RPC_URL: ${POLYGON_RPC_URL}
      SOLANA_RPC_URL: ${SOLANA_RPC_URL}
    ports:
      - "3002:3002"
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - cryptosentinel-network
    restart: unless-stopped
    volumes:
      - blockchain_logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Web Frontend (for production)
  web:
    build:
      context: ../../
      dockerfile: deployment/docker/Dockerfile.web
    container_name: cryptosentinel-web
    environment:
      NODE_ENV: production
      NEXT_PUBLIC_API_URL: http://api:3001
      NEXT_PUBLIC_AI_SERVICE_URL: http://ai-service:8000
      NEXT_PUBLIC_BLOCKCHAIN_SERVICE_URL: http://blockchain-service:3002
    ports:
      - "3000:3000"
    depends_on:
      - api
      - ai-service
      - blockchain-service
    networks:
      - cryptosentinel-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: cryptosentinel-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - web
      - api
      - ai-service
      - blockchain-service
    networks:
      - cryptosentinel-network
    restart: unless-stopped

  # Monitoring - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: cryptosentinel-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - cryptosentinel-network
    restart: unless-stopped

  # Monitoring - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: cryptosentinel-grafana
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - cryptosentinel-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  api_logs:
    driver: local
  ai_models:
    driver: local
  ai_logs:
    driver: local
  blockchain_logs:
    driver: local
  nginx_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  cryptosentinel-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
