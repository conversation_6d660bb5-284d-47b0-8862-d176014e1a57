[build]
  base = "apps/web"
  publish = "apps/web/out"
  command = "npm run build:netlify"

[build.environment]
  NODE_VERSION = "18"
  NPM_VERSION = "9"
  NEXT_TELEMETRY_DISABLED = "1"

# Redirect rules for SPA
[[redirects]]
  from = "/api/*"
  to = "https://api.cryptosentinel.ai/api/:splat"
  status = 200
  force = true

[[redirects]]
  from = "/ai/*"
  to = "https://ai.cryptosentinel.ai/:splat"
  status = 200
  force = true

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=()"

[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*.woff2"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Environment-specific settings
[context.production]
  command = "npm run build:netlify"
  [context.production.environment]
    NODE_ENV = "production"
    NEXT_PUBLIC_APP_URL = "https://cryptosentinel.ai"
    NEXT_PUBLIC_API_URL = "https://api.cryptosentinel.ai"
    NEXT_PUBLIC_AI_SERVICE_URL = "https://ai.cryptosentinel.ai"

[context.staging]
  command = "npm run build:netlify"
  [context.staging.environment]
    NODE_ENV = "staging"
    NEXT_PUBLIC_APP_URL = "https://staging.cryptosentinel.ai"
    NEXT_PUBLIC_API_URL = "https://api-staging.cryptosentinel.ai"
    NEXT_PUBLIC_AI_SERVICE_URL = "https://ai-staging.cryptosentinel.ai"

[context.deploy-preview]
  command = "npm run build:netlify"
  [context.deploy-preview.environment]
    NODE_ENV = "preview"

[context.branch-deploy]
  command = "npm run build:netlify"

# Functions (for serverless functions if needed)
[functions]
  directory = "netlify/functions"
  node_bundler = "esbuild"

# Edge functions
[edge_functions]
  directory = "netlify/edge-functions"

# Plugin configuration
[[plugins]]
  package = "@netlify/plugin-nextjs"

[[plugins]]
  package = "netlify-plugin-cache"
  [plugins.inputs]
    paths = [
      "node_modules",
      ".next/cache"
    ]

[[plugins]]
  package = "@netlify/plugin-lighthouse"
  [plugins.inputs]
    output_path = "reports/lighthouse.html"
    audit_url = "/"

# Form handling (if needed for contact forms)
[forms]
  settings = true

# Split testing (A/B testing)
[split_testing]
  enabled = true
