{"name": "tokenforge", "version": "1.0.0", "description": "AI-Powered Token Risk Assessment Platform", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "build:netlify": "turbo run build --filter=web", "build:aws": "turbo run build", "build:gcp": "turbo run build", "test": "turbo run test", "test:unit": "turbo run test:unit", "test:integration": "turbo run test:integration", "test:e2e": "turbo run test:e2e", "lint": "turbo run lint", "lint:fix": "turbo run lint:fix", "type-check": "turbo run type-check", "clean": "turbo run clean", "setup": "./scripts/setup.sh", "deploy:netlify": "./scripts/deploy.sh netlify", "deploy:aws": "./scripts/deploy.sh aws", "deploy:gcp": "./scripts/deploy.sh gcp", "docker:build": "docker-compose -f deployment/docker/docker-compose.yml build", "docker:up": "docker-compose -f deployment/docker/docker-compose.yml up", "docker:down": "docker-compose -f deployment/docker/docker-compose.yml down", "db:migrate": "cd apps/api && npm run db:migrate", "db:seed": "cd apps/api && npm run db:seed", "contracts:compile": "cd packages/contracts && npx hardhat compile", "contracts:deploy": "cd packages/contracts && npx hardhat run scripts/deploy.js", "contracts:verify": "cd packages/contracts && npx hardhat verify"}, "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "eslint-config-next": "^14.0.3", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "husky": "^8.0.3", "lint-staged": "^15.1.0", "prettier": "^3.1.0", "turbo": "^1.11.0", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/HectorTa1989/TokenForge_AI-Powered-Risk-Assessment.git"}, "author": "HectorTa1989", "license": "MIT", "keywords": ["cryptocurrency", "blockchain", "token-analysis", "risk-assessment", "ai", "machine-learning", "defi", "security", "scam-detection"], "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run test"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}}