{"name": "@tokenforge/shared", "version": "1.0.0", "description": "Shared utilities and constants for TokenForge", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "files": ["dist", "src"], "dependencies": {"ethers": "^6.8.1", "axios": "^1.6.2", "lodash": "^4.17.21", "date-fns": "^2.30.0", "crypto-js": "^4.2.0", "validator": "^13.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/lodash": "^4.14.202", "@types/crypto-js": "^4.2.1", "@types/validator": "^13.11.7", "@types/jest": "^29.5.8", "typescript": "^5.3.2", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "jest": "^29.7.0", "ts-jest": "^29.1.1"}, "keywords": ["typescript", "utilities", "cryptocurrency", "blockchain", "shared"], "author": "HectorTa1989", "license": "MIT"}