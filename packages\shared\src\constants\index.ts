/**
 * TokenForge Constants
 * Application-wide constants and configuration values
 */

// =============================================================================
// Application Constants
// =============================================================================

export const APP_NAME = 'TokenForge';
export const APP_VERSION = '1.0.0';
export const APP_DESCRIPTION = 'AI-Powered Token Risk Assessment Platform';
export const APP_URL = 'https://tokenforge.ai';
export const API_VERSION = 'v1';

// =============================================================================
// Blockchain Constants
// =============================================================================

export const SUPPORTED_CHAINS = {
  ethereum: {
    chainId: 1,
    name: 'Ethereum',
    symbol: 'ETH',
    decimals: 18,
    rpcUrl: 'https://mainnet.infura.io/v3/',
    explorerUrl: 'https://etherscan.io',
    isTestnet: false,
  },
  bsc: {
    chainId: 56,
    name: 'Binance Smart Chain',
    symbol: 'BNB',
    decimals: 18,
    rpcUrl: 'https://bsc-dataseed.binance.org/',
    explorerUrl: 'https://bscscan.com',
    isTestnet: false,
  },
  polygon: {
    chainId: 137,
    name: 'Polygon',
    symbol: 'MATIC',
    decimals: 18,
    rpcUrl: 'https://polygon-rpc.com/',
    explorerUrl: 'https://polygonscan.com',
    isTestnet: false,
  },
  arbitrum: {
    chainId: 42161,
    name: 'Arbitrum One',
    symbol: 'ETH',
    decimals: 18,
    rpcUrl: 'https://arb1.arbitrum.io/rpc',
    explorerUrl: 'https://arbiscan.io',
    isTestnet: false,
  },
  optimism: {
    chainId: 10,
    name: 'Optimism',
    symbol: 'ETH',
    decimals: 18,
    rpcUrl: 'https://mainnet.optimism.io',
    explorerUrl: 'https://optimistic.etherscan.io',
    isTestnet: false,
  },
  avalanche: {
    chainId: 43114,
    name: 'Avalanche',
    symbol: 'AVAX',
    decimals: 18,
    rpcUrl: 'https://api.avax.network/ext/bc/C/rpc',
    explorerUrl: 'https://snowtrace.io',
    isTestnet: false,
  },
  fantom: {
    chainId: 250,
    name: 'Fantom',
    symbol: 'FTM',
    decimals: 18,
    rpcUrl: 'https://rpc.ftm.tools/',
    explorerUrl: 'https://ftmscan.com',
    isTestnet: false,
  },
} as const;

export const TESTNET_CHAINS = {
  goerli: {
    chainId: 5,
    name: 'Goerli Testnet',
    symbol: 'ETH',
    decimals: 18,
    rpcUrl: 'https://goerli.infura.io/v3/',
    explorerUrl: 'https://goerli.etherscan.io',
    isTestnet: true,
  },
  bscTestnet: {
    chainId: 97,
    name: 'BSC Testnet',
    symbol: 'tBNB',
    decimals: 18,
    rpcUrl: 'https://data-seed-prebsc-1-s1.binance.org:8545/',
    explorerUrl: 'https://testnet.bscscan.com',
    isTestnet: true,
  },
  mumbai: {
    chainId: 80001,
    name: 'Polygon Mumbai',
    symbol: 'MATIC',
    decimals: 18,
    rpcUrl: 'https://rpc-mumbai.maticvigil.com/',
    explorerUrl: 'https://mumbai.polygonscan.com',
    isTestnet: true,
  },
} as const;

// =============================================================================
// Risk Assessment Constants
// =============================================================================

export const RISK_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical',
} as const;

export const RISK_SCORES = {
  LOW: { min: 0, max: 25 },
  MEDIUM: { min: 26, max: 50 },
  HIGH: { min: 51, max: 75 },
  CRITICAL: { min: 76, max: 100 },
} as const;

export const RISK_CATEGORIES = {
  CONTRACT: 'contract',
  SOCIAL: 'social',
  DEVELOPER: 'developer',
  LIQUIDITY: 'liquidity',
  MARKET: 'market',
} as const;

export const RISK_FACTORS = {
  // Contract Risk Factors
  HONEYPOT: 'honeypot',
  MINT_FUNCTION: 'mint_function',
  OWNERSHIP_NOT_RENOUNCED: 'ownership_not_renounced',
  PROXY_CONTRACT: 'proxy_contract',
  PAUSE_FUNCTION: 'pause_function',
  BLACKLIST_FUNCTION: 'blacklist_function',
  HIGH_TAX: 'high_tax',
  MAX_TRANSACTION_LIMIT: 'max_transaction_limit',
  
  // Social Risk Factors
  LOW_SOCIAL_ACTIVITY: 'low_social_activity',
  NEGATIVE_SENTIMENT: 'negative_sentiment',
  FAKE_FOLLOWERS: 'fake_followers',
  PUMP_AND_DUMP_SIGNALS: 'pump_and_dump_signals',
  
  // Developer Risk Factors
  ANONYMOUS_TEAM: 'anonymous_team',
  LOW_GITHUB_ACTIVITY: 'low_github_activity',
  COPY_PASTE_CODE: 'copy_paste_code',
  NO_AUDIT: 'no_audit',
  
  // Liquidity Risk Factors
  LOW_LIQUIDITY: 'low_liquidity',
  CONCENTRATED_OWNERSHIP: 'concentrated_ownership',
  LOCKED_LIQUIDITY: 'locked_liquidity',
  
  // Market Risk Factors
  HIGH_VOLATILITY: 'high_volatility',
  LOW_VOLUME: 'low_volume',
  PRICE_MANIPULATION: 'price_manipulation',
} as const;

// =============================================================================
// API Constants
// =============================================================================

export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
} as const;

export const API_ERRORS = {
  INVALID_TOKEN_ADDRESS: 'INVALID_TOKEN_ADDRESS',
  UNSUPPORTED_CHAIN: 'UNSUPPORTED_CHAIN',
  TOKEN_NOT_FOUND: 'TOKEN_NOT_FOUND',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  USER_NOT_FOUND: 'USER_NOT_FOUND',
  EMAIL_ALREADY_EXISTS: 'EMAIL_ALREADY_EXISTS',
  USERNAME_ALREADY_EXISTS: 'USERNAME_ALREADY_EXISTS',
  PORTFOLIO_NOT_FOUND: 'PORTFOLIO_NOT_FOUND',
  ALERT_NOT_FOUND: 'ALERT_NOT_FOUND',
  SUBSCRIPTION_REQUIRED: 'SUBSCRIPTION_REQUIRED',
  ANALYSIS_IN_PROGRESS: 'ANALYSIS_IN_PROGRESS',
  EXTERNAL_API_ERROR: 'EXTERNAL_API_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
} as const;

// =============================================================================
// Rate Limiting Constants
// =============================================================================

export const RATE_LIMITS = {
  FREE_TIER: {
    requests: 100,
    window: 15 * 60 * 1000, // 15 minutes
  },
  PRO_TIER: {
    requests: 1000,
    window: 15 * 60 * 1000, // 15 minutes
  },
  ENTERPRISE_TIER: {
    requests: 10000,
    window: 15 * 60 * 1000, // 15 minutes
  },
} as const;

// =============================================================================
// Cache Constants
// =============================================================================

export const CACHE_KEYS = {
  TOKEN_ANALYSIS: 'token_analysis',
  TOKEN_PRICE: 'token_price',
  TOKEN_METRICS: 'token_metrics',
  RISK_ASSESSMENT: 'risk_assessment',
  CONTRACT_ANALYSIS: 'contract_analysis',
  SOCIAL_ANALYSIS: 'social_analysis',
  DEVELOPER_ANALYSIS: 'developer_analysis',
  USER_PROFILE: 'user_profile',
  PORTFOLIO: 'portfolio',
  BLOCKCHAIN_DATA: 'blockchain_data',
} as const;

export const CACHE_TTL = {
  SHORT: 5 * 60, // 5 minutes
  MEDIUM: 15 * 60, // 15 minutes
  LONG: 60 * 60, // 1 hour
  VERY_LONG: 24 * 60 * 60, // 24 hours
} as const;

// =============================================================================
// Validation Constants
// =============================================================================

export const VALIDATION_RULES = {
  EMAIL: {
    minLength: 5,
    maxLength: 254,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  },
  USERNAME: {
    minLength: 3,
    maxLength: 30,
    pattern: /^[a-zA-Z0-9_-]+$/,
  },
  PASSWORD: {
    minLength: 8,
    maxLength: 128,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
  },
  TOKEN_ADDRESS: {
    ethereum: /^0x[a-fA-F0-9]{40}$/,
    solana: /^[1-9A-HJ-NP-Za-km-z]{32,44}$/,
  },
  PORTFOLIO_NAME: {
    minLength: 1,
    maxLength: 100,
  },
  ALERT_NAME: {
    minLength: 1,
    maxLength: 100,
  },
} as const;

// =============================================================================
// Subscription Constants
// =============================================================================

export const SUBSCRIPTION_PLANS = {
  FREE: {
    id: 'free',
    name: 'Free',
    price: 0,
    currency: 'USD',
    interval: 'month',
    features: [
      'Basic token analysis',
      'Risk assessment',
      '100 API requests/month',
      '1 portfolio',
      '5 alerts',
    ],
    limits: {
      apiRequests: 100,
      portfolios: 1,
      alerts: 5,
      apiAccess: false,
      prioritySupport: false,
    },
  },
  PRO: {
    id: 'pro',
    name: 'Pro',
    price: 29,
    currency: 'USD',
    interval: 'month',
    features: [
      'Advanced token analysis',
      'Social sentiment analysis',
      'Developer reputation tracking',
      '10,000 API requests/month',
      'Unlimited portfolios',
      '100 alerts',
      'API access',
      'Priority support',
    ],
    limits: {
      apiRequests: 10000,
      portfolios: -1, // unlimited
      alerts: 100,
      apiAccess: true,
      prioritySupport: true,
    },
  },
  ENTERPRISE: {
    id: 'enterprise',
    name: 'Enterprise',
    price: 299,
    currency: 'USD',
    interval: 'month',
    features: [
      'All Pro features',
      'Custom risk models',
      'Bulk analysis',
      '100,000 API requests/month',
      'White-label solution',
      'Dedicated support',
      'Custom integrations',
    ],
    limits: {
      apiRequests: 100000,
      portfolios: -1, // unlimited
      alerts: -1, // unlimited
      apiAccess: true,
      prioritySupport: true,
    },
  },
} as const;

// =============================================================================
// Notification Constants
// =============================================================================

export const NOTIFICATION_TYPES = {
  RISK_CHANGE: 'risk_change',
  PRICE_ALERT: 'price_alert',
  PORTFOLIO_UPDATE: 'portfolio_update',
  SECURITY_ALERT: 'security_alert',
  SYSTEM_MAINTENANCE: 'system_maintenance',
  WELCOME: 'welcome',
  EMAIL_VERIFICATION: 'email_verification',
  PASSWORD_RESET: 'password_reset',
  SUBSCRIPTION_UPDATE: 'subscription_update',
} as const;

// =============================================================================
// File Upload Constants
// =============================================================================

export const FILE_UPLOAD = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
} as const;

// =============================================================================
// Regex Patterns
// =============================================================================

export const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  USERNAME: /^[a-zA-Z0-9_-]{3,30}$/,
  ETHEREUM_ADDRESS: /^0x[a-fA-F0-9]{40}$/,
  SOLANA_ADDRESS: /^[1-9A-HJ-NP-Za-km-z]{32,44}$/,
  TRANSACTION_HASH: /^0x[a-fA-F0-9]{64}$/,
  URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  TWITTER_HANDLE: /^@?[a-zA-Z0-9_]{1,15}$/,
  GITHUB_USERNAME: /^[a-zA-Z0-9](?:[a-zA-Z0-9]|-(?=[a-zA-Z0-9])){0,38}$/,
} as const;

// =============================================================================
// Default Values
// =============================================================================

export const DEFAULT_VALUES = {
  PAGINATION: {
    page: 1,
    limit: 20,
    maxLimit: 100,
  },
  ANALYSIS: {
    timeout: 30000, // 30 seconds
    retries: 3,
    confidence: 0.8,
  },
  CACHE: {
    ttl: CACHE_TTL.MEDIUM,
  },
  RATE_LIMIT: {
    window: 15 * 60 * 1000, // 15 minutes
    max: 100,
  },
} as const;
