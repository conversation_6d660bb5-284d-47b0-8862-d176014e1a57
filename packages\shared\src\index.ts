/**
 * TokenForge Shared Package
 * Main entry point for shared utilities, constants, and functions
 */

// Export all constants
export * from './constants';

// Export all utilities
export * from './utils';

// Export validators
export * from './validators';

// Re-export commonly used functions for convenience
export {
  // Address utilities
  isValidAddress,
  formatAddress,
  normalizeAddress,
  validateAndNormalizeAddress,
  getExplorerUrl,
  
  // Crypto utilities
  generateSecureRandom,
  generateUUID,
  hashPassword,
  verifyPassword,
  encrypt,
  decrypt,
  generateApiKey,
  
  // Validation utilities
  isValidEmail,
  isValidUsername,
  isValidPassword,
  isValidUrl,
  validatePagination,
  validateSort,
  
  // Formatting utilities
  formatNumber,
  formatCurrency,
  formatPercentage,
  formatLargeNumber,
  formatDate,
  formatRelativeTime,
  formatRiskScore,
  formatRiskLevel,
  
  // API utilities
  createApiResponse,
  createSuccessResponse,
  createErrorResponse,
  createPaginatedResponse,
  handleApiError,
  createHttpClient,
  
  // Logger
  logger,
  Logger,
  LogLevel
} from './utils';

// Export types for convenience
export type {
  LogEntry,
  LoggerConfig
} from './utils/logger';
