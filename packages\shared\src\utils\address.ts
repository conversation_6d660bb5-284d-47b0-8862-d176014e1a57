/**
 * Address Utilities
 * Functions for validating and formatting blockchain addresses
 */

import { REGEX_PATTERNS } from '../constants';

/**
 * Validate Ethereum address format
 */
export function isValidEthereumAddress(address: string): boolean {
  return REGEX_PATTERNS.ETHEREUM_ADDRESS.test(address);
}

/**
 * Validate Solana address format
 */
export function isValidSolanaAddress(address: string): boolean {
  return REGEX_PATTERNS.SOLANA_ADDRESS.test(address);
}

/**
 * Validate address based on chain
 */
export function isValidAddress(address: string, chain: string): boolean {
  switch (chain.toLowerCase()) {
    case 'ethereum':
    case 'bsc':
    case 'polygon':
    case 'arbitrum':
    case 'optimism':
    case 'avalanche':
    case 'fantom':
      return isValidEthereumAddress(address);
    case 'solana':
      return isValidSolanaAddress(address);
    default:
      return false;
  }
}

/**
 * Format address for display (truncate middle)
 */
export function formatAddress(address: string, startChars = 6, endChars = 4): string {
  if (!address) return '';
  if (address.length <= startChars + endChars) return address;
  
  return `${address.slice(0, startChars)}...${address.slice(-endChars)}`;
}

/**
 * Normalize address to lowercase
 */
export function normalizeAddress(address: string): string {
  return address.toLowerCase().trim();
}

/**
 * Check if address is zero address
 */
export function isZeroAddress(address: string): boolean {
  const normalized = normalizeAddress(address);
  return normalized === '******************************************' || normalized === '0x0';
}

/**
 * Generate checksum address (Ethereum)
 */
export function toChecksumAddress(address: string): string {
  if (!isValidEthereumAddress(address)) {
    throw new Error('Invalid Ethereum address');
  }
  
  const addr = address.toLowerCase().replace('0x', '');
  const hash = require('crypto').createHash('keccak256').update(addr).digest('hex');
  
  let checksumAddress = '0x';
  for (let i = 0; i < addr.length; i++) {
    if (parseInt(hash[i], 16) >= 8) {
      checksumAddress += addr[i].toUpperCase();
    } else {
      checksumAddress += addr[i];
    }
  }
  
  return checksumAddress;
}

/**
 * Compare addresses (case-insensitive)
 */
export function addressesEqual(addr1: string, addr2: string): boolean {
  return normalizeAddress(addr1) === normalizeAddress(addr2);
}

/**
 * Extract addresses from text
 */
export function extractAddresses(text: string, chain = 'ethereum'): string[] {
  const pattern = chain === 'solana' ? REGEX_PATTERNS.SOLANA_ADDRESS : REGEX_PATTERNS.ETHEREUM_ADDRESS;
  const matches = text.match(new RegExp(pattern.source, 'g'));
  return matches || [];
}

/**
 * Validate and normalize address
 */
export function validateAndNormalizeAddress(address: string, chain: string): string {
  if (!address) {
    throw new Error('Address is required');
  }
  
  const normalized = normalizeAddress(address);
  
  if (!isValidAddress(normalized, chain)) {
    throw new Error(`Invalid ${chain} address format`);
  }
  
  if (isZeroAddress(normalized)) {
    throw new Error('Zero address is not allowed');
  }
  
  return normalized;
}

/**
 * Get address type (contract, EOA, etc.)
 */
export function getAddressType(address: string): 'contract' | 'eoa' | 'unknown' {
  // This would typically require blockchain interaction
  // For now, return unknown as placeholder
  return 'unknown';
}

/**
 * Generate address from public key (simplified)
 */
export function addressFromPublicKey(publicKey: string, chain = 'ethereum'): string {
  // This is a simplified implementation
  // In practice, you'd use proper cryptographic libraries
  if (chain === 'ethereum') {
    const hash = require('crypto').createHash('keccak256').update(publicKey).digest('hex');
    return '0x' + hash.slice(-40);
  }
  
  throw new Error(`Address generation not implemented for ${chain}`);
}

/**
 * Check if address is likely a contract
 */
export function isLikelyContract(address: string): boolean {
  // Simple heuristic: contracts often have more varied byte patterns
  const normalized = normalizeAddress(address).replace('0x', '');
  const uniqueChars = new Set(normalized).size;
  return uniqueChars > 8; // Arbitrary threshold
}

/**
 * Get explorer URL for address
 */
export function getExplorerUrl(address: string, chain: string): string {
  const explorers: Record<string, string> = {
    ethereum: 'https://etherscan.io/address/',
    bsc: 'https://bscscan.com/address/',
    polygon: 'https://polygonscan.com/address/',
    arbitrum: 'https://arbiscan.io/address/',
    optimism: 'https://optimistic.etherscan.io/address/',
    avalanche: 'https://snowtrace.io/address/',
    fantom: 'https://ftmscan.com/address/',
    solana: 'https://solscan.io/account/',
  };
  
  const baseUrl = explorers[chain.toLowerCase()];
  if (!baseUrl) {
    throw new Error(`Explorer not configured for chain: ${chain}`);
  }
  
  return baseUrl + address;
}

/**
 * Batch validate addresses
 */
export function validateAddresses(addresses: string[], chain: string): {
  valid: string[];
  invalid: Array<{ address: string; error: string }>;
} {
  const valid: string[] = [];
  const invalid: Array<{ address: string; error: string }> = [];
  
  for (const address of addresses) {
    try {
      const normalized = validateAndNormalizeAddress(address, chain);
      valid.push(normalized);
    } catch (error) {
      invalid.push({
        address,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
  
  return { valid, invalid };
}

/**
 * Generate random address (for testing)
 */
export function generateRandomAddress(chain = 'ethereum'): string {
  if (chain === 'ethereum') {
    const bytes = require('crypto').randomBytes(20);
    return '0x' + bytes.toString('hex');
  }
  
  if (chain === 'solana') {
    const chars = '**********************************************************';
    let result = '';
    for (let i = 0; i < 44; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
  
  throw new Error(`Random address generation not implemented for ${chain}`);
}
