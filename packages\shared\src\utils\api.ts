/**
 * API Utilities
 * Functions for handling API requests, responses, and errors
 */

import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import { HTTP_STATUS, API_ERRORS } from '../constants';

/**
 * Create standardized API response
 */
export function createApiResponse<T>(
  success: boolean,
  data?: T,
  message?: string,
  error?: string
) {
  return {
    success,
    data,
    message,
    error,
    timestamp: Date.now()
  };
}

/**
 * Create success response
 */
export function createSuccessResponse<T>(data: T, message?: string) {
  return createApiResponse(true, data, message);
}

/**
 * Create error response
 */
export function createErrorResponse(error: string, message?: string) {
  return createApiResponse(false, undefined, message, error);
}

/**
 * Create paginated response
 */
export function createPaginatedResponse<T>(
  data: T[],
  page: number,
  limit: number,
  total: number,
  message?: string
) {
  const pages = Math.ceil(total / limit);
  
  return {
    success: true,
    data,
    message,
    timestamp: Date.now(),
    pagination: {
      page,
      limit,
      total,
      pages,
      hasNext: page < pages,
      hasPrev: page > 1
    }
  };
}

/**
 * Handle API errors
 */
export function handleApiError(error: any): {
  status: number;
  code: string;
  message: string;
  details?: any;
} {
  // Axios error
  if (error.isAxiosError) {
    const axiosError = error as AxiosError;
    
    if (axiosError.response) {
      return {
        status: axiosError.response.status,
        code: API_ERRORS.EXTERNAL_API_ERROR,
        message: axiosError.response.statusText || 'External API error',
        details: axiosError.response.data
      };
    }
    
    if (axiosError.request) {
      return {
        status: HTTP_STATUS.SERVICE_UNAVAILABLE,
        code: API_ERRORS.EXTERNAL_API_ERROR,
        message: 'Network error - unable to reach external service'
      };
    }
  }
  
  // Validation error
  if (error.name === 'ValidationError') {
    return {
      status: HTTP_STATUS.BAD_REQUEST,
      code: API_ERRORS.VALIDATION_ERROR,
      message: error.message,
      details: error.details
    };
  }
  
  // Database error
  if (error.name === 'DatabaseError' || error.code?.startsWith('P')) {
    return {
      status: HTTP_STATUS.INTERNAL_SERVER_ERROR,
      code: API_ERRORS.DATABASE_ERROR,
      message: 'Database operation failed'
    };
  }
  
  // Rate limit error
  if (error.name === 'RateLimitError') {
    return {
      status: HTTP_STATUS.TOO_MANY_REQUESTS,
      code: API_ERRORS.RATE_LIMIT_EXCEEDED,
      message: 'Rate limit exceeded',
      details: {
        retryAfter: error.retryAfter,
        limit: error.limit,
        remaining: error.remaining
      }
    };
  }
  
  // Generic error
  return {
    status: HTTP_STATUS.INTERNAL_SERVER_ERROR,
    code: 'INTERNAL_ERROR',
    message: error.message || 'An unexpected error occurred'
  };
}

/**
 * Create HTTP client with default configuration
 */
export function createHttpClient(baseURL: string, timeout = 10000) {
  const client = axios.create({
    baseURL,
    timeout,
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'TokenForge/1.0.0'
    }
  });
  
  // Request interceptor
  client.interceptors.request.use(
    (config) => {
      // Add timestamp to prevent caching
      if (config.method === 'get') {
        config.params = {
          ...config.params,
          _t: Date.now()
        };
      }
      
      return config;
    },
    (error) => Promise.reject(error)
  );
  
  // Response interceptor
  client.interceptors.response.use(
    (response) => response,
    (error) => {
      const handledError = handleApiError(error);
      return Promise.reject(handledError);
    }
  );
  
  return client;
}

/**
 * Retry failed requests with exponential backoff
 */
export async function retryRequest<T>(
  requestFn: () => Promise<T>,
  maxRetries = 3,
  baseDelay = 1000
): Promise<T> {
  let lastError: any;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await requestFn();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        break;
      }
      
      // Don't retry on client errors (4xx)
      if (error.status >= 400 && error.status < 500) {
        break;
      }
      
      // Exponential backoff with jitter
      const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError;
}

/**
 * Validate API key format
 */
export function isValidApiKey(apiKey: string): boolean {
  if (!apiKey || typeof apiKey !== 'string') return false;
  
  // TokenForge API keys start with 'tf_' followed by 48 hex characters
  return /^tf_[a-fA-F0-9]{48}$/.test(apiKey);
}

/**
 * Extract API key from request headers
 */
export function extractApiKey(headers: Record<string, string>): string | null {
  const authHeader = headers.authorization || headers.Authorization;
  
  if (authHeader && authHeader.startsWith('Bearer ')) {
    const token = authHeader.substring(7);
    return isValidApiKey(token) ? token : null;
  }
  
  const apiKeyHeader = headers['x-api-key'] || headers['X-API-Key'];
  if (apiKeyHeader && isValidApiKey(apiKeyHeader)) {
    return apiKeyHeader;
  }
  
  return null;
}

/**
 * Create rate limit headers
 */
export function createRateLimitHeaders(
  limit: number,
  remaining: number,
  resetTime: number
): Record<string, string> {
  return {
    'X-RateLimit-Limit': limit.toString(),
    'X-RateLimit-Remaining': remaining.toString(),
    'X-RateLimit-Reset': resetTime.toString(),
    'X-RateLimit-Reset-After': Math.ceil((resetTime - Date.now()) / 1000).toString()
  };
}

/**
 * Parse query parameters
 */
export function parseQueryParams(query: Record<string, any>): {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filters: Record<string, any>;
} {
  const page = Math.max(1, parseInt(query.page) || 1);
  const limit = Math.min(100, Math.max(1, parseInt(query.limit) || 20));
  const sortBy = query.sortBy;
  const sortOrder = query.sortOrder === 'asc' ? 'asc' : 'desc';
  
  // Extract filters (exclude pagination and sorting params)
  const filters: Record<string, any> = {};
  const excludeKeys = ['page', 'limit', 'sortBy', 'sortOrder'];
  
  for (const [key, value] of Object.entries(query)) {
    if (!excludeKeys.includes(key) && value !== undefined && value !== '') {
      filters[key] = value;
    }
  }
  
  return { page, limit, sortBy, sortOrder, filters };
}

/**
 * Validate request body against schema
 */
export function validateRequestBody<T>(
  body: any,
  requiredFields: string[],
  optionalFields: string[] = []
): { isValid: boolean; data?: T; errors: string[] } {
  const errors: string[] = [];
  
  if (!body || typeof body !== 'object') {
    return { isValid: false, errors: ['Request body is required'] };
  }
  
  // Check required fields
  for (const field of requiredFields) {
    if (!(field in body) || body[field] === undefined || body[field] === null) {
      errors.push(`Field '${field}' is required`);
    }
  }
  
  // Check for unknown fields
  const allowedFields = [...requiredFields, ...optionalFields];
  for (const field of Object.keys(body)) {
    if (!allowedFields.includes(field)) {
      errors.push(`Unknown field '${field}'`);
    }
  }
  
  return {
    isValid: errors.length === 0,
    data: errors.length === 0 ? body as T : undefined,
    errors
  };
}

/**
 * Create CORS headers
 */
export function createCorsHeaders(origin?: string): Record<string, string> {
  const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'];
  const allowOrigin = origin && allowedOrigins.includes(origin) ? origin : allowedOrigins[0];
  
  return {
    'Access-Control-Allow-Origin': allowOrigin,
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    'Access-Control-Allow-Credentials': 'true',
    'Access-Control-Max-Age': '86400'
  };
}

/**
 * Log API request/response
 */
export function logApiCall(
  method: string,
  url: string,
  statusCode: number,
  responseTime: number,
  userId?: string,
  error?: string
) {
  const logData = {
    timestamp: new Date().toISOString(),
    method: method.toUpperCase(),
    url,
    statusCode,
    responseTime: `${responseTime}ms`,
    userId,
    error
  };
  
  if (error) {
    console.error('API Error:', logData);
  } else {
    console.log('API Call:', logData);
  }
}

/**
 * Create webhook payload
 */
export function createWebhookPayload(
  event: string,
  data: any,
  userId?: string
): {
  id: string;
  event: string;
  data: any;
  userId?: string;
  timestamp: number;
  signature?: string;
} {
  return {
    id: require('crypto').randomUUID(),
    event,
    data,
    userId,
    timestamp: Date.now()
  };
}

/**
 * Verify webhook signature
 */
export function verifyWebhookSignature(
  payload: string,
  signature: string,
  secret: string
): boolean {
  const expectedSignature = require('crypto')
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');
  
  return signature === `sha256=${expectedSignature}`;
}
