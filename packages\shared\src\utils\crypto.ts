/**
 * Cryptographic Utilities
 * Functions for encryption, hashing, and security operations
 */

import CryptoJS from 'crypto-js';
import { randomBytes, createHash, createHmac } from 'crypto';

/**
 * Generate secure random string
 */
export function generateSecureRandom(length = 32): string {
  return randomBytes(length).toString('hex');
}

/**
 * Generate UUID v4
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Hash password using bcrypt-like approach
 */
export function hashPassword(password: string, saltRounds = 12): string {
  const salt = generateSecureRandom(16);
  const hash = createHash('sha256')
    .update(password + salt)
    .digest('hex');
  
  return `$2b$${saltRounds}$${salt}$${hash}`;
}

/**
 * Verify password against hash
 */
export function verifyPassword(password: string, hash: string): boolean {
  try {
    const parts = hash.split('$');
    if (parts.length !== 4 || parts[0] !== '' || parts[1] !== '2b') {
      return false;
    }
    
    const salt = parts[3].substring(0, 32);
    const expectedHash = parts[3].substring(32);
    
    const actualHash = createHash('sha256')
      .update(password + salt)
      .digest('hex');
    
    return actualHash === expectedHash;
  } catch {
    return false;
  }
}

/**
 * Encrypt data using AES
 */
export function encrypt(data: string, key: string): string {
  const encrypted = CryptoJS.AES.encrypt(data, key).toString();
  return encrypted;
}

/**
 * Decrypt data using AES
 */
export function decrypt(encryptedData: string, key: string): string {
  const decrypted = CryptoJS.AES.decrypt(encryptedData, key);
  return decrypted.toString(CryptoJS.enc.Utf8);
}

/**
 * Create HMAC signature
 */
export function createSignature(data: string, secret: string, algorithm = 'sha256'): string {
  return createHmac(algorithm, secret)
    .update(data)
    .digest('hex');
}

/**
 * Verify HMAC signature
 */
export function verifySignature(data: string, signature: string, secret: string, algorithm = 'sha256'): boolean {
  const expectedSignature = createSignature(data, secret, algorithm);
  return signature === expectedSignature;
}

/**
 * Hash data using SHA256
 */
export function sha256(data: string): string {
  return createHash('sha256').update(data).digest('hex');
}

/**
 * Hash data using SHA512
 */
export function sha512(data: string): string {
  return createHash('sha512').update(data).digest('hex');
}

/**
 * Generate API key
 */
export function generateApiKey(prefix = 'tf'): string {
  const randomPart = generateSecureRandom(24);
  return `${prefix}_${randomPart}`;
}

/**
 * Generate JWT secret
 */
export function generateJWTSecret(): string {
  return generateSecureRandom(64);
}

/**
 * Create secure token for email verification, password reset, etc.
 */
export function generateSecureToken(): string {
  return generateSecureRandom(32);
}

/**
 * Mask sensitive data
 */
export function maskSensitiveData(data: string, visibleChars = 4): string {
  if (data.length <= visibleChars * 2) {
    return '*'.repeat(data.length);
  }
  
  const start = data.substring(0, visibleChars);
  const end = data.substring(data.length - visibleChars);
  const middle = '*'.repeat(data.length - visibleChars * 2);
  
  return start + middle + end;
}

/**
 * Generate checksum for data integrity
 */
export function generateChecksum(data: string): string {
  return sha256(data).substring(0, 8);
}

/**
 * Verify data integrity using checksum
 */
export function verifyChecksum(data: string, checksum: string): boolean {
  const expectedChecksum = generateChecksum(data);
  return expectedChecksum === checksum;
}

/**
 * Encode data to Base64
 */
export function encodeBase64(data: string): string {
  return Buffer.from(data, 'utf8').toString('base64');
}

/**
 * Decode data from Base64
 */
export function decodeBase64(encodedData: string): string {
  return Buffer.from(encodedData, 'base64').toString('utf8');
}

/**
 * Generate nonce for cryptographic operations
 */
export function generateNonce(): string {
  return Date.now().toString() + generateSecureRandom(8);
}

/**
 * Create deterministic hash from multiple inputs
 */
export function createDeterministicHash(...inputs: string[]): string {
  const combined = inputs.sort().join('|');
  return sha256(combined);
}

/**
 * Generate session ID
 */
export function generateSessionId(): string {
  return generateUUID().replace(/-/g, '');
}

/**
 * Create rate limiting key
 */
export function createRateLimitKey(identifier: string, endpoint: string): string {
  return `ratelimit:${sha256(identifier + endpoint).substring(0, 16)}`;
}

/**
 * Sanitize input to prevent injection attacks
 */
export function sanitizeInput(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/['"]/g, '') // Remove quotes
    .replace(/[;]/g, '') // Remove semicolons
    .trim();
}

/**
 * Generate secure filename
 */
export function generateSecureFilename(originalName: string): string {
  const extension = originalName.split('.').pop() || '';
  const timestamp = Date.now();
  const random = generateSecureRandom(8);
  return `${timestamp}_${random}.${extension}`;
}

/**
 * Create audit trail hash
 */
export function createAuditHash(userId: string, action: string, resource: string, timestamp: number): string {
  const data = `${userId}|${action}|${resource}|${timestamp}`;
  return sha256(data);
}

/**
 * Generate OTP (One-Time Password)
 */
export function generateOTP(length = 6): string {
  const digits = '0123456789';
  let otp = '';
  
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * digits.length);
    otp += digits[randomIndex];
  }
  
  return otp;
}

/**
 * Create time-based hash (expires after certain time)
 */
export function createTimeBasedHash(data: string, expirationMinutes = 60): string {
  const expirationTime = Date.now() + (expirationMinutes * 60 * 1000);
  const payload = `${data}|${expirationTime}`;
  return encodeBase64(payload);
}

/**
 * Verify time-based hash
 */
export function verifyTimeBasedHash(hash: string, expectedData: string): boolean {
  try {
    const payload = decodeBase64(hash);
    const [data, expirationTime] = payload.split('|');
    
    if (data !== expectedData) {
      return false;
    }
    
    const now = Date.now();
    const expiration = parseInt(expirationTime, 10);
    
    return now < expiration;
  } catch {
    return false;
  }
}
