/**
 * Formatting Utilities
 * Functions for formatting numbers, dates, and other data for display
 */

import { format, formatDistanceToNow, isValid, parseISO } from 'date-fns';

/**
 * Format number with commas
 */
export function formatNumber(num: number, decimals = 2): string {
  if (isNaN(num)) return '0';
  
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(num);
}

/**
 * Format currency
 */
export function formatCurrency(amount: number, currency = 'USD', decimals = 2): string {
  if (isNaN(amount)) return '$0.00';
  
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(amount);
}

/**
 * Format percentage
 */
export function formatPercentage(value: number, decimals = 2): string {
  if (isNaN(value)) return '0%';
  
  return new Intl.NumberFormat('en-US', {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(value / 100);
}

/**
 * Format large numbers with suffixes (K, M, B, T)
 */
export function formatLargeNumber(num: number, decimals = 1): string {
  if (isNaN(num)) return '0';
  
  const absNum = Math.abs(num);
  const sign = num < 0 ? '-' : '';
  
  if (absNum >= 1e12) {
    return sign + (absNum / 1e12).toFixed(decimals) + 'T';
  } else if (absNum >= 1e9) {
    return sign + (absNum / 1e9).toFixed(decimals) + 'B';
  } else if (absNum >= 1e6) {
    return sign + (absNum / 1e6).toFixed(decimals) + 'M';
  } else if (absNum >= 1e3) {
    return sign + (absNum / 1e3).toFixed(decimals) + 'K';
  } else {
    return sign + absNum.toFixed(decimals);
  }
}

/**
 * Format token amount with proper decimals
 */
export function formatTokenAmount(amount: string | number, decimals = 18, displayDecimals = 4): string {
  const num = typeof amount === 'string' ? parseFloat(amount) : amount;
  if (isNaN(num)) return '0';
  
  const divisor = Math.pow(10, decimals);
  const formatted = num / divisor;
  
  if (formatted === 0) return '0';
  if (formatted < 0.0001) return '< 0.0001';
  
  return formatNumber(formatted, displayDecimals);
}

/**
 * Format date
 */
export function formatDate(date: Date | string | number, formatString = 'MMM dd, yyyy'): string {
  let dateObj: Date;
  
  if (typeof date === 'string') {
    dateObj = parseISO(date);
  } else if (typeof date === 'number') {
    dateObj = new Date(date);
  } else {
    dateObj = date;
  }
  
  if (!isValid(dateObj)) return 'Invalid date';
  
  return format(dateObj, formatString);
}

/**
 * Format relative time (e.g., "2 hours ago")
 */
export function formatRelativeTime(date: Date | string | number): string {
  let dateObj: Date;
  
  if (typeof date === 'string') {
    dateObj = parseISO(date);
  } else if (typeof date === 'number') {
    dateObj = new Date(date);
  } else {
    dateObj = date;
  }
  
  if (!isValid(dateObj)) return 'Invalid date';
  
  return formatDistanceToNow(dateObj, { addSuffix: true });
}

/**
 * Format duration in milliseconds to human readable
 */
export function formatDuration(milliseconds: number): string {
  if (milliseconds < 1000) {
    return `${milliseconds}ms`;
  }
  
  const seconds = Math.floor(milliseconds / 1000);
  if (seconds < 60) {
    return `${seconds}s`;
  }
  
  const minutes = Math.floor(seconds / 60);
  if (minutes < 60) {
    return `${minutes}m ${seconds % 60}s`;
  }
  
  const hours = Math.floor(minutes / 60);
  if (hours < 24) {
    return `${hours}h ${minutes % 60}m`;
  }
  
  const days = Math.floor(hours / 24);
  return `${days}d ${hours % 24}h`;
}

/**
 * Format file size
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Format risk score as percentage
 */
export function formatRiskScore(score: number): string {
  if (isNaN(score)) return '0%';
  return Math.round(score) + '%';
}

/**
 * Format risk level with color coding
 */
export function formatRiskLevel(level: string): {
  label: string;
  color: string;
  bgColor: string;
} {
  switch (level.toLowerCase()) {
    case 'low':
      return {
        label: 'Low Risk',
        color: 'text-green-600',
        bgColor: 'bg-green-100'
      };
    case 'medium':
      return {
        label: 'Medium Risk',
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-100'
      };
    case 'high':
      return {
        label: 'High Risk',
        color: 'text-orange-600',
        bgColor: 'bg-orange-100'
      };
    case 'critical':
      return {
        label: 'Critical Risk',
        color: 'text-red-600',
        bgColor: 'bg-red-100'
      };
    default:
      return {
        label: 'Unknown',
        color: 'text-gray-600',
        bgColor: 'bg-gray-100'
      };
  }
}

/**
 * Format price change with sign and color
 */
export function formatPriceChange(change: number): {
  formatted: string;
  color: string;
  sign: string;
} {
  if (isNaN(change)) {
    return {
      formatted: '0%',
      color: 'text-gray-500',
      sign: ''
    };
  }
  
  const sign = change > 0 ? '+' : '';
  const color = change > 0 ? 'text-green-500' : change < 0 ? 'text-red-500' : 'text-gray-500';
  
  return {
    formatted: formatPercentage(change),
    color,
    sign
  };
}

/**
 * Format market cap
 */
export function formatMarketCap(marketCap: number): string {
  if (isNaN(marketCap) || marketCap <= 0) return 'N/A';
  return '$' + formatLargeNumber(marketCap);
}

/**
 * Format volume
 */
export function formatVolume(volume: number): string {
  if (isNaN(volume) || volume <= 0) return 'N/A';
  return '$' + formatLargeNumber(volume);
}

/**
 * Format holder count
 */
export function formatHolderCount(count: number): string {
  if (isNaN(count) || count <= 0) return '0';
  return formatLargeNumber(count, 0);
}

/**
 * Format transaction hash for display
 */
export function formatTxHash(hash: string, startChars = 6, endChars = 4): string {
  if (!hash) return '';
  if (hash.length <= startChars + endChars) return hash;
  
  return `${hash.slice(0, startChars)}...${hash.slice(-endChars)}`;
}

/**
 * Format API response time
 */
export function formatResponseTime(milliseconds: number): string {
  if (milliseconds < 1000) {
    return `${Math.round(milliseconds)}ms`;
  } else {
    return `${(milliseconds / 1000).toFixed(2)}s`;
  }
}

/**
 * Format uptime
 */
export function formatUptime(seconds: number): string {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  
  if (days > 0) {
    return `${days}d ${hours}h ${minutes}m`;
  } else if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else {
    return `${minutes}m`;
  }
}

/**
 * Format error message for display
 */
export function formatErrorMessage(error: any): string {
  if (typeof error === 'string') {
    return error;
  }
  
  if (error && typeof error === 'object') {
    if (error.message) {
      return error.message;
    }
    
    if (error.error) {
      return error.error;
    }
    
    if (error.details) {
      return error.details;
    }
  }
  
  return 'An unknown error occurred';
}

/**
 * Truncate text with ellipsis
 */
export function truncateText(text: string, maxLength: number): string {
  if (!text || text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
}

/**
 * Format social media handle
 */
export function formatSocialHandle(handle: string, platform: string): string {
  if (!handle) return '';
  
  const cleanHandle = handle.replace(/^@/, '');
  
  switch (platform.toLowerCase()) {
    case 'twitter':
      return `@${cleanHandle}`;
    case 'github':
      return cleanHandle;
    case 'telegram':
      return `@${cleanHandle}`;
    default:
      return cleanHandle;
  }
}

/**
 * Format confidence score
 */
export function formatConfidence(confidence: number): string {
  if (isNaN(confidence)) return '0%';
  return Math.round(confidence * 100) + '%';
}
