/**
 * Logger Utilities
 * Centralized logging functionality with different levels and outputs
 */

export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
  TRACE = 4
}

export interface LogEntry {
  timestamp: string;
  level: string;
  message: string;
  meta?: any;
  service?: string;
  userId?: string;
  requestId?: string;
  error?: Error;
}

export interface LoggerConfig {
  level: LogLevel;
  service: string;
  format: 'json' | 'text';
  outputs: ('console' | 'file' | 'database')[];
  filePath?: string;
  maxFileSize?: number;
  maxFiles?: number;
}

class Logger {
  private config: LoggerConfig;
  private logBuffer: LogEntry[] = [];
  private flushInterval?: NodeJS.Timeout;

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = {
      level: LogLevel.INFO,
      service: 'tokenforge',
      format: 'json',
      outputs: ['console'],
      maxFileSize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
      ...config
    };

    // Start periodic flush for database logging
    if (this.config.outputs.includes('database')) {
      this.flushInterval = setInterval(() => this.flushLogs(), 5000);
    }
  }

  private shouldLog(level: LogLevel): boolean {
    return level <= this.config.level;
  }

  private createLogEntry(
    level: LogLevel,
    message: string,
    meta?: any,
    error?: Error
  ): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level: LogLevel[level],
      message,
      meta,
      service: this.config.service,
      error
    };
  }

  private formatLogEntry(entry: LogEntry): string {
    if (this.config.format === 'json') {
      return JSON.stringify(entry);
    }

    const { timestamp, level, service, message, meta, error } = entry;
    let formatted = `[${timestamp}] ${level.padEnd(5)} [${service}] ${message}`;

    if (meta) {
      formatted += ` ${JSON.stringify(meta)}`;
    }

    if (error) {
      formatted += `\n${error.stack}`;
    }

    return formatted;
  }

  private outputLog(entry: LogEntry): void {
    const formatted = this.formatLogEntry(entry);

    // Console output
    if (this.config.outputs.includes('console')) {
      switch (entry.level) {
        case 'ERROR':
          console.error(formatted);
          break;
        case 'WARN':
          console.warn(formatted);
          break;
        case 'DEBUG':
        case 'TRACE':
          console.debug(formatted);
          break;
        default:
          console.log(formatted);
      }
    }

    // File output
    if (this.config.outputs.includes('file') && this.config.filePath) {
      this.writeToFile(formatted);
    }

    // Database output (buffered)
    if (this.config.outputs.includes('database')) {
      this.logBuffer.push(entry);
    }
  }

  private writeToFile(content: string): void {
    try {
      const fs = require('fs');
      const path = require('path');

      if (!this.config.filePath) return;

      // Ensure directory exists
      const dir = path.dirname(this.config.filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      // Check file size and rotate if necessary
      if (fs.existsSync(this.config.filePath)) {
        const stats = fs.statSync(this.config.filePath);
        if (stats.size > (this.config.maxFileSize || 10 * 1024 * 1024)) {
          this.rotateLogFile();
        }
      }

      // Append to file
      fs.appendFileSync(this.config.filePath, content + '\n');
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
  }

  private rotateLogFile(): void {
    try {
      const fs = require('fs');
      const path = require('path');

      if (!this.config.filePath) return;

      const ext = path.extname(this.config.filePath);
      const base = this.config.filePath.slice(0, -ext.length);
      const maxFiles = this.config.maxFiles || 5;

      // Rotate existing files
      for (let i = maxFiles - 1; i >= 1; i--) {
        const oldFile = `${base}.${i}${ext}`;
        const newFile = `${base}.${i + 1}${ext}`;

        if (fs.existsSync(oldFile)) {
          if (i === maxFiles - 1) {
            fs.unlinkSync(oldFile); // Delete oldest file
          } else {
            fs.renameSync(oldFile, newFile);
          }
        }
      }

      // Move current file to .1
      if (fs.existsSync(this.config.filePath)) {
        fs.renameSync(this.config.filePath, `${base}.1${ext}`);
      }
    } catch (error) {
      console.error('Failed to rotate log file:', error);
    }
  }

  private async flushLogs(): Promise<void> {
    if (this.logBuffer.length === 0) return;

    try {
      // In a real implementation, you would send these to your database
      // For now, we'll just clear the buffer
      console.log(`Flushing ${this.logBuffer.length} log entries to database`);
      this.logBuffer = [];
    } catch (error) {
      console.error('Failed to flush logs to database:', error);
    }
  }

  public error(message: string, meta?: any, error?: Error): void {
    if (this.shouldLog(LogLevel.ERROR)) {
      const entry = this.createLogEntry(LogLevel.ERROR, message, meta, error);
      this.outputLog(entry);
    }
  }

  public warn(message: string, meta?: any): void {
    if (this.shouldLog(LogLevel.WARN)) {
      const entry = this.createLogEntry(LogLevel.WARN, message, meta);
      this.outputLog(entry);
    }
  }

  public info(message: string, meta?: any): void {
    if (this.shouldLog(LogLevel.INFO)) {
      const entry = this.createLogEntry(LogLevel.INFO, message, meta);
      this.outputLog(entry);
    }
  }

  public debug(message: string, meta?: any): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      const entry = this.createLogEntry(LogLevel.DEBUG, message, meta);
      this.outputLog(entry);
    }
  }

  public trace(message: string, meta?: any): void {
    if (this.shouldLog(LogLevel.TRACE)) {
      const entry = this.createLogEntry(LogLevel.TRACE, message, meta);
      this.outputLog(entry);
    }
  }

  public setLevel(level: LogLevel): void {
    this.config.level = level;
  }

  public setService(service: string): void {
    this.config.service = service;
  }

  public child(meta: any): Logger {
    const childLogger = new Logger(this.config);
    
    // Override outputLog to include child meta
    const originalOutputLog = childLogger.outputLog.bind(childLogger);
    childLogger.outputLog = (entry: LogEntry) => {
      entry.meta = { ...meta, ...entry.meta };
      originalOutputLog(entry);
    };

    return childLogger;
  }

  public async close(): Promise<void> {
    if (this.flushInterval) {
      clearInterval(this.flushInterval);
    }

    // Flush remaining logs
    await this.flushLogs();
  }
}

// Create default logger instance
const defaultLogger = new Logger({
  level: process.env.LOG_LEVEL ? 
    LogLevel[process.env.LOG_LEVEL.toUpperCase() as keyof typeof LogLevel] : 
    LogLevel.INFO,
  service: process.env.SERVICE_NAME || 'tokenforge',
  format: (process.env.LOG_FORMAT as 'json' | 'text') || 'json',
  outputs: process.env.LOG_OUTPUTS?.split(',') as any || ['console'],
  filePath: process.env.LOG_FILE_PATH
});

// Export logger functions
export const logger = {
  error: (message: string, meta?: any, error?: Error) => defaultLogger.error(message, meta, error),
  warn: (message: string, meta?: any) => defaultLogger.warn(message, meta),
  info: (message: string, meta?: any) => defaultLogger.info(message, meta),
  debug: (message: string, meta?: any) => defaultLogger.debug(message, meta),
  trace: (message: string, meta?: any) => defaultLogger.trace(message, meta),
  setLevel: (level: LogLevel) => defaultLogger.setLevel(level),
  setService: (service: string) => defaultLogger.setService(service),
  child: (meta: any) => defaultLogger.child(meta),
  close: () => defaultLogger.close()
};

// Export Logger class for custom instances
export { Logger };

// Utility functions for structured logging
export function logApiRequest(
  method: string,
  url: string,
  statusCode: number,
  responseTime: number,
  userId?: string
): void {
  logger.info('API Request', {
    method: method.toUpperCase(),
    url,
    statusCode,
    responseTime: `${responseTime}ms`,
    userId
  });
}

export function logApiError(
  method: string,
  url: string,
  statusCode: number,
  error: Error,
  userId?: string
): void {
  logger.error('API Error', {
    method: method.toUpperCase(),
    url,
    statusCode,
    userId
  }, error);
}

export function logDatabaseQuery(
  query: string,
  duration: number,
  rowCount?: number
): void {
  logger.debug('Database Query', {
    query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
    duration: `${duration}ms`,
    rowCount
  });
}

export function logCacheOperation(
  operation: 'get' | 'set' | 'del',
  key: string,
  hit?: boolean,
  ttl?: number
): void {
  logger.debug('Cache Operation', {
    operation,
    key,
    hit,
    ttl
  });
}

export function logSecurityEvent(
  event: string,
  userId?: string,
  ipAddress?: string,
  userAgent?: string
): void {
  logger.warn('Security Event', {
    event,
    userId,
    ipAddress,
    userAgent
  });
}

export function logPerformanceMetric(
  metric: string,
  value: number,
  unit: string,
  tags?: Record<string, string>
): void {
  logger.info('Performance Metric', {
    metric,
    value,
    unit,
    tags
  });
}
