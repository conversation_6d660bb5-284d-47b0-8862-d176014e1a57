/**
 * Validation Utilities
 * Functions for validating user input and data
 */

import validator from 'validator';
import { z } from 'zod';
import { VALIDATION_RULES, REGEX_PATTERNS } from '../constants';

/**
 * Validate email address
 */
export function isValidEmail(email: string): boolean {
  if (!email || typeof email !== 'string') return false;
  
  const { minLength, maxLength, pattern } = VALIDATION_RULES.EMAIL;
  
  return (
    email.length >= minLength &&
    email.length <= maxLength &&
    pattern.test(email) &&
    validator.isEmail(email)
  );
}

/**
 * Validate username
 */
export function isValidUsername(username: string): boolean {
  if (!username || typeof username !== 'string') return false;
  
  const { minLength, maxLength, pattern } = VALIDATION_RULES.USERNAME;
  
  return (
    username.length >= minLength &&
    username.length <= maxLength &&
    pattern.test(username)
  );
}

/**
 * Validate password strength
 */
export function isValidPassword(password: string): {
  isValid: boolean;
  errors: string[];
  strength: 'weak' | 'medium' | 'strong';
} {
  const errors: string[] = [];
  const rules = VALIDATION_RULES.PASSWORD;
  
  if (!password || typeof password !== 'string') {
    return { isValid: false, errors: ['Password is required'], strength: 'weak' };
  }
  
  if (password.length < rules.minLength) {
    errors.push(`Password must be at least ${rules.minLength} characters long`);
  }
  
  if (password.length > rules.maxLength) {
    errors.push(`Password must be no more than ${rules.maxLength} characters long`);
  }
  
  if (rules.requireUppercase && !/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (rules.requireLowercase && !/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (rules.requireNumbers && !/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (rules.requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }
  
  // Calculate strength
  let strength: 'weak' | 'medium' | 'strong' = 'weak';
  if (errors.length === 0) {
    const hasUpper = /[A-Z]/.test(password);
    const hasLower = /[a-z]/.test(password);
    const hasNumber = /\d/.test(password);
    const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    const isLongEnough = password.length >= 12;
    
    const criteriaCount = [hasUpper, hasLower, hasNumber, hasSpecial, isLongEnough].filter(Boolean).length;
    
    if (criteriaCount >= 4) strength = 'strong';
    else if (criteriaCount >= 3) strength = 'medium';
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    strength
  };
}

/**
 * Validate URL
 */
export function isValidUrl(url: string): boolean {
  if (!url || typeof url !== 'string') return false;
  return REGEX_PATTERNS.URL.test(url) && validator.isURL(url);
}

/**
 * Validate Twitter handle
 */
export function isValidTwitterHandle(handle: string): boolean {
  if (!handle || typeof handle !== 'string') return false;
  return REGEX_PATTERNS.TWITTER_HANDLE.test(handle);
}

/**
 * Validate GitHub username
 */
export function isValidGitHubUsername(username: string): boolean {
  if (!username || typeof username !== 'string') return false;
  return REGEX_PATTERNS.GITHUB_USERNAME.test(username);
}

/**
 * Validate portfolio name
 */
export function isValidPortfolioName(name: string): boolean {
  if (!name || typeof name !== 'string') return false;
  
  const { minLength, maxLength } = VALIDATION_RULES.PORTFOLIO_NAME;
  return name.trim().length >= minLength && name.trim().length <= maxLength;
}

/**
 * Validate alert name
 */
export function isValidAlertName(name: string): boolean {
  if (!name || typeof name !== 'string') return false;
  
  const { minLength, maxLength } = VALIDATION_RULES.ALERT_NAME;
  return name.trim().length >= minLength && name.trim().length <= maxLength;
}

/**
 * Validate numeric value
 */
export function isValidNumber(value: any, min?: number, max?: number): boolean {
  const num = Number(value);
  
  if (isNaN(num) || !isFinite(num)) return false;
  if (min !== undefined && num < min) return false;
  if (max !== undefined && num > max) return false;
  
  return true;
}

/**
 * Validate positive number
 */
export function isPositiveNumber(value: any): boolean {
  return isValidNumber(value, 0.000001); // Allow very small positive numbers
}

/**
 * Validate integer
 */
export function isValidInteger(value: any, min?: number, max?: number): boolean {
  const num = Number(value);
  
  if (!Number.isInteger(num)) return false;
  if (min !== undefined && num < min) return false;
  if (max !== undefined && num > max) return false;
  
  return true;
}

/**
 * Validate date string
 */
export function isValidDate(dateString: string): boolean {
  if (!dateString || typeof dateString !== 'string') return false;
  
  const date = new Date(dateString);
  return !isNaN(date.getTime()) && validator.isISO8601(dateString);
}

/**
 * Validate pagination parameters
 */
export function validatePagination(page: any, limit: any): {
  isValid: boolean;
  page: number;
  limit: number;
  errors: string[];
} {
  const errors: string[] = [];
  let validPage = 1;
  let validLimit = 20;
  
  // Validate page
  if (page !== undefined) {
    if (!isValidInteger(page, 1)) {
      errors.push('Page must be a positive integer');
    } else {
      validPage = Number(page);
    }
  }
  
  // Validate limit
  if (limit !== undefined) {
    if (!isValidInteger(limit, 1, 100)) {
      errors.push('Limit must be an integer between 1 and 100');
    } else {
      validLimit = Number(limit);
    }
  }
  
  return {
    isValid: errors.length === 0,
    page: validPage,
    limit: validLimit,
    errors
  };
}

/**
 * Validate sort parameters
 */
export function validateSort(sortBy: string, sortOrder: string, allowedFields: string[]): {
  isValid: boolean;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  errors: string[];
} {
  const errors: string[] = [];
  let validSortBy = allowedFields[0] || 'createdAt';
  let validSortOrder: 'asc' | 'desc' = 'desc';
  
  // Validate sortBy
  if (sortBy && !allowedFields.includes(sortBy)) {
    errors.push(`Invalid sort field. Allowed fields: ${allowedFields.join(', ')}`);
  } else if (sortBy) {
    validSortBy = sortBy;
  }
  
  // Validate sortOrder
  if (sortOrder && !['asc', 'desc'].includes(sortOrder)) {
    errors.push('Sort order must be "asc" or "desc"');
  } else if (sortOrder) {
    validSortOrder = sortOrder as 'asc' | 'desc';
  }
  
  return {
    isValid: errors.length === 0,
    sortBy: validSortBy,
    sortOrder: validSortOrder,
    errors
  };
}

/**
 * Sanitize string input
 */
export function sanitizeString(input: string, maxLength?: number): string {
  if (!input || typeof input !== 'string') return '';
  
  let sanitized = input.trim();
  
  // Remove potentially dangerous characters
  sanitized = sanitized.replace(/[<>]/g, '');
  
  // Limit length if specified
  if (maxLength && sanitized.length > maxLength) {
    sanitized = sanitized.substring(0, maxLength);
  }
  
  return sanitized;
}

/**
 * Validate file upload
 */
export function validateFileUpload(file: any): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (!file) {
    errors.push('File is required');
    return { isValid: false, errors };
  }
  
  // Check file size (10MB limit)
  if (file.size > 10 * 1024 * 1024) {
    errors.push('File size must be less than 10MB');
  }
  
  // Check file type
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  if (!allowedTypes.includes(file.type)) {
    errors.push('File type must be JPEG, PNG, GIF, or WebP');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Create Zod schema for user registration
 */
export const userRegistrationSchema = z.object({
  email: z.string().email('Invalid email address'),
  username: z.string()
    .min(3, 'Username must be at least 3 characters')
    .max(30, 'Username must be no more than 30 characters')
    .regex(/^[a-zA-Z0-9_-]+$/, 'Username can only contain letters, numbers, underscores, and hyphens'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/\d/, 'Password must contain at least one number')
    .regex(/[!@#$%^&*(),.?":{}|<>]/, 'Password must contain at least one special character'),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  acceptTerms: z.boolean().refine(val => val === true, 'You must accept the terms and conditions')
});

/**
 * Create Zod schema for token analysis request
 */
export const tokenAnalysisSchema = z.object({
  address: z.string().regex(/^0x[a-fA-F0-9]{40}$/, 'Invalid token address'),
  chain: z.enum(['ethereum', 'bsc', 'polygon', 'arbitrum', 'optimism', 'avalanche', 'fantom']),
  includeContract: z.boolean().optional(),
  includeSocial: z.boolean().optional(),
  includeDeveloper: z.boolean().optional(),
  forceRefresh: z.boolean().optional()
});

/**
 * Validate data against schema
 */
export function validateWithSchema<T>(schema: z.ZodSchema<T>, data: unknown): {
  isValid: boolean;
  data?: T;
  errors: string[];
} {
  try {
    const validatedData = schema.parse(data);
    return {
      isValid: true,
      data: validatedData,
      errors: []
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        errors: error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
      };
    }
    
    return {
      isValid: false,
      errors: ['Validation failed']
    };
  }
}
