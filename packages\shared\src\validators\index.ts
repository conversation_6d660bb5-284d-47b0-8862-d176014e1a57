/**
 * TokenForge Validators
 * Zod schemas and validation functions for data validation
 */

import { z } from 'zod';
import { REGEX_PATTERNS, SUPPORTED_CHAINS } from '../constants';

// =============================================================================
// Basic Validators
// =============================================================================

export const emailSchema = z.string()
  .email('Invalid email address')
  .min(5, 'Email must be at least 5 characters')
  .max(254, 'Email must be no more than 254 characters');

export const usernameSchema = z.string()
  .min(3, 'Username must be at least 3 characters')
  .max(30, 'Username must be no more than 30 characters')
  .regex(REGEX_PATTERNS.USERNAME, 'Username can only contain letters, numbers, underscores, and hyphens');

export const passwordSchema = z.string()
  .min(8, 'Password must be at least 8 characters')
  .max(128, 'Password must be no more than 128 characters')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  .regex(/\d/, 'Password must contain at least one number')
  .regex(/[!@#$%^&*(),.?":{}|<>]/, 'Password must contain at least one special character');

export const ethereumAddressSchema = z.string()
  .regex(REGEX_PATTERNS.ETHEREUM_ADDRESS, 'Invalid Ethereum address format');

export const chainSchema = z.enum([
  'ethereum', 'bsc', 'polygon', 'arbitrum', 'optimism', 'avalanche', 'fantom'
] as const);

// =============================================================================
// User Schemas
// =============================================================================

export const userRegistrationSchema = z.object({
  email: emailSchema,
  username: usernameSchema,
  password: passwordSchema,
  firstName: z.string().min(1).max(50).optional(),
  lastName: z.string().min(1).max(50).optional(),
  acceptTerms: z.boolean().refine(val => val === true, 'You must accept the terms and conditions')
});

export const userLoginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().optional()
});

export const userUpdateSchema = z.object({
  firstName: z.string().min(1).max(50).optional(),
  lastName: z.string().min(1).max(50).optional(),
  bio: z.string().max(500).optional(),
  location: z.string().max(100).optional(),
  website: z.string().url().optional(),
  twitter: z.string().regex(REGEX_PATTERNS.TWITTER_HANDLE).optional(),
  github: z.string().regex(REGEX_PATTERNS.GITHUB_USERNAME).optional()
});

export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: passwordSchema,
  confirmPassword: z.string()
}).refine(data => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
});

// =============================================================================
// Token Analysis Schemas
// =============================================================================

export const tokenAnalysisRequestSchema = z.object({
  address: ethereumAddressSchema,
  chain: chainSchema,
  includeContract: z.boolean().optional(),
  includeSocial: z.boolean().optional(),
  includeDeveloper: z.boolean().optional(),
  forceRefresh: z.boolean().optional()
});

export const bulkTokenAnalysisRequestSchema = z.object({
  tokens: z.array(z.object({
    address: ethereumAddressSchema,
    chain: chainSchema
  })).min(1, 'At least one token is required').max(100, 'Maximum 100 tokens allowed'),
  includeContract: z.boolean().optional(),
  includeSocial: z.boolean().optional(),
  includeDeveloper: z.boolean().optional()
});

// =============================================================================
// Portfolio Schemas
// =============================================================================

export const createPortfolioSchema = z.object({
  name: z.string().min(1, 'Portfolio name is required').max(100, 'Portfolio name must be no more than 100 characters'),
  description: z.string().max(500, 'Description must be no more than 500 characters').optional(),
  isPublic: z.boolean().optional()
});

export const updatePortfolioSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().max(500).optional(),
  isPublic: z.boolean().optional()
});

export const addTokenToPortfolioSchema = z.object({
  tokenAddress: ethereumAddressSchema,
  chain: chainSchema,
  amount: z.string().regex(/^\d+(\.\d+)?$/, 'Amount must be a valid number'),
  averagePrice: z.number().positive('Average price must be positive').optional()
});

export const updatePortfolioTokenSchema = z.object({
  amount: z.string().regex(/^\d+(\.\d+)?$/, 'Amount must be a valid number').optional(),
  averagePrice: z.number().positive('Average price must be positive').optional()
});

// =============================================================================
// Alert Schemas
// =============================================================================

export const alertTypeSchema = z.enum([
  'price_above', 'price_below', 'risk_increase', 'risk_decrease',
  'volume_spike', 'new_holder', 'whale_movement'
] as const);

export const alertConditionSchema = z.object({
  threshold: z.number(),
  timeframe: z.string().optional(),
  percentage: z.boolean().optional()
});

export const createAlertSchema = z.object({
  type: alertTypeSchema,
  tokenAddress: ethereumAddressSchema,
  chain: chainSchema,
  condition: alertConditionSchema
});

export const updateAlertSchema = z.object({
  condition: alertConditionSchema.optional(),
  isActive: z.boolean().optional()
});

// =============================================================================
// Search Schemas
// =============================================================================

export const tokenSearchSchema = z.object({
  query: z.string().min(1, 'Search query is required').max(100),
  chains: z.array(chainSchema).optional(),
  riskLevels: z.array(z.enum(['low', 'medium', 'high', 'critical'])).optional(),
  minMarketCap: z.number().positive().optional(),
  maxMarketCap: z.number().positive().optional(),
  minVolume24h: z.number().positive().optional(),
  sortBy: z.enum(['relevance', 'marketCap', 'volume', 'riskScore', 'created']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
  page: z.number().int().positive().optional(),
  limit: z.number().int().positive().max(100).optional()
});

// =============================================================================
// Pagination Schema
// =============================================================================

export const paginationSchema = z.object({
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(20)
});

// =============================================================================
// Sort Schema
// =============================================================================

export const sortSchema = z.object({
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
});

// =============================================================================
// API Key Schema
// =============================================================================

export const apiKeySchema = z.string()
  .regex(/^tf_[a-fA-F0-9]{48}$/, 'Invalid API key format');

// =============================================================================
// Webhook Schemas
// =============================================================================

export const webhookConfigSchema = z.object({
  url: z.string().url('Invalid webhook URL'),
  events: z.array(z.string()).min(1, 'At least one event must be selected'),
  secret: z.string().min(16, 'Webhook secret must be at least 16 characters').optional(),
  isActive: z.boolean().default(true)
});

// =============================================================================
// Subscription Schemas
// =============================================================================

export const subscriptionPlanSchema = z.enum(['free', 'pro', 'enterprise'] as const);

export const createSubscriptionSchema = z.object({
  planId: subscriptionPlanSchema,
  paymentMethodId: z.string().min(1, 'Payment method is required')
});

export const updateSubscriptionSchema = z.object({
  planId: subscriptionPlanSchema.optional(),
  cancelAtPeriodEnd: z.boolean().optional()
});

// =============================================================================
// Validation Helper Functions
// =============================================================================

export function validateSchema<T>(schema: z.ZodSchema<T>, data: unknown): {
  success: boolean;
  data?: T;
  errors?: string[];
} {
  try {
    const validatedData = schema.parse(data);
    return {
      success: true,
      data: validatedData
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        errors: error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
      };
    }
    
    return {
      success: false,
      errors: ['Validation failed']
    };
  }
}

export function createValidationMiddleware<T>(schema: z.ZodSchema<T>) {
  return (data: unknown) => {
    const result = validateSchema(schema, data);
    if (!result.success) {
      throw new Error(`Validation failed: ${result.errors?.join(', ')}`);
    }
    return result.data!;
  };
}

// =============================================================================
// Export all schemas
// =============================================================================

export const schemas = {
  // User schemas
  userRegistration: userRegistrationSchema,
  userLogin: userLoginSchema,
  userUpdate: userUpdateSchema,
  changePassword: changePasswordSchema,
  
  // Token analysis schemas
  tokenAnalysisRequest: tokenAnalysisRequestSchema,
  bulkTokenAnalysisRequest: bulkTokenAnalysisRequestSchema,
  
  // Portfolio schemas
  createPortfolio: createPortfolioSchema,
  updatePortfolio: updatePortfolioSchema,
  addTokenToPortfolio: addTokenToPortfolioSchema,
  updatePortfolioToken: updatePortfolioTokenSchema,
  
  // Alert schemas
  createAlert: createAlertSchema,
  updateAlert: updateAlertSchema,
  
  // Search schemas
  tokenSearch: tokenSearchSchema,
  
  // Utility schemas
  pagination: paginationSchema,
  sort: sortSchema,
  apiKey: apiKeySchema,
  
  // Webhook schemas
  webhookConfig: webhookConfigSchema,
  
  // Subscription schemas
  createSubscription: createSubscriptionSchema,
  updateSubscription: updateSubscriptionSchema
};
