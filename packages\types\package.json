{"name": "@tokenforge/types", "version": "1.0.0", "description": "Shared TypeScript types for TokenForge", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "files": ["dist", "src"], "dependencies": {}, "devDependencies": {"typescript": "^5.3.2", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0"}, "keywords": ["typescript", "types", "cryptocurrency", "blockchain"], "author": "HectorTa1989", "license": "MIT"}