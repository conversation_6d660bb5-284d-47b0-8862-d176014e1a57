/**
 * TokenForge API Types
 * Type definitions for API requests, responses, and endpoints
 */

// =============================================================================
// Authentication Types
// =============================================================================

export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterRequest {
  email: string;
  password: string;
  username: string;
  firstName?: string;
  lastName?: string;
  acceptTerms: boolean;
}

export interface AuthResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface ResetPasswordRequest {
  email: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

// =============================================================================
// Token Analysis API Types
// =============================================================================

export interface TokenAnalysisRequest {
  address: string;
  chain: SupportedChain;
  includeContract?: boolean;
  includeSocial?: boolean;
  includeDeveloper?: boolean;
  forceRefresh?: boolean;
}

export interface TokenAnalysisResponse {
  token: TokenInfo;
  metrics: TokenMetrics;
  riskAssessment: RiskAssessment;
  contractAnalysis?: ContractAnalysis;
  socialAnalysis?: SocialAnalysis;
  developerAnalysis?: DeveloperAnalysis;
  lastUpdated: number;
  analysisTime: number;
}

export interface BulkTokenAnalysisRequest {
  tokens: Array<{
    address: string;
    chain: SupportedChain;
  }>;
  includeContract?: boolean;
  includeSocial?: boolean;
  includeDeveloper?: boolean;
}

export interface BulkTokenAnalysisResponse {
  results: TokenAnalysisResponse[];
  failed: Array<{
    address: string;
    chain: SupportedChain;
    error: string;
  }>;
  totalProcessed: number;
  totalFailed: number;
  processingTime: number;
}

// =============================================================================
// Search and Discovery API Types
// =============================================================================

export interface TokenSearchRequest {
  query: string;
  chains?: SupportedChain[];
  riskLevels?: RiskLevel[];
  minMarketCap?: number;
  maxMarketCap?: number;
  minVolume24h?: number;
  sortBy?: 'relevance' | 'marketCap' | 'volume' | 'riskScore' | 'created';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface TokenSearchResponse {
  tokens: TokenSearchResult[];
  pagination: PaginationInfo;
  filters: SearchFilters;
  totalResults: number;
  searchTime: number;
}

export interface TokenSearchResult {
  token: TokenInfo;
  metrics: TokenMetrics;
  riskScore: number;
  riskLevel: RiskLevel;
  relevanceScore: number;
  highlights: string[];
}

export interface SearchFilters {
  chains: Array<{ chain: SupportedChain; count: number }>;
  riskLevels: Array<{ level: RiskLevel; count: number }>;
  marketCapRanges: Array<{ range: string; count: number }>;
}

// =============================================================================
// Portfolio API Types
// =============================================================================

export interface CreatePortfolioRequest {
  name: string;
  description?: string;
  isPublic?: boolean;
}

export interface UpdatePortfolioRequest {
  name?: string;
  description?: string;
  isPublic?: boolean;
}

export interface AddTokenToPortfolioRequest {
  tokenAddress: string;
  chain: SupportedChain;
  amount: string;
  averagePrice?: number;
}

export interface UpdatePortfolioTokenRequest {
  amount?: string;
  averagePrice?: number;
}

export interface PortfolioAnalysisResponse {
  portfolio: Portfolio;
  riskAnalysis: {
    overallRiskScore: number;
    overallRiskLevel: RiskLevel;
    riskDistribution: Record<RiskLevel, number>;
    highestRiskTokens: PortfolioToken[];
    recommendations: string[];
  };
  performance: {
    totalValue: number;
    totalChange24h: number;
    totalChangePercent24h: number;
    bestPerformer: PortfolioToken;
    worstPerformer: PortfolioToken;
  };
}

// =============================================================================
// Alert API Types
// =============================================================================

export interface CreateAlertRequest {
  type: AlertType;
  tokenAddress: string;
  chain: SupportedChain;
  condition: AlertCondition;
}

export interface UpdateAlertRequest {
  condition?: AlertCondition;
  isActive?: boolean;
}

export interface AlertHistoryResponse {
  alerts: Array<Alert & {
    triggerDetails?: {
      previousValue: number;
      currentValue: number;
      threshold: number;
      message: string;
    };
  }>;
  pagination: PaginationInfo;
}

// =============================================================================
// Analytics API Types
// =============================================================================

export interface AnalyticsRequest {
  startDate: string;
  endDate: string;
  granularity: 'hour' | 'day' | 'week' | 'month';
  metrics: string[];
}

export interface AnalyticsResponse {
  data: Array<{
    timestamp: number;
    values: Record<string, number>;
  }>;
  summary: {
    totalRequests: number;
    uniqueUsers: number;
    averageResponseTime: number;
    errorRate: number;
  };
}

export interface UserAnalyticsResponse {
  usage: {
    totalRequests: number;
    requestsToday: number;
    requestsThisMonth: number;
    remainingRequests: number;
  };
  activity: {
    tokensAnalyzed: number;
    portfoliosCreated: number;
    alertsCreated: number;
    lastActivity: number;
  };
  preferences: {
    favoriteChains: SupportedChain[];
    mostAnalyzedTokens: Array<{
      address: string;
      chain: SupportedChain;
      symbol: string;
      count: number;
    }>;
  };
}

// =============================================================================
// Subscription API Types
// =============================================================================

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  features: string[];
  limits: {
    requestsPerMonth: number;
    portfolios: number;
    alerts: number;
    apiAccess: boolean;
    prioritySupport: boolean;
  };
}

export interface UserSubscription {
  id: string;
  userId: string;
  planId: string;
  plan: SubscriptionPlan;
  status: 'active' | 'canceled' | 'past_due' | 'unpaid';
  currentPeriodStart: number;
  currentPeriodEnd: number;
  cancelAtPeriodEnd: boolean;
  usage: {
    requests: number;
    portfolios: number;
    alerts: number;
  };
}

export interface CreateSubscriptionRequest {
  planId: string;
  paymentMethodId: string;
}

export interface UpdateSubscriptionRequest {
  planId?: string;
  cancelAtPeriodEnd?: boolean;
}

// =============================================================================
// Utility Types
// =============================================================================

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  pages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface SortOptions {
  field: string;
  order: 'asc' | 'desc';
}

export interface FilterOptions {
  [key: string]: any;
}

// =============================================================================
// Error Types
// =============================================================================

export interface ApiError {
  code: string;
  message: string;
  details?: any;
  timestamp: number;
}

export interface ValidationError extends ApiError {
  field: string;
  value: any;
  constraint: string;
}

export interface RateLimitError extends ApiError {
  retryAfter: number;
  limit: number;
  remaining: number;
  resetTime: number;
}

// =============================================================================
// Health Check Types
// =============================================================================

export interface HealthCheckResponse {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: number;
  uptime: number;
  version: string;
  services: {
    database: ServiceHealth;
    redis: ServiceHealth;
    blockchain: ServiceHealth;
    ai: ServiceHealth;
  };
}

export interface ServiceHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  latency?: string;
  error?: string;
  lastCheck: number;
}

// Re-export common types
export type { User, SupportedChain, RiskLevel, AlertType } from './index';
