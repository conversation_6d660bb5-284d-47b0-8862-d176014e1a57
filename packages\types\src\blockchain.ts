/**
 * TokenForge Blockchain Types
 * Type definitions for blockchain interactions and data structures
 */

// =============================================================================
// Network and Chain Types
// =============================================================================

export type ChainId = 1 | 56 | 137 | 42161 | 10 | 43114 | 250 | 25 | 5 | 97 | 80001;

export type SupportedChain = 
  | 'ethereum'
  | 'bsc'
  | 'polygon'
  | 'arbitrum'
  | 'optimism'
  | 'avalanche'
  | 'fantom'
  | 'cronos'
  | 'goerli'
  | 'bsc-testnet'
  | 'mumbai';

export interface NetworkConfig {
  chainId: ChainId;
  name: string;
  symbol: string;
  rpcUrl: string;
  wsUrl?: string;
  explorerUrl: string;
  explorerApiUrl: string;
  explorerApiKey?: string;
  multicallAddress?: string;
  isTestnet: boolean;
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
  gasConfig: {
    gasLimit: number;
    gasPrice?: string;
    maxFeePerGas?: string;
    maxPriorityFeePerGas?: string;
  };
}

// =============================================================================
// Transaction Types
// =============================================================================

export interface Transaction {
  hash: string;
  blockNumber: number;
  blockHash: string;
  transactionIndex: number;
  from: string;
  to: string;
  value: string;
  gas: string;
  gasPrice: string;
  gasUsed?: string;
  status: 'success' | 'failed' | 'pending';
  timestamp: number;
  nonce: number;
  input: string;
  logs?: TransactionLog[];
}

export interface TransactionLog {
  address: string;
  topics: string[];
  data: string;
  blockNumber: number;
  transactionHash: string;
  transactionIndex: number;
  blockHash: string;
  logIndex: number;
  removed: boolean;
}

export interface TransactionReceipt {
  transactionHash: string;
  transactionIndex: number;
  blockHash: string;
  blockNumber: number;
  from: string;
  to: string;
  gasUsed: string;
  cumulativeGasUsed: string;
  contractAddress?: string;
  logs: TransactionLog[];
  status: 'success' | 'failed';
  effectiveGasPrice: string;
}

// =============================================================================
// Block Types
// =============================================================================

export interface Block {
  number: number;
  hash: string;
  parentHash: string;
  nonce: string;
  sha3Uncles: string;
  logsBloom: string;
  transactionsRoot: string;
  stateRoot: string;
  receiptsRoot: string;
  miner: string;
  difficulty: string;
  totalDifficulty: string;
  extraData: string;
  size: number;
  gasLimit: string;
  gasUsed: string;
  timestamp: number;
  transactions: string[] | Transaction[];
  uncles: string[];
}

// =============================================================================
// Token Contract Types
// =============================================================================

export interface TokenContract {
  address: string;
  name: string;
  symbol: string;
  decimals: number;
  totalSupply: string;
  chain: SupportedChain;
  standard: 'ERC20' | 'ERC721' | 'ERC1155' | 'BEP20' | 'SPL';
  isVerified: boolean;
  createdAt: number;
  creator: string;
  creationTx: string;
}

export interface TokenBalance {
  tokenAddress: string;
  holderAddress: string;
  balance: string;
  balanceFormatted: number;
  percentage: number;
  value?: number;
  lastUpdated: number;
}

export interface TokenTransfer {
  transactionHash: string;
  blockNumber: number;
  timestamp: number;
  from: string;
  to: string;
  value: string;
  valueFormatted: number;
  tokenAddress: string;
  tokenSymbol: string;
  tokenDecimals: number;
  logIndex: number;
}

// =============================================================================
// Smart Contract Types
// =============================================================================

export interface SmartContract {
  address: string;
  chain: SupportedChain;
  name?: string;
  isVerified: boolean;
  sourceCode?: string;
  abi?: ContractABI[];
  compiler?: string;
  compilerVersion?: string;
  optimization?: boolean;
  optimizationRuns?: number;
  constructorArguments?: string;
  contractName?: string;
  evmVersion?: string;
  library?: string;
  licenseType?: string;
  proxy?: ProxyInfo;
  implementation?: string;
  createdAt: number;
  creator: string;
  creationTx: string;
}

export interface ContractABI {
  type: 'function' | 'constructor' | 'event' | 'fallback' | 'receive';
  name?: string;
  inputs: ABIInput[];
  outputs?: ABIOutput[];
  stateMutability?: 'pure' | 'view' | 'nonpayable' | 'payable';
  anonymous?: boolean;
  constant?: boolean;
  payable?: boolean;
}

export interface ABIInput {
  name: string;
  type: string;
  indexed?: boolean;
  components?: ABIInput[];
  internalType?: string;
}

export interface ABIOutput {
  name: string;
  type: string;
  components?: ABIOutput[];
  internalType?: string;
}

export interface ProxyInfo {
  isProxy: boolean;
  proxyType: 'transparent' | 'uups' | 'beacon' | 'diamond' | 'minimal' | 'clone';
  implementation: string;
  admin?: string;
  beacon?: string;
}

// =============================================================================
// DeFi Protocol Types
// =============================================================================

export interface LiquidityPool {
  address: string;
  chain: SupportedChain;
  protocol: string;
  token0: TokenContract;
  token1: TokenContract;
  reserve0: string;
  reserve1: string;
  totalSupply: string;
  fee: number;
  volume24h: number;
  volumeWeek: number;
  liquidity: number;
  apr: number;
  createdAt: number;
}

export interface DEXTrade {
  transactionHash: string;
  blockNumber: number;
  timestamp: number;
  dex: string;
  trader: string;
  tokenIn: string;
  tokenOut: string;
  amountIn: string;
  amountOut: string;
  priceImpact: number;
  gasUsed: string;
  gasPrice: string;
}

// =============================================================================
// NFT Types
// =============================================================================

export interface NFTContract {
  address: string;
  chain: SupportedChain;
  name: string;
  symbol: string;
  standard: 'ERC721' | 'ERC1155';
  totalSupply?: string;
  owner?: string;
  isVerified: boolean;
  description?: string;
  image?: string;
  externalUrl?: string;
  createdAt: number;
}

export interface NFTToken {
  contractAddress: string;
  tokenId: string;
  chain: SupportedChain;
  owner: string;
  name?: string;
  description?: string;
  image?: string;
  imageUrl?: string;
  animationUrl?: string;
  externalUrl?: string;
  attributes?: NFTAttribute[];
  metadata?: any;
  lastSale?: NFTSale;
  rarity?: NFTRarity;
}

export interface NFTAttribute {
  traitType: string;
  value: string | number;
  displayType?: string;
  maxValue?: number;
}

export interface NFTSale {
  transactionHash: string;
  blockNumber: number;
  timestamp: number;
  marketplace: string;
  seller: string;
  buyer: string;
  price: string;
  currency: string;
  priceUsd: number;
}

export interface NFTRarity {
  rank: number;
  score: number;
  totalSupply: number;
  rarityScore: number;
}

// =============================================================================
// Gas and Fee Types
// =============================================================================

export interface GasEstimate {
  gasLimit: string;
  gasPrice: string;
  maxFeePerGas?: string;
  maxPriorityFeePerGas?: string;
  estimatedCost: string;
  estimatedCostUsd: number;
}

export interface FeeData {
  gasPrice?: string;
  maxFeePerGas?: string;
  maxPriorityFeePerGas?: string;
  lastBaseFeePerGas?: string;
}

// =============================================================================
// Event Types
// =============================================================================

export interface ContractEvent {
  address: string;
  topics: string[];
  data: string;
  blockNumber: number;
  transactionHash: string;
  transactionIndex: number;
  blockHash: string;
  logIndex: number;
  removed: boolean;
  decoded?: {
    name: string;
    signature: string;
    params: Array<{
      name: string;
      type: string;
      value: any;
      indexed: boolean;
    }>;
  };
}

// =============================================================================
// Wallet Types
// =============================================================================

export interface WalletInfo {
  address: string;
  chain: SupportedChain;
  balance: string;
  balanceUsd: number;
  nonce: number;
  isContract: boolean;
  tokens: TokenBalance[];
  nfts: NFTToken[];
  transactions: Transaction[];
  firstSeen: number;
  lastSeen: number;
  totalTransactions: number;
  labels?: string[];
}

// =============================================================================
// Provider Types
// =============================================================================

export interface ProviderConfig {
  url: string;
  timeout: number;
  retries: number;
  retryDelay: number;
  headers?: Record<string, string>;
}

export interface ProviderStats {
  requests: number;
  errors: number;
  avgResponseTime: number;
  lastError?: string;
  lastRequest: number;
  isHealthy: boolean;
}

// =============================================================================
// Utility Types
// =============================================================================

export interface BlockRange {
  from: number;
  to: number;
}

export interface AddressInfo {
  address: string;
  isContract: boolean;
  isVerified?: boolean;
  name?: string;
  tags?: string[];
}

export interface ChainStats {
  chainId: ChainId;
  name: string;
  latestBlock: number;
  avgBlockTime: number;
  gasPrice: string;
  isHealthy: boolean;
  lastUpdated: number;
}
