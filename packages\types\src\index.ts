// =============================================================================
// CryptoSentinel Shared Types
// =============================================================================

// Blockchain Types
export type ChainId = 1 | 56 | 137 | 42161 | 10 | 43114 | 250 | 25;

export type SupportedChain = 
  | 'ethereum'
  | 'bsc'
  | 'polygon'
  | 'arbitrum'
  | 'optimism'
  | 'avalanche'
  | 'fantom'
  | 'cronos'
  | 'solana';

export interface ChainConfig {
  id: ChainId;
  name: string;
  symbol: string;
  rpcUrl: string;
  explorerUrl: string;
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
}

// Token Types
export interface TokenInfo {
  address: string;
  name: string;
  symbol: string;
  decimals: number;
  totalSupply: string;
  chain: SupportedChain;
  logoUrl?: string;
  website?: string;
  description?: string;
  tags?: string[];
}

export interface TokenMetrics {
  price: number;
  priceChange24h: number;
  volume24h: number;
  marketCap: number;
  circulatingSupply: number;
  maxSupply?: number;
  fullyDilutedValuation?: number;
  holders: number;
  transactions24h: number;
}

// Risk Assessment Types
export type RiskLevel = 'low' | 'medium' | 'high' | 'critical';

export interface RiskFactor {
  name: string;
  description: string;
  severity: RiskLevel;
  score: number;
  weight: number;
  category: 'contract' | 'social' | 'developer' | 'liquidity' | 'market';
}

export interface RiskAssessment {
  tokenAddress: string;
  chain: SupportedChain;
  overallScore: number;
  riskLevel: RiskLevel;
  confidence: number;
  factors: RiskFactor[];
  timestamp: number;
  version: string;
}

// Contract Analysis Types
export interface ContractAnalysis {
  address: string;
  isVerified: boolean;
  sourceCode?: string;
  compiler?: string;
  optimization?: boolean;
  runs?: number;
  constructorArguments?: string;
  abi?: any[];
  
  // Security Analysis
  hasOwner: boolean;
  hasMintFunction: boolean;
  hasBurnFunction: boolean;
  hasPauseFunction: boolean;
  hasBlacklist: boolean;
  hasWhitelist: boolean;
  hasMaxTransaction: boolean;
  hasMaxWallet: boolean;
  hasTradingDelay: boolean;
  hasHoneypot: boolean;
  hasRugPull: boolean;
  
  // Function Analysis
  functions: ContractFunction[];
  events: ContractEvent[];
  modifiers: ContractModifier[];
  
  // Ownership Analysis
  owner?: string;
  ownershipRenounced: boolean;
  multiSig: boolean;
  
  // Proxy Analysis
  isProxy: boolean;
  implementation?: string;
  proxyType?: 'transparent' | 'uups' | 'beacon' | 'diamond';
}

export interface ContractFunction {
  name: string;
  signature: string;
  visibility: 'public' | 'external' | 'internal' | 'private';
  stateMutability: 'pure' | 'view' | 'nonpayable' | 'payable';
  inputs: ContractParameter[];
  outputs: ContractParameter[];
  riskLevel: RiskLevel;
  description?: string;
}

export interface ContractEvent {
  name: string;
  signature: string;
  inputs: ContractParameter[];
  anonymous: boolean;
}

export interface ContractModifier {
  name: string;
  parameters: ContractParameter[];
}

export interface ContractParameter {
  name: string;
  type: string;
  indexed?: boolean;
}

// Social Analysis Types
export interface SocialAnalysis {
  sentiment: SentimentAnalysis;
  mentions: SocialMention[];
  influencers: InfluencerMention[];
  trends: TrendAnalysis;
  community: CommunityMetrics;
}

export interface SentimentAnalysis {
  score: number; // -1 to 1
  label: 'negative' | 'neutral' | 'positive';
  confidence: number;
  breakdown: {
    positive: number;
    neutral: number;
    negative: number;
  };
  sources: {
    twitter: number;
    reddit: number;
    telegram: number;
    discord: number;
  };
}

export interface SocialMention {
  id: string;
  platform: 'twitter' | 'reddit' | 'telegram' | 'discord';
  author: string;
  content: string;
  sentiment: number;
  engagement: number;
  timestamp: number;
  url?: string;
}

export interface InfluencerMention {
  influencer: {
    username: string;
    platform: string;
    followers: number;
    verified: boolean;
  };
  mention: SocialMention;
  impact: number;
}

export interface TrendAnalysis {
  trending: boolean;
  trendScore: number;
  velocity: number;
  peakMentions: number;
  timeframe: string;
}

export interface CommunityMetrics {
  totalMembers: number;
  activeMembers: number;
  growth24h: number;
  engagement: number;
  platforms: {
    twitter?: number;
    telegram?: number;
    discord?: number;
    reddit?: number;
  };
}

// Developer Analysis Types
export interface DeveloperAnalysis {
  team: TeamMember[];
  repositories: Repository[];
  activity: DeveloperActivity;
  reputation: DeveloperReputation;
}

export interface TeamMember {
  name?: string;
  username: string;
  platform: 'github' | 'gitlab' | 'twitter';
  avatar?: string;
  bio?: string;
  location?: string;
  company?: string;
  website?: string;
  followers: number;
  following: number;
  publicRepos: number;
  contributions: number;
  joinDate: number;
  verified: boolean;
  reputation: number;
}

export interface Repository {
  name: string;
  fullName: string;
  description?: string;
  url: string;
  language: string;
  stars: number;
  forks: number;
  watchers: number;
  issues: number;
  pullRequests: number;
  commits: number;
  contributors: number;
  lastCommit: number;
  created: number;
  updated: number;
  license?: string;
  topics: string[];
  archived: boolean;
  fork: boolean;
}

export interface DeveloperActivity {
  totalCommits: number;
  commitsLast30Days: number;
  activeDays: number;
  averageCommitsPerDay: number;
  languages: Record<string, number>;
  lastActivity: number;
  consistency: number;
}

export interface DeveloperReputation {
  score: number;
  level: 'unknown' | 'beginner' | 'intermediate' | 'advanced' | 'expert';
  factors: {
    experience: number;
    activity: number;
    quality: number;
    community: number;
  };
  badges: string[];
  achievements: string[];
}

// User Types
export interface User {
  id: string;
  email: string;
  username: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  bio?: string;
  location?: string;
  website?: string;
  twitter?: string;
  github?: string;
  
  // Account Status
  verified: boolean;
  premium: boolean;
  role: 'user' | 'admin' | 'moderator';
  status: 'active' | 'suspended' | 'banned';
  
  // Preferences
  preferences: UserPreferences;
  
  // Timestamps
  createdAt: number;
  updatedAt: number;
  lastLoginAt?: number;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  timezone: string;
  currency: string;
  notifications: NotificationSettings;
  privacy: PrivacySettings;
}

export interface NotificationSettings {
  email: boolean;
  push: boolean;
  sms: boolean;
  alerts: {
    riskChanges: boolean;
    priceAlerts: boolean;
    newTokens: boolean;
    weeklyReport: boolean;
  };
}

export interface PrivacySettings {
  profilePublic: boolean;
  portfolioPublic: boolean;
  activityPublic: boolean;
  allowAnalytics: boolean;
}

// Portfolio Types
export interface Portfolio {
  id: string;
  userId: string;
  name: string;
  description?: string;
  isDefault: boolean;
  isPublic: boolean;
  tokens: PortfolioToken[];
  totalValue: number;
  totalChange24h: number;
  totalChangePercent24h: number;
  createdAt: number;
  updatedAt: number;
}

export interface PortfolioToken {
  tokenAddress: string;
  chain: SupportedChain;
  symbol: string;
  name: string;
  amount: string;
  averagePrice: number;
  currentPrice: number;
  value: number;
  change24h: number;
  changePercent24h: number;
  riskScore: number;
  riskLevel: RiskLevel;
  addedAt: number;
  updatedAt: number;
}

// Alert Types
export interface Alert {
  id: string;
  userId: string;
  type: AlertType;
  tokenAddress: string;
  chain: SupportedChain;
  condition: AlertCondition;
  isActive: boolean;
  triggered: boolean;
  triggeredAt?: number;
  createdAt: number;
  updatedAt: number;
}

export type AlertType = 
  | 'price_above'
  | 'price_below'
  | 'risk_increase'
  | 'risk_decrease'
  | 'volume_spike'
  | 'new_holder'
  | 'whale_movement';

export interface AlertCondition {
  threshold: number;
  timeframe?: string;
  percentage?: boolean;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: number;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// WebSocket Types
export interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: number;
}

export interface PriceUpdate {
  tokenAddress: string;
  chain: SupportedChain;
  price: number;
  change24h: number;
  volume24h: number;
  timestamp: number;
}

export interface RiskUpdate {
  tokenAddress: string;
  chain: SupportedChain;
  riskScore: number;
  riskLevel: RiskLevel;
  changes: string[];
  timestamp: number;
}

// Export all types
export * from './api';
export * from './blockchain';
export * from './models';
