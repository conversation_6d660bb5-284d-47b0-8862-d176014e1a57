/**
 * TokenForge Database Models
 * Type definitions for database entities and models
 */

// =============================================================================
// Base Model Types
// =============================================================================

export interface BaseModel {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface TimestampModel {
  createdAt: number;
  updatedAt: number;
}

// =============================================================================
// User Models
// =============================================================================

export interface UserModel extends BaseModel {
  email: string;
  username: string;
  passwordHash: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  bio?: string;
  location?: string;
  website?: string;
  twitter?: string;
  github?: string;
  
  // Account Status
  emailVerified: boolean;
  emailVerificationToken?: string;
  emailVerificationExpires?: Date;
  passwordResetToken?: string;
  passwordResetExpires?: Date;
  
  // Subscription
  subscriptionId?: string;
  subscriptionStatus: 'none' | 'active' | 'canceled' | 'past_due';
  subscriptionPlan: 'free' | 'pro' | 'enterprise';
  subscriptionExpires?: Date;
  
  // Permissions
  role: 'user' | 'admin' | 'moderator';
  status: 'active' | 'suspended' | 'banned';
  permissions: string[];
  
  // Preferences
  preferences: UserPreferencesModel;
  
  // Usage Tracking
  apiRequestsCount: number;
  apiRequestsResetAt: Date;
  lastLoginAt?: Date;
  lastActiveAt?: Date;
}

export interface UserPreferencesModel {
  theme: 'light' | 'dark' | 'system';
  language: string;
  timezone: string;
  currency: string;
  notifications: NotificationSettingsModel;
  privacy: PrivacySettingsModel;
}

export interface NotificationSettingsModel {
  email: boolean;
  push: boolean;
  sms: boolean;
  alerts: {
    riskChanges: boolean;
    priceAlerts: boolean;
    newTokens: boolean;
    weeklyReport: boolean;
    securityAlerts: boolean;
    maintenanceNotices: boolean;
  };
}

export interface PrivacySettingsModel {
  profilePublic: boolean;
  portfolioPublic: boolean;
  activityPublic: boolean;
  allowAnalytics: boolean;
  shareUsageData: boolean;
}

// =============================================================================
// Token Models
// =============================================================================

export interface TokenModel extends BaseModel {
  address: string;
  chain: string;
  name: string;
  symbol: string;
  decimals: number;
  totalSupply: string;
  logoUrl?: string;
  website?: string;
  description?: string;
  tags: string[];
  
  // Contract Info
  isVerified: boolean;
  sourceCode?: string;
  abi?: any;
  compiler?: string;
  
  // Status
  isActive: boolean;
  isScam: boolean;
  isHoneypot: boolean;
  
  // Metrics
  price: number;
  priceChange24h: number;
  volume24h: number;
  marketCap: number;
  circulatingSupply: number;
  maxSupply?: number;
  holders: number;
  transactions24h: number;
  
  // Risk Assessment
  riskScore: number;
  riskLevel: string;
  riskFactors: any;
  lastRiskUpdate: Date;
  
  // Analysis Data
  contractAnalysis?: any;
  socialAnalysis?: any;
  developerAnalysis?: any;
  
  // Timestamps
  firstSeenAt: Date;
  lastAnalyzedAt: Date;
  lastPriceUpdate: Date;
}

// =============================================================================
// Portfolio Models
// =============================================================================

export interface PortfolioModel extends BaseModel {
  userId: string;
  name: string;
  description?: string;
  isDefault: boolean;
  isPublic: boolean;
  
  // Calculated Fields
  totalValue: number;
  totalChange24h: number;
  totalChangePercent24h: number;
  tokenCount: number;
  
  // Risk Metrics
  overallRiskScore: number;
  overallRiskLevel: string;
  riskDistribution: any;
  
  // Performance
  performance: any;
  
  // Settings
  settings: PortfolioSettingsModel;
}

export interface PortfolioTokenModel extends BaseModel {
  portfolioId: string;
  tokenAddress: string;
  chain: string;
  symbol: string;
  name: string;
  
  // Holdings
  amount: string;
  averagePrice: number;
  totalCost: number;
  
  // Current Values
  currentPrice: number;
  currentValue: number;
  unrealizedPnl: number;
  unrealizedPnlPercent: number;
  
  // Performance
  change24h: number;
  changePercent24h: number;
  
  // Risk
  riskScore: number;
  riskLevel: string;
  
  // Metadata
  notes?: string;
  tags: string[];
  addedAt: Date;
  lastUpdated: Date;
}

export interface PortfolioSettingsModel {
  autoUpdate: boolean;
  updateInterval: number;
  includeInAnalytics: boolean;
  notifications: {
    priceAlerts: boolean;
    riskAlerts: boolean;
    performanceReports: boolean;
  };
}

// =============================================================================
// Alert Models
// =============================================================================

export interface AlertModel extends BaseModel {
  userId: string;
  name: string;
  description?: string;
  type: string;
  
  // Target
  tokenAddress: string;
  chain: string;
  tokenSymbol: string;
  
  // Condition
  condition: AlertConditionModel;
  
  // Status
  isActive: boolean;
  triggered: boolean;
  triggeredAt?: Date;
  triggerCount: number;
  lastTriggerValue?: number;
  
  // Notification Settings
  notificationMethods: string[];
  cooldownPeriod: number;
  maxTriggers?: number;
  
  // Metadata
  tags: string[];
  expiresAt?: Date;
}

export interface AlertConditionModel {
  operator: 'gt' | 'lt' | 'gte' | 'lte' | 'eq' | 'ne';
  threshold: number;
  field: string;
  timeframe?: string;
  percentage?: boolean;
  consecutive?: number;
}

export interface AlertTriggerModel extends BaseModel {
  alertId: string;
  userId: string;
  
  // Trigger Details
  triggerValue: number;
  threshold: number;
  condition: string;
  
  // Context
  tokenAddress: string;
  chain: string;
  tokenSymbol: string;
  
  // Notification
  notificationSent: boolean;
  notificationMethods: string[];
  notificationError?: string;
  
  // Metadata
  metadata: any;
  triggeredAt: Date;
}

// =============================================================================
// Analysis Models
// =============================================================================

export interface RiskAssessmentModel extends BaseModel {
  tokenAddress: string;
  chain: string;
  
  // Overall Assessment
  overallScore: number;
  riskLevel: string;
  confidence: number;
  version: string;
  
  // Risk Factors
  factors: RiskFactorModel[];
  
  // Analysis Components
  contractRisk: any;
  socialRisk: any;
  developerRisk: any;
  liquidityRisk: any;
  marketRisk: any;
  
  // Metadata
  analysisTime: number;
  dataQuality: number;
  lastUpdated: Date;
}

export interface RiskFactorModel {
  name: string;
  description: string;
  category: string;
  severity: string;
  score: number;
  weight: number;
  confidence: number;
  evidence: any;
}

export interface ContractAnalysisModel extends BaseModel {
  tokenAddress: string;
  chain: string;
  
  // Contract Info
  isVerified: boolean;
  sourceCode?: string;
  compiler?: string;
  optimization?: boolean;
  
  // Security Analysis
  securityFlags: any;
  vulnerabilities: any[];
  
  // Function Analysis
  functions: any[];
  events: any[];
  modifiers: any[];
  
  // Ownership
  owner?: string;
  ownershipRenounced: boolean;
  multiSig: boolean;
  
  // Proxy
  isProxy: boolean;
  implementation?: string;
  proxyType?: string;
  
  // Analysis Results
  analysisResults: any;
  analysisTime: number;
  lastAnalyzed: Date;
}

// =============================================================================
// Analytics Models
// =============================================================================

export interface AnalyticsEventModel extends BaseModel {
  userId?: string;
  sessionId: string;
  eventType: string;
  eventName: string;
  
  // Event Data
  properties: any;
  context: any;
  
  // Request Info
  userAgent?: string;
  ipAddress?: string;
  referer?: string;
  
  // Timestamps
  timestamp: Date;
  serverTimestamp: Date;
}

export interface UsageStatsModel extends BaseModel {
  userId: string;
  date: Date;
  
  // API Usage
  apiRequests: number;
  apiErrors: number;
  avgResponseTime: number;
  
  // Feature Usage
  tokensAnalyzed: number;
  portfoliosViewed: number;
  alertsTriggered: number;
  
  // Engagement
  sessionDuration: number;
  pageViews: number;
  uniqueTokens: number;
  
  // Performance
  cacheHitRate: number;
  errorRate: number;
}

// =============================================================================
// System Models
// =============================================================================

export interface AuditLogModel extends BaseModel {
  userId?: string;
  action: string;
  resource: string;
  resourceId?: string;
  
  // Details
  details: any;
  changes?: any;
  
  // Context
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  
  // Result
  success: boolean;
  error?: string;
  
  // Timestamp
  timestamp: Date;
}

export interface SystemConfigModel extends BaseModel {
  key: string;
  value: any;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  description?: string;
  category: string;
  isPublic: boolean;
  isEditable: boolean;
  validationRules?: any;
}

export interface CacheEntryModel {
  key: string;
  value: any;
  ttl: number;
  createdAt: Date;
  expiresAt: Date;
  hitCount: number;
  lastAccessed: Date;
}
