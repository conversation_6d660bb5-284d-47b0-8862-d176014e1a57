Build "TokenForge" - AI-Powered Risk Assessment
Core Enhancement: Integrate AI analysis for scam detection and project viability scoring.
Key Features:

Real-time contract analysis for common scam patterns
Social sentiment analysis across platforms
Developer history and reputation tracking
Automated red flag detection (honeypots, mint functions, etc.)
Risk scoring dashboard for each token

Detailed Requirements Document
Technical Architecture
Blockchain Layer:

Multi-chain support (Ethereum, Polygon, BSC, Solana)
Cross-chain bridge integration
Layer 2 scaling solutions for reduced fees
Smart contract templates with security audits

Backend Infrastructure:

Microservices architecture
Real-time data processing with WebSockets
GraphQL API for flexible data queries
Redis caching for performance
PostgreSQL for transactional data
MongoDB for analytics and logs

Frontend Requirements:

Progressive Web App (PWA) for mobile optimization
React/Next.js with TypeScript
Real-time price charts with TradingView integration
Responsive design with dark/light mode
Accessibility compliance (WCAG 2.1)

Core Features Specification
Token Creation Wizard:

Step-by-step guided process
Template selection (utility, governance, reward)
Tokenomics calculator and visualization
Legal compliance checker by jurisdiction
Automated smart contract deployment

Enhanced Trading Interface:

Advanced order types (limit, stop-loss, take-profit)
Portfolio tracking and P&L analytics
Price alerts and notifications
Social trading indicators
Liquidity depth visualization

Security & Compliance:

KYC/AML integration with third-party providers
Multi-signature wallet support
Hardware wallet compatibility
Regulatory compliance framework
Insurance integration for verified projects

Problems Solved
Current pump.fun Limitations Addressed:

Lack of Due Diligence: Implement mandatory project documentation and verification
High Scam Risk: Add AI-powered scam detection and community moderation
Poor User Experience: Create intuitive interfaces with educational resources
Limited Utility Focus: Require real-world use cases for token approval
Regulatory Concerns: Build compliance framework from day one
Market Manipulation: Implement fair launch mechanisms and whale protection
Limited Social Features: Add comprehensive social trading and community tools

Additional Value Propositions:

Education Hub: Built-in learning resources for crypto newcomers
Incubator Program: Support promising projects with mentorship
Insurance Layer: Optional insurance for verified high-quality projects
Analytics Suite: Comprehensive market analysis and insights
Mobile-First Design: Optimized for smartphone trading

Development Prompts
For Technical Team:
Build a decentralized token launchpad with the following specifications:
- Multi-chain architecture supporting Ethereum and Solana initially
- Smart contract factory pattern for token deployment
- Integration with Uniswap V3 and Jupiter for automated liquidity provision
- Real-time WebSocket connections for price feeds and trading data
- Implement bonding curve mathematics with configurable parameters
- Add reentrancy guards and access controls to all contracts
- Create comprehensive test suite with 90%+ coverage
- Implement circuit breakers for unusual trading activity
for UI/UX Team:
Design a modern, intuitive interface for cryptocurrency token creation and trading:
- Mobile-first responsive design with progressive web app capabilities
- Gamified onboarding process for new users
- Real-time price charts with customizable indicators
- One-click token creation wizard with preview functionality
- Social features including user profiles, following, and leaderboards
- Accessibility features for users with disabilities
- Performance optimization for sub-second load times
- Integration with popular wallet providers (MetaMask, WalletConnect, Phantom)
For Security Team:
Implement comprehensive security measures:
- Smart contract audit framework with automated vulnerability scanning
- Rate limiting and DDoS protection
- Multi-factor authentication for high-value accounts
- Cold storage integration for platform funds
- Real-time monitoring for suspicious activities
- Incident response procedures and emergency stops
- Regular penetration testing schedule
- Bug bounty program integration

My github is https://github.com/HectorTa1989. Show me github readme with some good product names that nobody registered website domain with those names before, system architecture in mermaid syntax, workflow in mermaid syntax, Project structure all in github readme. Then code for each file in the project structure in separate artifacts (each file in 1 block) with exact file path, file name. Write commit message for each file after each file, so I can commit to github. Code using our own algorithms and free APIs is better. Ensure it can be run well on both localhost, netlify, GCP, AWS