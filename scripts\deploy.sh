#!/bin/bash

# =============================================================================
# CryptoSentinel Deployment Script
# =============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Display usage
usage() {
    echo "Usage: $0 [PLATFORM] [OPTIONS]"
    echo ""
    echo "Platforms:"
    echo "  netlify    Deploy to Netlify"
    echo "  aws        Deploy to AWS"
    echo "  gcp        Deploy to Google Cloud Platform"
    echo "  docker     Deploy with Docker"
    echo ""
    echo "Options:"
    echo "  --env      Environment (development, staging, production)"
    echo "  --help     Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 netlify --env production"
    echo "  $0 aws --env staging"
    echo "  $0 docker --env development"
}

# Deploy to Netlify
deploy_netlify() {
    local env=${1:-production}
    
    log_info "Deploying to Netlify (Environment: $env)..."
    
    # Check if Netlify CLI is installed
    if ! command_exists netlify; then
        log_error "Netlify CLI is not installed. Install it with: npm install -g netlify-cli"
        exit 1
    fi
    
    # Build the project
    log_info "Building project for Netlify..."
    export BUILD_TARGET=netlify
    export NODE_ENV=$env
    npm run build:netlify
    
    # Deploy to Netlify
    if [ "$env" = "production" ]; then
        log_info "Deploying to production..."
        netlify deploy --prod --dir=apps/web/out
    else
        log_info "Deploying to preview..."
        netlify deploy --dir=apps/web/out
    fi
    
    log_success "Netlify deployment completed!"
}

# Deploy to AWS
deploy_aws() {
    local env=${1:-production}
    
    log_info "Deploying to AWS (Environment: $env)..."
    
    # Check if AWS CLI is installed
    if ! command_exists aws; then
        log_error "AWS CLI is not installed. Please install it first."
        exit 1
    fi
    
    # Check if CDK is installed
    if ! command_exists cdk; then
        log_error "AWS CDK is not installed. Install it with: npm install -g aws-cdk"
        exit 1
    fi
    
    # Build the project
    log_info "Building project for AWS..."
    export BUILD_TARGET=aws
    export NODE_ENV=$env
    npm run build:aws
    
    # Deploy infrastructure
    log_info "Deploying infrastructure with CDK..."
    cd deployment/aws
    cdk deploy --all --require-approval never
    cd ../..
    
    log_success "AWS deployment completed!"
}

# Deploy to Google Cloud Platform
deploy_gcp() {
    local env=${1:-production}
    
    log_info "Deploying to GCP (Environment: $env)..."
    
    # Check if gcloud CLI is installed
    if ! command_exists gcloud; then
        log_error "Google Cloud CLI is not installed. Please install it first."
        exit 1
    fi
    
    # Build the project
    log_info "Building project for GCP..."
    export BUILD_TARGET=gcp
    export NODE_ENV=$env
    npm run build:gcp
    
    # Deploy to Cloud Run
    log_info "Deploying to Cloud Run..."
    
    # Deploy web service
    gcloud run deploy cryptosentinel-web \
        --source apps/web \
        --platform managed \
        --region us-central1 \
        --allow-unauthenticated \
        --set-env-vars NODE_ENV=$env
    
    # Deploy API service
    gcloud run deploy cryptosentinel-api \
        --source apps/api \
        --platform managed \
        --region us-central1 \
        --allow-unauthenticated \
        --set-env-vars NODE_ENV=$env
    
    # Deploy AI service
    gcloud run deploy cryptosentinel-ai \
        --source apps/ai-service \
        --platform managed \
        --region us-central1 \
        --allow-unauthenticated \
        --set-env-vars NODE_ENV=$env
    
    log_success "GCP deployment completed!"
}

# Deploy with Docker
deploy_docker() {
    local env=${1:-development}
    
    log_info "Deploying with Docker (Environment: $env)..."
    
    # Check if Docker is installed
    if ! command_exists docker; then
        log_error "Docker is not installed. Please install it first."
        exit 1
    fi
    
    # Check if Docker Compose is installed
    if ! command_exists docker-compose; then
        log_error "Docker Compose is not installed. Please install it first."
        exit 1
    fi
    
    # Set environment
    export NODE_ENV=$env
    
    # Build and start services
    log_info "Building Docker images..."
    docker-compose -f deployment/docker/docker-compose.yml build
    
    log_info "Starting services..."
    docker-compose -f deployment/docker/docker-compose.yml up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 30
    
    # Check service health
    log_info "Checking service health..."
    docker-compose -f deployment/docker/docker-compose.yml ps
    
    log_success "Docker deployment completed!"
    log_info "Services are available at:"
    log_info "  Frontend: http://localhost:3000"
    log_info "  API: http://localhost:3001"
    log_info "  AI Service: http://localhost:8000"
    log_info "  Monitoring: http://localhost:9090 (Prometheus), http://localhost:3001 (Grafana)"
}

# Pre-deployment checks
pre_deployment_checks() {
    log_info "Running pre-deployment checks..."
    
    # Check if we're in the right directory
    if [ ! -f "package.json" ] || [ ! -f "turbo.json" ]; then
        log_error "Please run this script from the project root directory"
        exit 1
    fi
    
    # Run tests
    log_info "Running tests..."
    npm run test
    
    # Run linting
    log_info "Running linting..."
    npm run lint
    
    # Type checking
    log_info "Running type checking..."
    npm run type-check
    
    log_success "Pre-deployment checks passed!"
}

# Post-deployment tasks
post_deployment_tasks() {
    local platform=$1
    local env=$2
    
    log_info "Running post-deployment tasks..."
    
    # Send deployment notification (if configured)
    if [ -n "$SLACK_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"🚀 CryptoSentinel deployed to $platform ($env environment)\"}" \
            "$SLACK_WEBHOOK_URL"
    fi
    
    # Update deployment status
    echo "$(date): Deployed to $platform ($env)" >> deployment.log
    
    log_success "Post-deployment tasks completed!"
}

# Main function
main() {
    local platform=$1
    local env="production"
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --env)
                env="$2"
                shift 2
                ;;
            --help)
                usage
                exit 0
                ;;
            netlify|aws|gcp|docker)
                platform="$1"
                shift
                ;;
            *)
                log_error "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done
    
    # Check if platform is specified
    if [ -z "$platform" ]; then
        log_error "Platform not specified"
        usage
        exit 1
    fi
    
    echo "=============================================="
    echo "🚀 CryptoSentinel Deployment"
    echo "=============================================="
    echo "Platform: $platform"
    echo "Environment: $env"
    echo ""
    
    # Run pre-deployment checks
    pre_deployment_checks
    echo ""
    
    # Deploy based on platform
    case $platform in
        netlify)
            deploy_netlify "$env"
            ;;
        aws)
            deploy_aws "$env"
            ;;
        gcp)
            deploy_gcp "$env"
            ;;
        docker)
            deploy_docker "$env"
            ;;
        *)
            log_error "Unsupported platform: $platform"
            usage
            exit 1
            ;;
    esac
    
    echo ""
    
    # Run post-deployment tasks
    post_deployment_tasks "$platform" "$env"
    
    echo ""
    echo "=============================================="
    log_success "Deployment completed successfully! 🎉"
    echo "=============================================="
}

# Run main function with all arguments
main "$@"
