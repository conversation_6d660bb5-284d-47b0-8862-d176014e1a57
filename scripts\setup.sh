#!/bin/bash

# =============================================================================
# CryptoSentinel Development Setup Script
# =============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check system requirements
check_requirements() {
    log_info "Checking system requirements..."
    
    # Check Node.js
    if command_exists node; then
        NODE_VERSION=$(node --version | cut -d'v' -f2)
        REQUIRED_NODE="18.0.0"
        if [ "$(printf '%s\n' "$REQUIRED_NODE" "$NODE_VERSION" | sort -V | head -n1)" = "$REQUIRED_NODE" ]; then
            log_success "Node.js $NODE_VERSION is installed"
        else
            log_error "Node.js $REQUIRED_NODE or higher is required. Current: $NODE_VERSION"
            exit 1
        fi
    else
        log_error "Node.js is not installed. Please install Node.js 18 or higher."
        exit 1
    fi
    
    # Check npm
    if command_exists npm; then
        NPM_VERSION=$(npm --version)
        log_success "npm $NPM_VERSION is installed"
    else
        log_error "npm is not installed"
        exit 1
    fi
    
    # Check Python (for AI service)
    if command_exists python3; then
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
        log_success "Python $PYTHON_VERSION is installed"
    else
        log_warning "Python 3 is not installed. AI service will not work without it."
    fi
    
    # Check Docker (optional)
    if command_exists docker; then
        DOCKER_VERSION=$(docker --version | cut -d' ' -f3 | cut -d',' -f1)
        log_success "Docker $DOCKER_VERSION is installed"
    else
        log_warning "Docker is not installed. Docker deployment will not be available."
    fi
    
    # Check PostgreSQL (optional)
    if command_exists psql; then
        POSTGRES_VERSION=$(psql --version | cut -d' ' -f3)
        log_success "PostgreSQL $POSTGRES_VERSION is installed"
    else
        log_warning "PostgreSQL is not installed. You'll need to use Docker or install it separately."
    fi
    
    # Check Redis (optional)
    if command_exists redis-cli; then
        log_success "Redis CLI is installed"
    else
        log_warning "Redis is not installed. You'll need to use Docker or install it separately."
    fi
}

# Install dependencies
install_dependencies() {
    log_info "Installing dependencies..."
    
    # Install root dependencies
    log_info "Installing root dependencies..."
    npm install
    
    # Install Python dependencies for AI service
    if command_exists python3 && command_exists pip3; then
        log_info "Installing Python dependencies for AI service..."
        cd apps/ai-service
        if [ -f "requirements.txt" ]; then
            pip3 install -r requirements.txt
        fi
        cd ../..
    fi
    
    log_success "Dependencies installed successfully"
}

# Setup environment files
setup_environment() {
    log_info "Setting up environment files..."
    
    # Copy environment template if .env doesn't exist
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env
            log_success "Created .env file from template"
            log_warning "Please update the .env file with your actual configuration values"
        else
            log_error ".env.example file not found"
        fi
    else
        log_info ".env file already exists"
    fi
    
    # Setup environment for web app
    if [ ! -f "apps/web/.env.local" ]; then
        cat > apps/web/.env.local << EOF
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_AI_SERVICE_URL=http://localhost:8000
NEXT_PUBLIC_BLOCKCHAIN_SERVICE_URL=http://localhost:3002
EOF
        log_success "Created apps/web/.env.local"
    fi
    
    # Setup environment for API
    if [ ! -f "apps/api/.env" ]; then
        cat > apps/api/.env << EOF
NODE_ENV=development
PORT=3001
DATABASE_URL=postgresql://cryptosentinel:password@localhost:5432/cryptosentinel
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
CORS_ORIGIN=http://localhost:3000
EOF
        log_success "Created apps/api/.env"
    fi
    
    # Setup environment for AI service
    if [ ! -f "apps/ai-service/.env" ]; then
        cat > apps/ai-service/.env << EOF
PORT=8000
REDIS_URL=redis://localhost:6379
DATABASE_URL=postgresql://cryptosentinel:password@localhost:5432/cryptosentinel
DEBUG=true
EOF
        log_success "Created apps/ai-service/.env"
    fi
}

# Setup database
setup_database() {
    log_info "Setting up database..."
    
    if command_exists docker; then
        log_info "Starting PostgreSQL and Redis with Docker..."
        docker-compose -f deployment/docker/docker-compose.yml up -d postgres redis
        
        # Wait for services to be ready
        log_info "Waiting for database to be ready..."
        sleep 10
        
        # Run database migrations
        log_info "Running database migrations..."
        cd apps/api
        npm run db:migrate
        cd ../..
        
        log_success "Database setup completed"
    else
        log_warning "Docker not available. Please set up PostgreSQL and Redis manually."
        log_info "PostgreSQL connection: postgresql://cryptosentinel:password@localhost:5432/cryptosentinel"
        log_info "Redis connection: redis://localhost:6379"
    fi
}

# Build packages
build_packages() {
    log_info "Building shared packages..."
    
    # Build types package
    cd packages/types
    npm run build
    cd ../..
    
    # Build shared package
    cd packages/shared
    npm run build
    cd ../..
    
    log_success "Packages built successfully"
}

# Setup Git hooks
setup_git_hooks() {
    log_info "Setting up Git hooks..."
    
    if [ -d ".git" ]; then
        npx husky install
        npx husky add .husky/pre-commit "npx lint-staged"
        npx husky add .husky/pre-push "npm run test"
        log_success "Git hooks setup completed"
    else
        log_warning "Not a Git repository. Skipping Git hooks setup."
    fi
}

# Main setup function
main() {
    echo "=============================================="
    echo "🚀 CryptoSentinel Development Setup"
    echo "=============================================="
    echo ""
    
    check_requirements
    echo ""
    
    install_dependencies
    echo ""
    
    setup_environment
    echo ""
    
    build_packages
    echo ""
    
    setup_database
    echo ""
    
    setup_git_hooks
    echo ""
    
    echo "=============================================="
    log_success "Setup completed successfully! 🎉"
    echo "=============================================="
    echo ""
    echo "Next steps:"
    echo "1. Update your .env files with actual API keys and configuration"
    echo "2. Start the development servers:"
    echo "   npm run dev"
    echo ""
    echo "3. Open your browser and navigate to:"
    echo "   Frontend: http://localhost:3000"
    echo "   API Docs: http://localhost:3001/api-docs"
    echo "   AI Service: http://localhost:8000/docs"
    echo ""
    echo "For more information, check the README.md file."
    echo ""
}

# Run main function
main "$@"
