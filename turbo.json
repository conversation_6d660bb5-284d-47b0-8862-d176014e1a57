{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**", "build/**"]}, "dev": {"cache": false, "persistent": true}, "test": {"dependsOn": ["build"], "outputs": ["coverage/**"]}, "test:unit": {"outputs": ["coverage/**"]}, "test:integration": {"dependsOn": ["build"], "outputs": ["coverage/**"]}, "test:e2e": {"dependsOn": ["build"], "outputs": ["coverage/**", "test-results/**"]}, "lint": {"outputs": []}, "lint:fix": {"outputs": []}, "type-check": {"dependsOn": ["^build"], "outputs": []}, "clean": {"cache": false}}}